---
title: 推論疊加：連續思想鏈的理論視角
source: https://notebooklm.google.com/notebook/cecbe47d-2558-4de6-b70d-3653cbc87620?_gl=1*1lbfs13*_ga*MjgzMTY5ODM2LjE3NDMxMzM5MjY.*_ga_W0LDH41ZCB*MTc0MzEzMzkyNi4xLjAuMTc0MzEzMzkyNi42MC4wLjA.&original_referer=https:%2F%2Fnotebooklm.google%23&pli=1
author:
published:
created: 2025-06-26
description: Use the power of AI for quick summarization and note taking, NotebookLM is your powerful virtual research assistant rooted in information you can trust.
tags:
  - AI/Theory
reference:
  - https://arxiv.org/html/2505.12514v1
---
## Sources

Reasoning by Superposition: A Theoretical Perspective on Chain of Continuous Thought

## Chat

♾️

## 推論疊加：連續思想鏈的理論視角

1 source

這篇研究論文 **深入探討** 了大型語言模型（LLMs）如何透過一種稱為「連續思維鏈（CoT）」的新方法來解決複雜推理問題。 **核心論點在於** ，與傳統的離散思維鏈不同，連續思維鏈能夠在潛在空間中「疊加」多個潛在的搜尋路徑，實現 **隱式並行搜尋** ，顯著提升了圖可達性等任務的效率。作者們 **理論上證明** 了這種兩層變形金剛模型在處理圖問題上的優勢，並透過實驗 **驗證了其效能** ，發現這種疊加表示會自然而然地從訓練中「湧現」。 **研究還討論了** 不同訓練策略對模型探索行為的影響，並 **提供了詳細的數學推導** 來支持其理論建構。

## Studio

## Audio Overview

🎉 Create an Audio Overview in more languages! [Learn more](https://support.google.com/notebooklm/answer/********?ref_topic=********&sjid=3352195109469844277-NC)

## Notes

Google apps

Google Account

Ray Sheng

<EMAIL>