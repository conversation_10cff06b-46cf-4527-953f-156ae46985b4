# Virtual Environment Management Rules for AI

## Core Principle
Always detect the virtual environment management tool being used in the current project before executing any Python-related commands. Use the appropriate tool-specific commands based on the detected environment.

## Detection Rules

### 1. UV Environment Detection
**Indicators:**
- Presence of `uv.lock` file in project root
- Presence of `pyproject.toml` with `[tool.uv]` sections
- `.venv` directory created by uv
- `UV_PROJECT_ENVIRONMENT` environment variable set

**Detection Command:**
```bash
# Check for uv indicators
if [ -f "uv.lock" ] || [ -f "pyproject.toml" ] && grep -q "\[tool\.uv\]" pyproject.toml; then
    echo "UV environment detected"
fi
```

### 2. Conda Environment Detection
**Indicators:**
- Presence of `environment.yml` or `environment.yaml` file
- Presence of `conda-meta/` directory in environment
- `CONDA_DEFAULT_ENV` environment variable set
- `conda list` command works without error
- `.condarc` file exists

**Detection Command:**
```bash
# Check for conda indicators
if [ -f "environment.yml" ] || [ -f "environment.yaml" ] || [ -n "$CONDA_DEFAULT_ENV" ]; then
    echo "Conda environment detected"
fi
```

### 3. Standard venv/virtualenv Detection
**Indicators:**
- Presence of `requirements.txt` without uv.lock
- `VIRTUAL_ENV` environment variable set
- `pyvenv.cfg` file in environment directory
- No conda or uv indicators present

## Command Mapping Rules

### Environment Initialization

| Task | UV Command | Conda Command | Standard venv |
|------|------------|---------------|---------------|
| Create new environment | `uv init` | `conda create -n <name> python=<version>` | `python -m venv <name>` |
| Create with specific Python | `uv init --python <version>` | `conda create -n <name> python=<version>` | `python<version> -m venv <name>` |

### Environment Activation

| Task | UV Command | Conda Command | Standard venv |
|------|------------|---------------|---------------|
| Activate environment | `uv shell` or use `uv run` | `conda activate <name>` | `source <path>/bin/activate` (Linux/Mac)<br>`<path>\Scripts\activate` (Windows) |

### Package Management

| Task | UV Command | Conda Command | Standard venv |
|------|------------|---------------|---------------|
| Install package | `uv add <package>` | `conda install <package>` | `pip install <package>` |
| Install from file | `uv sync` | `conda env update -f environment.yml` | `pip install -r requirements.txt` |
| Install dev dependencies | `uv sync --dev` | `conda env update -f environment.yml` | `pip install -r requirements-dev.txt` |
| Remove package | `uv remove <package>` | `conda remove <package>` | `pip uninstall <package>` |
| Update packages | `uv sync --upgrade` | `conda update --all` | `pip install --upgrade -r requirements.txt` |
| List packages | `uv tree` | `conda list` | `pip list` |

### Running Python Code

| Task | UV Command | Conda Command | Standard venv |
|------|------------|---------------|---------------|
| Run Python script | `uv run python <script.py>` | `python <script.py>` (after activation) | `python <script.py>` (after activation) |
| Run with specific command | `uv run <command>` | `<command>` (after activation) | `<command>` (after activation) |

## Implementation Template

```bash
#!/bin/bash

# Function to detect virtual environment type
detect_venv_type() {
    if [ -f "uv.lock" ] || ([ -f "pyproject.toml" ] && grep -q "\[tool\.uv\]" pyproject.toml 2>/dev/null); then
        echo "uv"
    elif [ -f "environment.yml" ] || [ -f "environment.yaml" ] || [ -n "$CONDA_DEFAULT_ENV" ]; then
        echo "conda"
    elif [ -f "requirements.txt" ] || [ -n "$VIRTUAL_ENV" ]; then
        echo "venv"
    else
        echo "unknown"
    fi
}

# Function to run Python with appropriate tool
run_python() {
    local script="$1"
    local venv_type=$(detect_venv_type)
    
    case $venv_type in
        "uv")
            uv run python "$script"
            ;;
        "conda")
            python "$script"
            ;;
        "venv")
            python "$script"
            ;;
        *)
            echo "Warning: Unknown virtual environment type, using system Python"
            python "$script"
            ;;
    esac
}

# Function to install packages with appropriate tool
install_package() {
    local package="$1"
    local venv_type=$(detect_venv_type)
    
    case $venv_type in
        "uv")
            uv add "$package"
            ;;
        "conda")
            conda install "$package"
            ;;
        "venv")
            pip install "$package"
            ;;
        *)
            echo "Error: Cannot determine package manager"
            return 1
            ;;
    esac
}
```

## AI Behavior Rules

1. **Always detect first**: Before executing any Python-related command, run detection logic
2. **Use appropriate commands**: Based on detection results, use the corresponding tool's commands
3. **Provide context**: When switching tools, explain why (e.g., "Detected UV environment, using `uv run` instead of `python`")
4. **Handle edge cases**: If detection is ambiguous, ask user for clarification
5. **Respect existing setup**: Don't change the virtual environment type unless explicitly requested
6. **Cross-platform awareness**: Adjust commands for Windows/Linux/Mac differences when needed

## Error Handling

- If detection fails, ask user to specify the virtual environment type
- If a command fails, suggest alternative approaches or troubleshooting steps
- Always verify environment is properly activated before running commands
- Provide fallback options when primary tool is not available

## Examples

### Example 1: Running a script
```
# AI detects uv.lock file
# AI uses: uv run python main.py
# Instead of: python main.py
```

### Example 2: Installing dependencies
```
# AI detects environment.yml file
# AI uses: conda env update -f environment.yml
# Instead of: pip install -r requirements.txt
```

### Example 3: Adding new package
```
# AI detects UV environment
# AI uses: uv add requests
# Instead of: pip install requests
```

This prompt ensures consistent and appropriate virtual environment management across different project setups.
