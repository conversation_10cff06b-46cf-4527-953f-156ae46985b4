---
title: <PERSON> Best Practices
source:
  - https://www.anthropic.com/engineering/claude-code-best-practices
author:
  - "[[@AnthropicAI]]"
published:
created: 2025-04-20
tags:
  - AI/Claude
Reference:
  - https://docs.anthropic.com/zh-CN/docs/agents-and-tools/claude-code/tutorials
  - https://docs.anthropic.com/en/docs/claude-code/mcp
---
# Memo Test

## upgrade claude code

 list versions
  npm view @anthropic-ai/claude-code versions --json
--sometime still not latest one, you can check belowcd
https://github.com/anthropics/claude-code/blob/main/CHANGELOG.md

install
```
npm cache clean --force
npm install -g @anthropic-ai/claude-code@x.y.z
npm list -g
claude update

```
setup to global installation
```
claude config set --global installMethod global
claude config list --global
```
## initial setup
```
init
logout and login to switch api/max mode

```
## Claude cant startup due to wrong country
- step1 : install proxy in ubuntu wsl
```
sudo apt install privoxy -y
```
- step2 : Configure Privoxy and point to ************:1082 proxy
```bash

  sudo cp /etc/privoxy/config /etc/privoxy/config.backup
  sudo vi config
  cat config
  user-manual /usr/share/doc/privoxy/user-manual
  confdir /etc/privoxy
  logdir /var/log/privoxy
  actionsfile match-all.action # Actions that are applied to all sites and maybe overruled later on.
  actionsfile default.action   # Main actions file
  actionsfile user.action      # User customizations
  filterfile default.filter
  filterfile user.filter      # User customizations
  logfile logfile
  listen-address  0.0.0.0:8118
  listen-address  [::1]:8118
  toggle  1
  enable-remote-toggle  0
  enable-remote-http-toggle  0
  enable-edit-actions 0
  enforce-blocks 0
  buffer-limit 4096
  enable-proxy-authentication-forwarding 0
  forwarded-connect-retries  0
  accept-intercepted-requests 0
  allow-cgi-request-crunching 0
  split-large-forms 0
  keep-alive-timeout 5
  tolerate-pipelining 1
  socket-timeout 300
  
  forward-socks5 / ************:1082 .
  
  permit-access  ***********/24
  permit-access  127.0.0.1
  
  toggle  0
    
```
- step 3: start up
```bash
# Start and enable Privoxy
sudo systemctl start privoxy
sudo systemctl enable privoxy

# Check status
sudo systemctl status privoxy

# Check if it's listening
sudo netstat -tlnp | grep 8118
-- if any issue , we can debug
sudo tail -f /var/log/privoxy/logfile

```
- step 4: test
```bash
curl -x http://************:8118 ipconfig.io/country

```
- step 5: start claude
```bash
export HTTP_PROXY=http://************:8118
export HTTPS_PROXY=http://************:8118
-- maybe it is not required
npm config set proxy http://************:8118
npm config set https-proxy http://************:8118
npm config get proxy
npm config get https-proxy

claude
```
- step 6: once claude auth complete, next we can rollback, in fututre it is not required.
```bash
npm config delete proxy
npm config delete https-proxy
sudo systemctl stop privoxy
sudo systemctl disable privoxy
```
## add github bot
https://docs.anthropic.com/en/docs/claude-code/github-actions
https://github.com/anthropics/claude-code-action
https://github.com/marketplace/actions/claude-code-action-official

https://github.com/settings/tokens

Run Claude Code from your GitHub PRs to respond to reviewer feedback, fix CI errors, and modify code. Simply tag @claude in comments to trigger changes.
```
# install github cli -- Must install latest one
https://github.com/cli/cli/releases/tag/v2.73.0

wget https://github.com/cli/cli/releases/download/v2.73.0/gh_2.73.0_linux_arm64.deb
# Install the downloaded package
sudo dpkg -i gh_2.73.0_linux_arm64.deb
# Fix any dependency issues if they occur
# sudo apt install -f

gh --version
gh auth status

# Install on jethome64 without browser, manually acccess https://github.com/login/device and enter onetime code
# before install , you need to disable/mask snapd and snapd.socket service, relogin
sudo apt install firefox
gh auth login
---------------------
? What account do you want to log into? GitHub.com
? What is your preferred protocol for Git operations? HTTPS
? Authenticate Git with your GitHub credentials? Yes
? How would you like to authenticate GitHub CLI? Login with a web browser

! First copy your one-time code: 7112-BADC
- Press Enter to open github.com in your browser...

Command '/usr/bin/firefox' requires the firefox snap to be installed.
Please install it with:

snap install firefox

! Failed opening a web browser at https://github.com/login/device
  exit status 4
  Please try entering the URL in your browser manually
✓ Authentication complete. Press Enter to continue...
- gh config set -h github.com git_protocol https
✓ Configured git protocol
✓ Logged in as netcaster1
---------------------

## verify
gh auth status
gh repo list --limit 5

# install from claude codee
claude
/install-github-app
+++++++
claude code looks removed above command, then we have to install maually


- Install the Claude GitHub app to your repository: https://github.com/apps/claude
- Add ANTHROPIC_API_KEY to your repository secrets (Learn how to use secrets in GitHub Actions)
= Copy the workflow file from examples/claude.yml into your repository’s .github/workflows/
+++++++


# Test
After completing either the quickstart or manual setup, test the action by tagging @claude in an issue or PR comment!
Step 1: Verify the app is installed
  Go to your repository settings:
  https://github.com/netcaster1/python-toolbox/settings/installations
  You should see "Claude" listed under installed GitHub Apps.
  
  If NOT, INSTALL this app
  https://github.com/apps/claude
  
Step 2: Test with an issue or PR comment
  
  Create a new issue in your repository, or
  Open an existing issue/PR
  Add a comment mentioning @claude like:
  @claude can you help me review this code?
  or
  @claude what improvements can be made to this Python script?
  
  
Step 3: Check for Claude's response
  
  Claude should respond as a comment within a few minutes
  If it doesn't work, check the "Actions" tab for any workflow errors  
 
```

## mcp management in claude codec
claude mcp
claude remove -s user name
claude mcp add -s user omni_server https://omni.ngrok.pro/sse -t sse

 基本语法
claude mcp serve


```
{
  "command": "claude",
  "args": ["mcp", "serve"],
  "env": {}
}

    "claudeCode": {
      "command": "wsl",
      "args": [
        "claude",
        "mcp",
        "serve"
      ],
	  "env": {}
    },	 
```
## change default editor of claude code
export EDITOR=vi

## Enable Backgroud tasks
add this line to your .zshrc or .bashrc (ask CC to do it for you) and you can move long running tasks to the background to keep chatting

export ENABLE_BACKGROUND_TASKS=1

---

## Use Kimi K2 in Claude Code


export ANTHROPIC_AUTH_TOKEN=sk-Zz6es3CSYpTrEI9Od9w8nao36XmQqoWZ1Ajo9Z1P2PLEWnaX

export ANTHROPIC_BASE_URL=https://api.moonshot.ai/anthropic

---

## Claude bridge

https://github.com/badlogic/lemmy.git

### ローカルのOllamaを使用

npm install -g @mariozechner/claude-bridge
claude-bridge google 
claude-bridge openai qwen3:14b --baseURL http://10.244.135.244:8234/v1 --apiKey ollama
claude-bridge google gemini-2.5-pro-preview-05-06 --apiKey AIzaSyA7eaDvusussmwXjF_cdcY71qoe1v5zp-s
claude-bridge google gemini-2.5-pro-preview-06-05 --apiKey AIzaSyA7eaDvusussmwXjF_cdcY71qoe1v5zp-s
  
  ---
## SuperClaude
https://github.com/NomenAK/SuperClaude

[[Claude - Super Claude Usage · NomenAKSuperClaude]]

```
git clone https://github.com/NomenAK/SuperClaude.git
cd SuperClaude
./install.sh                           # Default: ~/.claude/

-- fix a bug
vi install.sh
echo -e "Shared resources: ${GREEN}$shared_files${NC} (expected: 30)"
if [ "$main_files" -ge 4 ] && [ "$command_files" -ge 19 ] && [ "$shared_files" -ge 30 ]; then
echo " Shared resources: sharedfiles/30([ "$shared_files" -lt 30 ] && echo " ❌" || echo " ✓")"




```
## Claude Squad
[[smtg-ai claude-squad Manage multiple AI agents like Claude Code, Aider, Codex, and Amp]]


## Claudia

https://github.com/getAsterisk/claudia/tree/main
git clone https://github.com/getAsterisk/claudia.git
```

# Install via rustup
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# Install bun
curl -fsSL https://bun.sh/install | bash

# Install system dependencies
sudo apt update
sudo apt install -y \
  libwebkit2gtk-4.1-dev \
  libgtk-3-dev \
  libayatana-appindicator3-dev \
  librsvg2-dev \
  patchelf \
  build-essential \
  curl \
  wget \
  file \
  libssl-dev \
  libxdo-dev \
  libsoup-3.0-dev \
  libjavascriptcoregtk-4.1-dev
  
git clone https://github.com/getAsterisk/claudia.git
cd claudia

bun install
# Build the application
bun run tauri build

# The built executable will be in:
# - Linux: src-tauri/target/release/bundle/
# - macOS: src-tauri/target/release/bundle/
# - Windows: src-tauri/target/release/bundle/

# Run the built executable directly
# Linux/macOS
./src-tauri/target/release/claudia

# Windows
./src-tauri/target/release/claudia.exe

The build process creates several artifacts:

Executable: The main Claudia application
Installers (when using tauri build):
.deb package (Linux)
.AppImage (Linux)
.dmg installer (macOS)
.msi installer (Windows)
.exe installer (Windows)
All artifacts are located in src-tauri/target/release/bundle/.

```

After tested.  Issues
1. It will cause my original .claude.json removed. have to manually recover
2. only run dev mode can locate claude
3. can't access from web browser even local b
## SuperDesgin
https://github.com/superdesigndev/superdesign


---
## IDE integrations
https://docs.anthropic.com/en/docs/claude-code/ide-integrations
https://www.cursor.com/ja/downloads

### Install browser (firefox or brave (it is best))
as google-chrome or microsoft-edge both dont have arm64 ubuntu repo.
we will install firefox or brave,  but looks brave is the best
#### firefox
```
-- make sure we dont install snap firefox
sudo snap remove firefox
sudo apt remove firefox
sudo install -d -m 0755 /etc/apt/keyrings

# Create directory for Mozilla signing key
sudo install -d -m 0755 /etc/apt/keyrings

# Import Mozilla APT repo signing key
wget -q https://packages.mozilla.org/apt/repo-signing-key.gpg -O- | sudo tee /etc/apt/keyrings/packages.mozilla.org.asc > /dev/null

# Add Mozilla APT repository
echo "deb [signed-by=/etc/apt/keyrings/packages.mozilla.org.asc] https://packages.mozilla.org/apt mozilla main" | sudo tee -a /etc/apt/sources.list.d/mozilla.list > /dev/null

# Set repository priority
echo '
Package: *
Pin: origin packages.mozilla.org
Pin-Priority: 1000
' | sudo tee /etc/apt/preferences.d/mozilla

# Install Firefox
!!! maybe no need to run in jethome --  sudo apt update
sudo apt install firefox

```

#### brave browser
```
sudo curl -fsSLo /usr/share/keyrings/brave-browser-archive-keyring.gpg https://brave-browser-apt-release.s3.brave.com/brave-browser-archive-keyring.gpg

echo "deb [signed-by=/usr/share/keyrings/brave-browser-archive-keyring.gpg] https://brave-browser-apt-release.s3.brave.com/ stable main" | sudo tee /etc/apt/sources.list.d/brave-browser-release.list

sudo apt update

sudo apt install brave-browser
```




### Cursor IDE
https://www.cursor.com/en/downloads
Cursor-0.50.5-aarch64.AppImage
```
# Make the AppImage executable
chmod +x /databank/install/cursor/Cursor-0.50.5-aarch64.AppImage

# Test run (from the current directory)
cd /databank/install/cursor
./Cursor-0.50.5-aarch64.AppImage


```
But above way, can't update as it is appImage, then we need extract it
```
sudo apt install fuse libfuse2
Above way is NOT GOOD for jetson environment

or

chmod +x Cursor-0.50.5-aarch64.AppImage
./Cursor-0.50.5-aarch64.AppImage --appimage-extract
./squashfs-root/AppRun

-- then we create one script under /usr/bin/cursor
ubuntu@monster-arm:~$ cat /usr/bin/cursor
#!/bin/bash
cd /opt/app/cursor
./squashfs-root/AppRun "$@"

```


 <span style="background:#40a9ff">  the 2nd way , looks download not working. So Temprariy solution</span>

   ```
    1. use monster-arm which FUSE enabled , it will donwload/update AppImage file
      (note , it will use same AppImage name, but content including latest version)
     
ray@jethome64 (base) ➜  cursor env|grep DIS
     DISPLAY=localhost:10.0
     -- knockoracle from jethome or jethome64
     ray@jethome64 (base) ➜  cursor ssh -XYC oraclearm
     ubuntu@monster-arm:~$ env|grep DIS
     DISPLAY=localhost:11.0
     ubuntu@monster-arm:~$ xauth list
     monster-arm/unix:13  MIT-MAGIC-COOKIE-1  16650205f08d607f54019c35bac2a058
     monster-arm/unix:12  MIT-MAGIC-COOKIE-1  5ca5c6f9545cc28ca837bdfdc843b791
     monster-arm/unix:10  MIT-MAGIC-COOKIE-1  b8f2dbf8b31d39bb0f06a1e66929cc9b
     monster-arm/unix:11  MIT-MAGIC-COOKIE-1  df8b80405bc1f25c03dd3bf03135c455
     ubuntu@monster-arm:~$ sudo su - mlusr
     mlusr@monster-arm:~$ env|grep DIS
     mlusr@monster-arm:~$ export DISPLAY=localhost:11.0
     mlusr@monster-arm:~$ xauth add monster-arm/unix:11  MIT-MAGIC-COOKIE-1  df8b80405bc1f25c03dd3bf03135c455
     mlusr@monster-arm:~$ xclock
     mlusr@monster-arm:~$ cursor
     -- it will start upgrade but to same app image 
     [main 2025-06-10T11:38:21.223Z] update#quitAndInstall(): starting updater with command /tmp/.mount_cursornBghbe/usr/share/cursor/resources/appimageupdatetool.AppImage -u "zsync|https://downloads.cursor.com/production/53b99ce608cba35127ae3a050c1738a959750865/linux/arm64/Cursor-1.0.0-aarch64.AppImage.zsync" -O "/databank/app/cursor/Cursor-0.50.5-aarch64.AppImage"; /databank/app/cursor/Cursor-0.50.5-aarch64.AppImage &
     Checking for updates...
     zsync2: Downloading from https://downloads.cursor.com/production/53b99ce608cba35127ae3a050c1738a959750865/linux/arm64/Cursor-1.0.0-aarch64.AppImage
     Update successful. Updated existing file /databank/app/cursor/Cursor-0.50.5-aarch64.AppImage
     
           
    1. download and upload into jethome
    2. appimage-extract
    3. run 
    4. create fast run script
       [23:12:05]ray@jethome64 (base) ➜  ~ cat /usr/bin/cursor
       #!/usr/bin/zsh
       cd /opt/app/cursor
       
       nohup ./squashfs-root/AppRun > /dev/null 2>&1 & echo $!
       
   ```
   
   
#### Install extension manually

```
-- start cursor
ctrl+shift+p
extensions: Install from VSIX ..

-- VSIX file location
/home/<USER>/.nvm/versions/node/v22.11.0/lib/node_modules/@anthropic-ai/claude-code/vendor
```



### VS code IDE
1. download vs code
https://code.visualstudio.com/download#
2. install vs code
 sudo dpkg -i code_1.100.2-1747260559_arm64.deb
 code --version
3. lnstall claude code extension in vs code
   start vs code
   open integrated terminal inside vscode
   type claude 
   done~!
-- Issue if during installation
```
sudo apt update
sudo apt-get install -f
sudo apt-get install libatk-bridge2.0-0 libatk1.0-0 libatspi2.0-0     libgtk-3-0 libxcomposite1 libxdamage1 xdg-utils
sudo dpkg -i code_1.100.2-1747260559_arm64.deb

```





#### get device-code authentication working

##### Install
GitHub Copilot (by GitHub)
GitHub Pull Requests and Issues (by GitHub)

##### setup environmenmt
export VSCODE_GITHUB_AUTH_METHOD=device-code
export GITHUB_CODESPACES_AUTH=1
export VSCODE_DEVICE_CODE_AUTH=1
export GITHUB_TOKEN=$(gh auth token)
export GITHUB_USER=netcaster1
export VSCODE_GITHUB_ACCOUNT=netcaster1
export VSCODE_GITHUB_LOGIN_ACCOUNT=netcaster1

##### make github device code appear
###### 1. Get your gh token
TOKEN=$(gh auth token)

###### 2. Create the auth file VS Code expects
mkdir -p ~/.vscode-server/data/User/workspaceStorage/
cat > ~/.vscode-server/data/User/workspaceStorage/github-auth.json << EOF
{
  "github.com": {
    "accessToken": "$(gh auth token)",
    "account": {
      "id": "$(gh api user --jq .id)",
      "label": "$(gh api user --jq .login)"
    },
    "scopes": ["user:email", "read:user", "repo"]
  }
}
EOF

###### 3. Also create in the globalStorage location
mkdir -p ~/.vscode-server/data/User/globalStorage/vscode.github-authentication/
cp ~/.vscode-server/data/User/workspaceStorage/github-auth.json \
   ~/.vscode-server/data/User/globalStorage/vscode.github-authentication/

mkdir -p ~/.vscode-server/data/User/globalStorage/github.copilot
cp -rp ../vscode.github-authentication/github-auth.json .


##### Make github using correct user:netcaster1 to start authentication/
and make github copilot working
###### start terminal and below will do from this terminal
```
# Get your GitHub username and ID
GH_USER=$(gh api user --jq .login)
GH_ID=$(gh api user --jq .id)
GH_TOKEN=$(gh auth token)

echo "Username: $GH_USER"
echo "User ID: $GH_ID"

# Create the accounts configuration
mkdir -p ~/.vscode-server/data/User/globalStorage/vscode.github-authentication/

cat > ~/.vscode-server/data/User/globalStorage/vscode.github-authentication/accounts.json << EOF
[
  {
    "id": "$GH_ID",
    "label": "$GH_USER",
    "session": {
      "id": "github-session-$GH_ID",
      "accessToken": "$GH_TOKEN",
      "account": {
        "label": "$GH_USER",
        "id": "$GH_ID"
      },
      "scopes": ["read:user", "repo", "user:email", "workflow"]
    }
  }
]
EOF


# Create session storage
cat > ~/.vscode-server/data/User/globalStorage/vscode.github-authentication/sessions.json << EOF
{
  "github": [
    {
      "id": "github-session-$GH_ID",
      "accessToken": "$GH_TOKEN",
      "account": {
        "label": "$GH_USER",
        "id": "$GH_ID"
      },
      "scopes": ["read:user", "repo", "user:email", "workflow"]
    }
  ]
}
EOF

# Update VS Code settings to use specific account
cat > ~/.vscode-server/data/Machine/settings.json << EOF
{
  "github.copilot.enable": {
    "*": true
  },
  "github.gitAuthentication.preferredAccountId": "$GH_ID",
  "github.accounts": ["$GH_USER"],
  "github.copilot.advanced": {
    "debug.useNodeFetcher": true
  }
}
EOF

cat > ~/.vscode-server/data/User/globalStorage/github.vscode-pull-request-github/auth.json << EOF
{
  "sessions": [
    {
      "id": "$GH_ID",
      "accessToken": "$GH_TOKEN",
      "account": {
        "label": "$GH_USER",
        "id": "$GH_ID"
      },
      "scopes": ["read:user", "repo", "user:email", "workflow", "write:discussion", "read:org"]
    }
  ]
}
EOF

#setup ~/.vscode-server/data/Machine/settings.json 
{
  "github.copilot.enable": {
    "*": true
  },
  "github.gitAuthentication.preferredAccountId": "********",
  "github.accounts": ["netcaster1"],
  "github.copilot.advanced": {
    "debug.useNodeFetcher": true
  },

  "githubPullRequests.logLevel": "debug",
  "githubPullRequests.remotes": ["origin", "upstream"],
  "github.preferredAccountId": "********",
  "github.branchProtection": true
}

# Create extension-specific config
cat > ~/.vscode-server/data/User/globalStorage/github.vscode-pull-request-github/config.json << EOF
{
  "github.com": {
    "token": "$GH_TOKEN",
    "username": "$GH_USER",
    "authenticated": true
  }
}
EOF

# Create a script to store in secret storage
cat > ~/.vscode-server/store-auth.js << 'EOF'
const cp = require('child_process');
const token = cp.execSync('gh auth token').toString().trim();

// Store in VS Code secrets
const key = 'github.auth';
const value = JSON.stringify({
  'netcaster1': {
    accessToken: token,
    scopes: ['read:user', 'repo', 'user:email', 'workflow']
  }
});

// This would need to be run inside VS Code context
console.log(`
Run this in VS Code Developer Console:
await context.secrets.store('${key}', '${value}');
`);
EOF

chmod 600 ~/.vscode-server/data/User/globalStorage/vscode.github-authentication/*
git config --global user.name "netcaster1"
git config --list

pkill -f code

code

go to settings -> github -> change 

    "github.copilot.advanced": {
        
        "authProvider": "github"
    },
    "github.gitAuthentication": true	

```   


<span style="background:#ff4d4f">Perhaps only do above these last steps change in setting .json is enough</span>

### X Windows

#### MobaXterm
but can't move window
<span style="background:#affad1">Change X11 server display mode to below</span>
![[Pasted image 20250524211451.png]]


[20:46:13]ray@jethome64 (base) ➜  ~ env|grep DIS
DISPLAY=localhost:11.0
(mlearn) [20:48:36]ray@jethome64 ➜  ai  xlsclients
jethome64  code
(mlearn) [20:51:05]ray@jethome64 ➜  ai xdpyinfo | head
name of display:    localhost:11.0
version number:    11.0
vendor string:    Moba/X
vendor release number:    ********
maximum request size:  ******** bytes
motion buffer size:  256
bitmap unit, bit order, padding:    32, LSBFirst, 32
image byte order:    LSBFirst
number of supported pixmap formats:    7
supported pixmap formats:



#### X410
```


https://x410.dev/comparing-microsoft-store-and-standalone-versions-of-x410/
https://account.microsoft.com/services?refd=account.microsoft.com

```
Windows Side
1. Installation
   https://x410.dev/
2. start X410
3. Firewall
   - 以管理员身份运行PowerShell
New-NetFirewallRule -DisplayName "X410" -Direction Inbound -Program "C:\Program Files\WindowsApps\ChoungNetworksUS.X410S_4.1.0.0_x64__vvzc8y2tzcnsr\X410\X410.exe" -Action Allow
   
4. Setup DISPLAY
   set DISPLAY=localhost:0.0
   or in Powershell 
   $env:DISPLAY = "localhost:0.0"
5. Connect to server
   -  Not working ssh -X ray@************
   ssh -X -Y -C ray@************
6. Run your x windows app





============================
Server Side
1. Install if not existing, maybe it is not required
   sudo apt install -y openssh-server xauth x11-apps
2. setup sshd_config
   sudo nano /etc/ssh/sshd_config
   
   - 确保以下选项启用
   X11Forwarding yes
   X11DisplayOffset 10
   X11UseLocalhost yes
   PrintMotd no
   PrintLastLog yes
   TCPKeepAlive yes
3. restart ssh
   sudo systemctl restart ssh
   sudo systemctl restart sshd
   
#### NoMachine
https://nomachine.com/
But heavy

## Claude code as MCP server
npx -y supergateway@latest --port 8525 --stdio "claude mcp serve"
then setup ngrok.yaml
```
  claudecode:
    addr: 8525
    proto: http
    host_header: ollama.jethome64
```

---

[Engineering at Anthropic Anthropic 的工程设计](https://www.anthropic.com/engineering)

We recently [released Claude Code](https://www.anthropic.com/news/claude-3-7-sonnet), a command line tool for agentic coding. Developed as a research project, Claude Code gives Anthropic engineers and researchers a more native way to integrate Claude into their coding workflows.  
我们最近 [发布了 Claude Code](https://www.anthropic.com/news/claude-3-7-sonnet) ，这是一个用于代理编码的命令行工具。Claude Code 作为一个研究项目开发，为 Anthropic 工程师和研究人员提供了一种更原生的方式，将 Claude 集成到他们的编码工作流程中。

Claude Code is intentionally low-level and unopinionated, providing close to raw model access without forcing specific workflows. This design philosophy creates a flexible, customizable, scriptable, and safe power tool. While powerful, this flexibility presents a learning curve for engineers new to agentic coding tools—at least until they develop their own best practices.  
Claude Code 故意保持低级和无主见，提供接近原始模型的访问，而无需强制使用特定的工作流程。这种设计理念创建了一个灵活、可自定义、可编写脚本且安全的 Power Tool。虽然功能强大，但这种灵活性为刚接触代理编码工具的工程师提供了一个学习曲线，至少在他们开发自己的最佳实践之前是这样。

This post outlines general patterns that have proven effective, both for Anthropic's internal teams and for external engineers using Claude Code across various codebases, languages, and environments. Nothing in this list is set in stone nor universally applicable; consider these suggestions as starting points. We encourage you to experiment and find what works best for you!  
这篇文章概述了已被证明有效的一般模式，无论是对于 Anthropic 的内部团队，还是对于在各种代码库、语言和环境中使用 Claude Code 的外部工程师。此列表中的任何内容都不是一成不变的，也不是普遍适用的;将这些建议视为起点。我们鼓励您尝试并找到最适合您的方法！

*Looking for more detailed information? Our comprehensive documentation at [claude.ai/code](https://claude.ai/code)* *covers all the features mentioned in this post and provides additional examples, implementation details, and advanced techniques.*  
*寻找更多详细信息？我们在 [claude.ai/code](https://claude.ai/code) 上的综合文档* *涵盖了本文中提到的所有功能，并提供了其他示例、实现细节和高级技术。*

# 1\. Customize your setup 1. 自定义您的设置

Claude Code is an agentic coding assistant that automatically pulls context into prompts. This context gathering consumes time and tokens, but you can optimize it through environment tuning.  
Claude Code 是一个代理编码助手，可自动将上下文提取到提示中。这种上下文收集会消耗时间和令牌，但您可以通过环境优化来优化它。

## a. Create CLAUDE.md files一个。创建 CLAUDE.md 文件

CLAUDE.md is a special file that Claude automatically pulls into context when starting a conversation. This makes it an ideal place for documenting:  
CLAUDE.md 是 Claude 在开始对话时自动提取到上下文中的特殊文件。这使它成为记录以下内容的理想场所：

- Common bash commands 常见的 bash 命令
- Core files and utility functions  
	核心文件和实用程序函数
- Code style guidelines 代码样式准则
- Testing instructions 测试说明
- Repository etiquette (e.g., branch naming, merge vs. rebase, etc.)  
	仓库礼仪（例如，分支命名、merge 与 rebase 等）
- Developer environment setup (e.g., pyenv use, which compilers work)  
	开发人员环境设置（例如，pyenv use，哪些编译器可以工作）
- Any unexpected behaviors or warnings particular to the project  
	特定于项目的任何意外行为或警告
- Other information you want Claude to remember  
	您希望 Claude 记住的其他信息

There’s no required format for CLAUDE.md files. We recommend keeping them concise and human-readable. For example:  
CLAUDE.md 文件没有必需的格式。我们建议保持它们简洁易读。例如：

```
# Bash commands
- npm run build: Build the project
- npm run typecheck: Run the typechecker

# Code style
- Use ES modules (import/export) syntax, not CommonJS (require)
- Destructure imports when possible (eg. import { foo } from 'bar')

# Workflow
- Be sure to typecheck when you’re done making a series of code changes
- Prefer running single tests, and not the whole test suite, for performance
```

You can place CLAUDE.md files in several locations:  
您可以将 CLAUDE.md 文件放置在多个位置：

- **The root of your repo**, or wherever you run *claude* from (the most common usage). Name it CLAUDE.md and check it into git so that you can share it across sessions and with your team (recommended), or name it CLAUDE.local.md and.gitignore it  
	**存储库的根目录** ，或者您运行 *claude* 的任何位置（最常见的用法）。将其命名为 CLAUDE.md 并将其签入 git，以便您可以在会话之间和与您的团队共享它（推荐），或者将其命名为 CLAUDE.local.md 并.gitignore 它
- **Any parent of the directory** where you run *claude*. This is most useful for monorepos, where you might run *claude* from *root/foo*, and have CLAUDE.md files in both *root/CLAUDE.md* and *root/foo/CLAUDE.md*. Both of these will be pulled into context automatically  
	运行 *claude* **的目录的任何父级** 。这对于 monorepo 最有用，因为您可以从 *root/foo* 运行 *claude* ，并在 *root/CLAUDE.md* 和 *root/foo/CLAUDE.md* 中都有 CLAUDE.md 文件。这两者都会自动拉入上下文
- **Any child of the directory** where you run *claude*. This is the inverse of the above, and in this case, Claude will pull in CLAUDE.md files on demand when you work with files in child directories  
	运行 *claude* **的目录的任何子目录** 。这与上述情况相反，在这种情况下，当您处理子目录中的文件时，Claude 将按需提取 CLAUDE.md 个文件
- **Your home folder** (*~/.claude/CLAUDE.md*), which applies it to all your *claude* sessions  
	**您的主文件夹** （ *~/.claude/CLAUDE.md* ），将其应用于您的所有 *claude* 会话

When you run the /init command, Claude will automatically generate a CLAUDE.md for you.  
当您运行 /init 命令时，Claude 将为您自动生成一个 CLAUDE.md。

## b. Tune your CLAUDE.md filesb.优化 CLAUDE.md 文件

Your CLAUDE.md files become part of Claude’s prompts, so they should be refined like any frequently used prompt. A common mistake is adding extensive content without iterating on its effectiveness. Take time to experiment and determine what produces the best instruction following from the model.  
您的 CLAUDE.md 文件将成为 Claude 提示的一部分，因此应像任何常用提示一样对其进行优化。一个常见的错误是添加大量内容而不迭代其有效性。花点时间试验并确定什么能从模型中产生最佳指令。

You can add content to your CLAUDE.md manually or press the # key to give Claude an instruction that it will automatically incorporate into the relevant CLAUDE.md. Many engineers use # frequently to document commands, files, and style guidelines while coding, then include CLAUDE.md changes in commits so team members benefit as well.  
您可以手动将内容添加到 CLAUDE.md 中，也可以按 # 键向 Claude 发出指令，该指令将自动合并到相关 CLAUDE.md 中。许多工程师在编码时经常使用 # 来记录命令、文件和样式指南，然后在提交中包含 CLAUDE.md 更改，以便团队成员也能受益。

At Anthropic, we occasionally run CLAUDE.md files through the [prompt improver](https://docs.anthropic.com/en/docs/build-with-claude/prompt-engineering/prompt-improver) and often tune instructions (e.g. adding emphasis with "IMPORTANT" or "YOU MUST") to improve adherence.  
在 Anthropic，我们偶尔会通过 [提示改进器](https://docs.anthropic.com/en/docs/build-with-claude/prompt-engineering/prompt-improver) 运行 CLAUDE.md 文件，并经常调整说明（例如，用“IMPORTANT”或“YOU MUST”添加强调）以提高依从性。

![Claude Code tool allowlist](https://www.anthropic.com/_next/image?url=https%3A%2F%2Fwww-cdn.anthropic.com%2Fimages%2F4zrzovbb%2Fwebsite%2F6961243cc6409e41ba93895faded4f4bc1772366-1600x1231.png&w=3840&q=75)

Claude Code tool allowlist

## c. Curate Claude's list of allowed toolsc. 策划 Claude 允许的工具列表

By default, Claude Code requests permission for any action that might modify your system: file writes, many bash commands, MCP tools, etc. We designed Claude Code with this deliberately conservative approach to prioritize safety. You can customize the allowlist to permit additional tools that you know are safe, or to allow potentially unsafe tools that are easy to undo (e.g., file editing, *git commit*).  
默认情况下，Claude Code 请求对可能修改系统的任何作的权限：文件写入、许多 bash 命令、MCP 工具等。我们设计 Claude Code 时采用了这种刻意保守的方法，以优先考虑安全。您可以自定义允许列表以允许使用您知道安全的其他工具，或允许可能不安全的易于撤消的工具（例如，文件编辑、 *git commit* ）。

There are four ways to manage allowed tools:  
有四种方法可以管理允许的工具：

- **Select "Always allow"** when prompted during a session.  
	在会话期间出现提示时 **选择 “Always allow”（始终允许）。**
- **Use the */allowed-tools* command** after starting Claude Code to add or remove tools from the allowlist. For example, you can add *Edit* to always allow file edits, *Bash(git commit:\*)* to allow git commits, or *mcp\_\_puppeteer\_\_puppeteer\_navigate* to allow navigating with the Puppeteer MCP server.  
	启动 Claude Code 后 **，使用 */allowed-tools* 命令** 在允许列表中添加或删除工具。例如，您可以添加 *编辑* 以始终允许文件编辑，添加 *Bash（git commit：\*）* 以允许 git 提交，或添加 *mcp\_\_puppeteer\_\_puppeteer\_navigate* 以允许使用 Puppeteer MCP 服务器进行导航。
- **Manually edit** your *.claude/settings.json* or *~/.claude.json* (we recommend checking the former into source control to share with your team)*.*  
	**手动编辑** *.claude/settings.json* 或 *~/.claude.json* （我们建议将前者签入源代码控制中，以便与您的团队共享 *）。*
- **Use the *\--allowedTools* CLI flag** for session-specific permissions.  
	**将 *\--allowedTools* CLI 标志用于** 特定于会话的权限。

## d. If using GitHub, install the gh CLId. 如果使用 GitHub，请安装 gh CLI

Claude knows how to use the *gh* CLI to interact with GitHub for creating issues, opening pull requests, reading comments, and more. Without *gh* installed, Claude can still use the GitHub API or MCP server if you have those installed.  
Claude 知道如何使用 *gh* CLI 与 GitHub 交互，以创建问题、打开拉取请求、阅读评论等。如果未安装 gh，Claude 仍然可以使用 GitHub API 或 MCP 服务器（如果已安装）。

Claude has access to your shell environment, where you can build up sets of convenience scripts and functions for it just like you would for yourself. It can also leverage more complex tools through MCP and REST APIs.  
Claude 可以访问您的 shell 环境，您可以在其中为其构建一组方便的脚本和函数，就像您自己一样。它还可以通过 MCP 和 REST API 利用更复杂的工具。

### a. Use Claude with bash toolsa. 将 Claude 与 bash 工具结合使用

Claude Code inherits your bash environment, giving it access to all your tools. While Claude knows common utilities like unix tools and *gh*, it won't know about your custom bash tools without instructions:  
Claude Code 继承了您的 bash 环境，使其能够访问您的所有工具。虽然 Claude 知道 unix 工具和 *gh* 等常用工具，但如果没有说明，它不会知道你的自定义 bash 工具：

1. Tell Claude the tool name with usage examples  
	用使用示例告诉 Claude 工具名称
2. Tell Claude to run *\--help* to see tool documentation  
	告诉 Claude 运行 *\--help* 查看工具文档
3. Document frequently used tools in CLAUDE.md  
	在 CLAUDE.md 中记录常用工具

### b. Use Claude with MCPb. 将 Claude 与 MCP 结合使用

Claude Code functions as both an MCP server and client. As a client, it can connect to any number of MCP servers to access their tools in three ways:  
Claude Code 既是 MCP 服务器，也是客户端。作为客户端，它可以连接到任意数量的 MCP 服务器，以三种方式访问其工具：

- **In project config** (available when running Claude Code in that directory)  
	**在 project config 中** （在该目录中运行 Claude Code 时可用）
- **In global config** (available in all projects)  
	**在全局配置中** （在所有项目中都可用）
- **In a checked-in *.mcp.json* file** (available to anyone working in your codebase). For example, you can add Puppeteer and Sentry servers to your *.mcp.json*, so that every engineer working on your repo can use these out of the box.  
	**在签入的 *.mcp.json* 文件中** （适用于在您的代码库中工作的任何人）。例如，您可以将 Puppeteer 和 Sentry 服务器添加到您的 *.mcp.json* ，以便处理您的存储库的每个工程师都可以开箱即用地使用它们。

When working with MCP, it can also be helpful to launch Claude with the *\--mcp-debug* flag to help identify configuration issues.  
使用 MCP 时，使用 *\--mcp-debug* 标志启动 Claude 也很有用，以帮助识别配置问题。

### c. Use custom slash commandsc. 使用自定义斜杠命令

For repeated workflows—debugging loops, log analysis, etc.—store prompt templates in Markdown files within the *.claude/commands* folder. These become available through the slash commands menu when you type /. You can check these commands into git to make them available for the rest of your team.  
对于重复的工作流程（调试循环、日志分析等），请将提示模板存储在 *.claude/commands* 文件夹内的 Markdown 文件中。当您键入 / 时，这些选项将通过斜杠命令菜单提供。您可以将这些命令签入 git 中，以便它们可供团队的其他成员使用。

Custom slash commands can include the special keyword *$ARGUMENTS* to pass parameters from command invocation.  
自定义斜杠命令可以包含特殊关键字 *$ARGUMENTS* ，用于从命令调用传递参数。

For example, here’s a slash command that you could use to automatically pull and fix a Github issue:  
例如，这是一个 slash 命令，您可以使用它来自动提取和修复 Github 问题：

Putting the above content into *.claude/commands/fix-github-issue.md* makes it available as the */project:fix-github-issue* command in Claude Code. You could then for example use */project:fix-github-issue 1234* to have Claude fix issue #1234. Similarly, you can add your own personal commands to the *~/.claude/commands* folder for commands you want available in all of your sessions.  
将上述内容放入 *.claude/commands/fix-github-issue.md* 中，即可在 Claude Code 中作为 */project：fix-github-issue* 命令使用。例如，您可以使用 */project：fix-github-issue 1234* 让 Claude 修复问题 # 1234。同样，您可以将自己的个人命令添加到您希望在所有会话中使用的命令的 *~/.claude/commands* 文件夹中。

# 3\. Try common workflows 3. 尝试常见的工作流程

Claude Code doesn’t impose a specific workflow, giving you the flexibility to use it how you want. Within the space this flexibility affords, several successful patterns for effectively using Claude Code have emerged across our community of users:  
Claude Code 不强加特定的工作流程，让您可以灵活地按照自己的方式使用它。在这种灵活性提供的空间中，我们的用户社区中出现了几种有效使用 Claude Code 的成功模式：
## a. Explore, plan, code, commit一个。探索、规划、编码、提交

This versatile workflow suits many problems:  
这种多功能工作流程适用于许多问题：

1. **Ask Claude to read relevant files, images, or URLs**, providing either general pointers ("read the file that handles logging") or specific filenames ("read logging.py"), but explicitly tell it not to write any code just yet.  
	**让 Claude 读取相关文件、图像或 URL** ，提供一般指针（“读取处理日志记录的文件”）或特定文件名（“读取 logging.py”），但明确告诉它暂时不要编写任何代码。
	1. This is the part of the workflow where you should consider strong use of subagents, especially for complex problems. Telling Claude to use subagents to verify details or investigate particular questions it might have, especially early on in a conversation or task, tends to preserve context availability without much downside in terms of lost efficiency.  
		这是工作流中应考虑大量使用子代理的部分，尤其是对于复杂问题。告诉 Claude 使用子代理来验证细节或调查它可能存在的特定问题，尤其是在对话或任务的早期，往往会保持上下文可用性，而不会在效率损失方面有太大的负面影响。
2. **Ask Claude to make a plan for how to approach a specific problem**. We recommend using the word "think" to trigger extended thinking mode, which gives Claude additional computation time to evaluate alternatives more thoroughly. These specific phrases are mapped directly to increasing levels of thinking budget in the system: "think" < "think hard" < "think harder" < "ultrathink." Each level allocates progressively more thinking budget for Claude to use.  
	**让 Claude 制定如何处理特定问题的计划** 。我们建议使用 “think” 这个词来触发扩展思维模式，这为 Claude 提供了额外的计算时间来更彻底地评估替代方案。这些特定的短语直接映射到系统中不断增加的思维预算水平：“think” < “think hard” < “think harder” < “ultrathink”。每个级别都会逐渐分配更多的思考预算供 Claude 使用。
	1. If the results of this step seem reasonable, you can have Claude create a document or a GitHub issue with its plan so that you can reset to this spot if the implementation (step 3) isn’t what you want.  
		如果此步骤的结果看起来合理，您可以让 Claude 创建一个文档或 GitHub 问题及其计划，以便在实现（步骤 3）不是您想要的时重置到此位置。
3. **Ask Claude to implement its solution in code**. This is also a good place to ask it to explicitly verify the reasonableness of its solution as it implements pieces of the solution.  
	**让 Claude 在代码中实现其解决方案** 。这也是要求它在实现解决方案的各个部分时显式验证其解决方案的合理性的好地方。
4. **Ask Claude to commit the result and create a pull request**. If relevant, this is also a good time to have Claude update any READMEs or changelogs with an explanation of what it just did.  
	**让 Claude 提交结果并创建拉取请求** 。如果相关，这也是让 Claude 更新任何 README 或更改日志并解释它刚刚做什么的好时机。

Steps # 1-#2 are crucial—without them, Claude tends to jump straight to coding a solution. While sometimes that's what you want, asking Claude to research and plan first significantly improves performance for problems requiring deeper thinking upfront.  
步骤 # 1-#2 至关重要 — 没有它们，Claude 往往会直接跳到编写解决方案。虽然有时这就是你想要的，但让 Claude 先研究和规划可以显著提高需要提前深入思考的问题的性能。

## b. Write tests, commit; code, iterate, commitb.编写测试，提交;代码、迭代、提交

This is an Anthropic-favorite workflow for changes that are easily verifiable with unit, integration, or end-to-end tests. Test-driven development (TDD) becomes even more powerful with agentic coding:  
这是 Anthropic 最喜欢的工作流，适用于可通过单元、集成或端到端测试轻松验证的更改。测试驱动开发 （TDD） 通过代理编码变得更加强大：

1. **Ask Claude to write tests based on expected input/output pairs**. Be explicit about the fact that you’re doing test-driven development so that it avoids creating mock implementations, even for functionality that doesn’t exist yet in the codebase.  
	**让 Claude 根据预期的输入/输出对编写测试** 。明确说明您正在进行测试驱动开发的事实，以便避免创建模拟实现，即使对于代码库中尚不存在的功能也是如此。
2. **Tell Claude to run the tests and confirm they fail**. Explicitly telling it not to write any implementation code at this stage is often helpful.  
	**告诉 Claude 运行测试并确认它们失败** 。在此阶段明确告诉它不要编写任何实现代码通常会有所帮助。
3. **Ask Claude to commit the tests** when you’re satisfied with them.  
	当您对测试感到满意时 **，请让 Claude 提交测试** 。
4. **Ask Claude to write code that passes the tests**, instructing it not to modify the tests. Tell Claude to keep going until all tests pass. It will usually take a few iterations for Claude to write code, run the tests, adjust the code, and run the tests again.  
	**让 Claude 编写通过测试的代码** ，指示它不要修改测试。告诉 Claude 继续前进，直到所有测试都通过。Claude 通常需要几次迭代来编写代码、运行测试、调整代码并再次运行测试。
	1. At this stage, it can help to ask it to verify with independent subagents that the implementation isn’t overfitting to the tests  
		在此阶段，要求 IT 部门使用独立的 subagent 验证 implementation 是否未过度拟合测试会有所帮助
5. **Ask Claude to commit the code** once you’re satisfied with the changes.  
	如果您对更改感到满意， **请让 Claude 提交代码** 。

Claude performs best when it has a clear target to iterate against—a visual mock, a test case, or another kind of output. By providing expected outputs like tests, Claude can make changes, evaluate results, and incrementally improve until it succeeds.  
当 Claude 具有明确的迭代目标（视觉模拟、测试用例或其他类型的输出）时，它的性能最佳。通过提供预期的输出（如测试），Claude 可以进行更改、评估结果并逐步改进，直到成功。

## c. Write code, screenshot result, iteratec. 编写代码、截图结果、迭代

Similar to the testing workflow, you can provide Claude with visual targets:  
与测试工作流类似，您可以为 Claude 提供可视目标：

1. **Give Claude a way to take browser screenshots** (e.g., with the [Puppeteer MCP server](https://github.com/modelcontextprotocol/servers/tree/c19925b8f0f2815ad72b08d2368f0007c86eb8e6/src/puppeteer), an [iOS simulator MCP server](https://github.com/joshuayoes/ios-simulator-mcp), or manually copy / paste screenshots into Claude).  
	**为 Claude 提供一种截取浏览器屏幕截图的方法** （例如，使用 [Puppeteer MCP 服务器](https://github.com/modelcontextprotocol/servers/tree/c19925b8f0f2815ad72b08d2368f0007c86eb8e6/src/puppeteer) 、 [iOS 模拟器 MCP 服务器](https://github.com/joshuayoes/ios-simulator-mcp) ，或手动将屏幕截图复制/粘贴到 Claude 中）。
2. **Give Claude a visual mock** by copying / pasting or drag-dropping an image, or giving Claude the image file path.  
	通过复制/粘贴或拖放图像，或为 Claude 提供图像文件路径， **为 Claude 提供视觉模拟** 。
3. **Ask Claude to implement the design** in code, take screenshots of the result, and iterate until its result matches the mock.  
	**让 Claude** 在代码中实现设计，截取结果的屏幕截图，然后迭代，直到其结果与模拟匹配。
4. **Ask Claude to commit** when you're satisfied.  
	当您满意时 **，请 Claude 承诺** 。

Like humans, Claude's outputs tend to improve significantly with iteration. While the first version might be good, after 2-3 iterations it will typically look much better. Give Claude the tools to see its outputs for best results.  
与人类一样，Claude 的输出往往会随着迭代而显著提高。虽然第一个版本可能很好，但经过 2-3 次迭代后，它通常会看起来好得多。为 Claude 提供工具以查看其输出以获得最佳结果。

![Safe yolo mode](https://www.anthropic.com/_next/image?url=https%3A%2F%2Fwww-cdn.anthropic.com%2Fimages%2F4zrzovbb%2Fwebsite%2F6ea59a36fe82c2b300bceaf3b880a4b4852c552d-1600x1143.png&w=3840&q=75)

Safe yolo mode

## d. Safe YOLO mode d. 安全的 YOLO 模式

Instead of supervising Claude, you can use *claude* *\--dangerously-skip-permissions* to bypass all permission checks and let Claude work uninterrupted until completion. This works well for workflows like fixing lint errors or generating boilerplate code.  
您可以使用 *claude* *\--dangerously-skip-permissions* 绕过所有权限检查，让 Claude 不间断地工作，直到完成，而不是监督 Claude。这适用于修复 lint 错误或生成样板代码等工作流程。

Letting Claude run arbitrary commands is risky and can result in data loss, system corruption, or even data exfiltration (e.g., via prompt injection attacks). To minimize these risks, use *\--dangerously-skip-permissions* in a container without internet access. You can follow this [reference implementation](https://github.com/anthropics/claude-code/tree/main/.devcontainer) using Docker Dev Containers.  
让 Claude 运行任意命令是有风险的，并可能导致数据丢失、系统损坏，甚至数据泄露（例如，通过提示注入攻击）。为了最大限度地降低这些风险，请在无法访问 Internet 的容器中使用 *\--dangerously-skip-permissions* 。您可以使用 Docker Dev Containers 遵循此 [参考实施](https://github.com/anthropics/claude-code/tree/main/.devcontainer) 。

## e. Codebase Q&A e. 代码库 Q&A

When onboarding to a new codebase, use Claude Code for learning and exploration. You can ask Claude the same sorts of questions you would ask another engineer on the project when pair programming. Claude can agentically search the codebase to answer general questions like:  
在加入新代码库时，请使用 Claude Code 进行学习和探索。你可以问 Claude 的问题，就像在结对编程时问项目中的其他工程师一样。Claude 可以代理搜索代码库以回答一般问题，例如：

- How does logging work?日志记录的工作原理是什么？
- How do I make a new API endpoint?  
	如何创建新的 API 终端节点？
- What does *async move {... }* do on line 134 of foo.rs?  
	*async move {... }* 在 foo.rs 的第 134 行做什么？
- What edge cases does *CustomerOnboardingFlowImpl* handle?  
	*CustomerOnboardingFlowImpl* 处理哪些边缘情况？
- Why are we calling *foo()* instead of *bar()* on line 333?  
	为什么我们在第 333 行调用 *foo（）* 而不是 *bar（）？*
- What’s the equivalent of line 334 of *baz.py* in Java?  
	Java 中 *baz.py* 的第 334 行相当于什么？

At Anthropic, using Claude Code in this way has become our core onboarding workflow, significantly improving ramp-up time and reducing load on other engineers. No special prompting is required! Simply ask questions, and Claude will explore the code to find answers.  
在 Anthropic，以这种方式使用 Claude Code 已成为我们的核心入职工作流程，显著缩短了启动时间并减轻了其他工程师的负担。不需要特别提示！只需提出问题，Claude 就会探索代码以找到答案。

![Use Claude to interact with git](https://www.anthropic.com/_next/image?url=https%3A%2F%2Fwww-cdn.anthropic.com%2Fimages%2F4zrzovbb%2Fwebsite%2Fa08ea13c2359aac0eceacebf2e15f81e8e8ec8d2-1600x1278.png&w=3840&q=75)

Use Claude to interact with git

## f. Use Claude to interact with gitf. 使用 Claude 与 git 交互

Claude can effectively handle many git operations. Many Anthropic engineers use Claude for 90%+ of our *git* interactions:  
Claude 可以有效地处理许多 git 作。许多 Anthropic 工程师使用 Claude 进行 90%+ 的 *git* 交互：

- **Searching *git* history** to answer questions like "What changes made it into v1.2.3?", "Who owns this particular feature?", or "Why was this API designed this way?" It helps to explicitly prompt Claude to look through git history to answer queries like these.  
	**搜索 *git* 历史记录** 以回答诸如“v1.2.3 中有哪些变化”、“谁拥有此特定功能”或“为什么此 API 是这样设计的？它有助于显式提示 Claude 查看 git history 来回答此类查询。
- **Writing commit messages**.Claude will look at your changes and recent history automatically to compose a message taking all the relevant context into account  
	**编写提交消息** 。Claude 将自动查看您的更改和最近的历史记录，以在考虑所有相关上下文的情况下撰写消息
- **Handling complex git operations** like reverting files, resolving rebase conflicts, and comparing and grafting patches  
	**处理复杂的 git 作** ，例如还原文件、解决变基冲突以及比较和嫁接补丁

## g. Use Claude to interact with GitHubg. 使用 Claude 与 GitHub 交互

Claude Code can manage many GitHub interactions:  
Claude Code 可以管理许多 GitHub 交互：

- **Creating pull requests**: Claude understands the shorthand "pr" and will generate appropriate commit messages based on the diff and surrounding context.  
	**创建拉取请求** ：Claude 理解简写 “pr” ，并将根据差异和周围上下文生成适当的提交消息。
- **Implementing one-shot resolutions** for simple code review comments: just tell it to fix comments on your PR (optionally, give it more specific instructions) and push back to the PR branch when it's done.  
	为简单的代码审查注释 **实现一次性解决方案** ：只需告诉它修复 PR 上的注释（可选地给它更具体的说明），并在完成后推送回 PR 分支。
- **Fixing failing builds** or linter warnings  
	**修复失败的构建** 或 Linter 警告
- **Categorizing and triaging open issues** by asking Claude to loop over open GitHub issues  
	通过要求 Claude 循环访问未解决的 GitHub 问题， **对未解决的问题进行分类和分类**

This eliminates the need to remember *gh* command line syntax while automating routine tasks.  
这样，在自动执行日常任务时，就无需记住 *gh* 命令行语法。

## h. Use Claude to work with Jupyter notebooksh. 使用 Claude 处理 Jupyter 笔记本

Researchers and data scientists at Anthropic use Claude Code to read and write Jupyter notebooks. Claude can interpret outputs, including images, providing a fast way to explore and interact with data. There are no required prompts or workflows, but a workflow we recommend is to have Claude Code and a.ipynb file open side-by-side in VS Code.  
Anthropic 的研究人员和数据科学家使用 Claude Code 来读取和写入 Jupyter 笔记本。Claude 可以解释输出（包括图像），从而提供一种快速浏览数据并与之交互的方法。没有必需的提示或工作流，但我们推荐的工作流是在 VS Code 中并排打开 Claude Code 和.ipynb 文件。

You can also ask Claude to clean up or make aesthetic improvements to your Jupyter notebook before you show it to colleagues. Specifically telling it to make the notebook or its data visualizations “aesthetically pleasing” tends to help remind it that it’s optimizing for a human viewing experience.  
您还可以要求 Claude 在向同事展示 Jupyter 笔记本之前对其进行清理或美学改进。明确地告诉它使笔记本或其数据可视化“美观”，这往往有助于提醒它，它正在针对人类的观看体验进行优化。

# 4\. Optimize your workflow4. 优化您的工作流程

The suggestions below apply across all workflows:  
以下建议适用于所有工作流程：

## a. Be specific in your instructions一个。在说明中具体

Claude Code’s success rate improves significantly with more specific instructions, especially on first attempts. Giving clear directions upfront reduces the need for course corrections later.  
Claude Code 的成功率通过更具体的说明显着提高，尤其是在第一次尝试时。提前给出明确的方向可以减少以后修正路线的需要。

For example:例如：

| Poor 穷 | Good 好 |
| --- | --- |
| add tests for foo.py 为 foo.py 添加测试 | write a new test case for foo.py, covering the edge case where the user is logged out. avoid mocks   为 foo.py 编写一个新的测试用例，涵盖用户注销的边缘情况。避免 mock |
| why does ExecutionFactory have such a weird api?   为什么 ExecutionFactory 有这么奇怪的 api？ | look through ExecutionFactory's git history and summarize how its api came to be   浏览 ExecutionFactory 的 git 历史并总结其 api 是如何形成的 |
| add a calendar widget 添加日历小部件 | look at how existing widgets are implemented on the home page to understand the patterns and specifically how code and interfaces are separated out. HotDogWidget.php is a good example to start with. then, follow the pattern to implement a new calendar widget that lets the user select a month and paginate forwards/backwards to pick a year. Build from scratch without libraries other than the ones already used in the rest of the codebase.   查看如何在主页上实现现有 widget，以了解模式，特别是代码和接口是如何分离的。HotDogWidget.php 是一个很好的开始示例。然后，按照 Pattern 实现一个新的 Calendar 小部件，该小部件允许用户选择一个月份并向前/向后分页以选择年份。从头开始构建，无需代码库其余部分已使用的库以外的库。 |

Claude can infer intent, but it can't read minds. Specificity leads to better alignment with expectations.  
Claude 可以推断意图，但它不能读心术。特异性可以更好地与期望保持一致。

![Give Claude images](https://www.anthropic.com/_next/image?url=https%3A%2F%2Fwww-cdn.anthropic.com%2Fimages%2F4zrzovbb%2Fwebsite%2F75e1b57a0b696e7aafeca1ed5fa6ba7c601a5953-1360x1126.png&w=3840&q=75)

Give Claude images

## b. Give Claude images b. 为 Claude 提供图像

Claude excels with images and diagrams through several methods:  
Claude 擅长通过多种方法处理图像和图表：

- **Paste screenshots** (pro tip: hit *cmd+ctrl+shift+4* in macOS to screenshot to clipboard and *ctrl+v* to paste. Note that this is not cmd+v like you would usually use to paste on mac and does not work remotely.)  
	**粘贴屏幕截图** （专业提示：在 macOS 中按 *cmd+ctrl+shift+4* 将屏幕截图到剪贴板，按 *ctrl+v* 进行粘贴。请注意，这不是您通常在 mac 上用于粘贴的 cmd+v，并且不能远程工作。
- **Drag and drop** images directly into the prompt input  
	**将** 图像直接拖放到提示输入中
- **Provide file paths** for images  
	**为图像提供文件路径**

This is particularly useful when working with design mocks as reference points for UI development, and visual charts for analysis and debugging. If you are not adding visuals to context, it can still be helpful to be clear with Claude about how important it is for the result to be visually appealing.  
当使用设计模拟作为 UI 开发的参考点，以及使用可视化图表进行分析和调试时，这尤其有用。如果您不向上下文添加视觉效果，与 Claude 明确说明结果具有视觉吸引力的重要性仍然会有所帮助。

![Mention files you want Claude to look at or work on](https://www.anthropic.com/_next/image?url=https%3A%2F%2Fwww-cdn.anthropic.com%2Fimages%2F4zrzovbb%2Fwebsite%2F7372868757dd17b6f2d3fef98d499d7991d89800-1450x1164.png&w=3840&q=75)

Mention files you want Claude to look at or work on

## c. Mention files you want Claude to look at or work onc. 提及您希望 Claude 查看或处理的文件

Use tab-completion to quickly reference files or folders anywhere in your repository, helping Claude find or update the right resources.  
使用 Tab 键补全功能可快速引用存储库中任意位置的文件或文件夹，从而帮助 Claude 查找或更新正确的资源。

![Give Claude URLs](https://www.anthropic.com/_next/image?url=https%3A%2F%2Fwww-cdn.anthropic.com%2Fimages%2F4zrzovbb%2Fwebsite%2Fe071de707f209bbaa7f16b593cc7ed0739875dae-1306x1088.png&w=3840&q=75)

Give Claude URLs

## d. Give Claude URLs d. 提供 Claude URL

Paste specific URLs alongside your prompts for Claude to fetch and read. To avoid permission prompts for the same domains (e.g., docs.foo.com), use */allowed-tools* to add domains to your allowlist.  
将特定 URL 粘贴到提示旁边，以便 Claude 获取和读取。为避免对相同域（例如 docs.foo.com）进行权限提示，请使用 */allowed-tools* 将域添加到您的许可名单中。
## e. Course correct early and oftene.尽早并经常纠正路线

While auto-accept mode (shift+tab to toggle) lets Claude work autonomously, you'll typically get better results by being an active collaborator and guiding Claude's approach. You can get the best results by thoroughly explaining the task to Claude at the beginning, but you can also course correct Claude at any time.  
虽然自动接受模式（shift+tab 切换）允许 Claude 自主工作，但通过成为积极的协作者并指导 Claude 的方法，您通常会获得更好的结果。一开始就向 Claude 彻底解释任务可以获得最佳效果，但您也可以随时纠正 Claude。

These four tools help with course correction:  
这四个工具有助于修正路线：

- **Ask Claude to make a plan** before coding. Explicitly tell it not to code until you’ve confirmed its plan looks good.  
	**让 Claude 在编码之前制定一个计划** 。明确告诉它不要编码，直到你确认它的计划看起来不错。
- **Press Escape to interrupt** Claude during any phase (thinking, tool calls, file edits), preserving context so you can redirect or expand instructions.  
	**按 Esc 键可中断** 在任何阶段（思考、工具调用、文件编辑）保持 Claude，保留上下文以便重定向或扩展说明。
- **Double-tap Escape to jump back in history**, edit a previous prompt, and explore a different direction. You can edit the prompt and repeat until you get the result you're looking for.  
	**双击 Escape 可跳回历史记录** 、编辑上一个提示并探索不同的方向。您可以编辑提示并重复作，直到获得所需的结果。
- **Ask Claude to undo changes**, often in conjunction with option #2 to take a different approach.  
	**要求 Claude 撤消更改** ，通常与选项 #2 结合使用，以采用不同的方法。

Though Claude Code occasionally solves problems perfectly on the first attempt, using these correction tools generally produces better solutions faster.  
尽管 Claude Code 偶尔会在第一次尝试时完美地解决问题，但使用这些更正工具通常会更快地产生更好的解决方案。
## f. Use /clear to keep context focusedf.使用 /clear 保持上下文重点

During long sessions, Claude's context window can fill with irrelevant conversation, file contents, and commands. This can reduce performance and sometimes distract Claude. Use the */clear* command frequently between tasks to reset the context window.  
在长时间的会话期间，Claude 的上下文窗口可能会充满不相关的对话、文件内容和命令。这会降低性能，有时会分散 Claude 的注意力。在任务之间频繁使用 */clear* 命令来重置上下文窗口。

## g. Use checklists and scratchpads for complex workflowsg.将清单和暂存器用于复杂的工作流程

For large tasks with multiple steps or requiring exhaustive solutions—like code migrations, fixing numerous lint errors, or running complex build scripts—improve performance by having Claude use a Markdown file (or even a GitHub issue!) as a checklist and working scratchpad:  
对于具有多个步骤或需要详尽解决方案的大型任务（如代码迁移、修复大量 lint 错误或运行复杂的构建脚本），让 Claude 使用 Markdown 文件（甚至是 GitHub 问题）作为清单和工作便笺簿来提高性能：

For example, to fix a large number of lint issues, you can do the following:  
例如，要修复大量 lint 问题，您可以执行以下作：

1. **Tell Claude to run the lint command** and write all resulting errors (with filenames and line numbers) to a Markdown checklist  
	**告诉 Claude 运行 lint 命令** 并将所有结果错误（包括文件名和行号）写入 Markdown 清单
2. **Instruct Claude to address each issue one by one**, fixing and verifying before checking it off and moving to the next  
	**指示 Claude 逐一解决每个问题** ，修复和验证，然后再检查并进入下一个问题

## h. Pass data into Claudeh.将数据传入 Claude

Several methods exist for providing data to Claude:  
有几种方法可以向 Claude 提供数据：

- **Copy and paste** directly into your prompt (most common approach)  
	**直接复制并粘贴到** 您的提示中（最常见的方法）
- **Pipe into Claude Code** (e.g., *cat foo.txt | claude*), particularly useful for logs, CSVs, and large data  
	**管道到 Claude 代码** （例如， *cat foo.txt | claude* ），特别适用于日志、CSV 和大数据
- **Tell Claude to pull data** via bash commands, MCP tools, or custom slash commands  
	**告诉 Claude** 通过 bash 命令、MCP 工具或自定义斜杠命令拉取数据
- **Ask Claude to read files** or fetch URLs (works for images too)  
	**让 Claude 读取文件** 或获取 URL（也适用于图像）

Most sessions involve a combination of these approaches. For example, you can pipe in a log file, then tell Claude to use a tool to pull in additional context to debug the logs.  
大多数会议都涉及这些方法的组合。例如，您可以通过管道传入日志文件，然后告诉 Claude 使用工具提取其他上下文来调试日志。

# 5\. Use headless mode to automate your infra5. 使用无头模式自动化您的基础设施

Claude Code includes [headless mode](https://docs.anthropic.com/en/docs/agents-and-tools/claude-code/overview#automate-ci-and-infra-workflows) for non-interactive contexts like CI, pre-commit hooks, build scripts, and automation. Use the -p flag with a prompt to enable headless mode, and *\--output-format stream-json* for streaming JSON output.  
Claude Code 包括用于非交互式上下文（如 CI、预提交钩子、构建脚本和自动化）的 [无头模式](https://docs.anthropic.com/en/docs/agents-and-tools/claude-code/overview#automate-ci-and-infra-workflows) 。使用带有提示符的 -p 标志启用无头模式，并使用 *\--output-format stream-json* 进行流式处理 JSON 输出。

Note that headless mode does not persist between sessions. You have to trigger it each session.  
请注意，无头模式不会在会话之间持续存在。您必须在每个会话中触发它。

## a. Use Claude for issue triagea. 使用 Claude 进行问题分类

Headless mode can power automations triggered by GitHub events, such as when a new issue is created in your repository. For example, the public [Claude Code repository](https://github.com/anthropics/claude-code/blob/main/.github/actions/claude-issue-triage-action/action.yml) uses Claude to inspect new issues as they come in and assign appropriate labels.  
无头模式可以为 GitHub 事件触发的自动化提供支持，例如在存储库中创建新问题时。例如，公共 [Claude Code 存储库](https://github.com/anthropics/claude-code/blob/main/.github/actions/claude-issue-triage-action/action.yml) 使用 Claude 来检查新问题并分配适当的标签。

## b. Use Claude as a linterb. 使用 Claude 作为 Linter

Claude Code can provide [subjective code reviews](https://github.com/anthropics/claude-code/blob/main/.github/actions/claude-code-action/action.yml) beyond what traditional linting tools detect, identifying issues like typos, stale comments, misleading function or variable names, and more.  
Claude Code 可以提供超出传统 linting 工具检测范围的主 [观代码审查](https://github.com/anthropics/claude-code/blob/main/.github/actions/claude-code-action/action.yml) ，识别拼写错误、过时注释、误导性函数或变量名称等问题。

# 6\. Uplevel with multi-Claude workflows6. 使用多 Claude 工作流程升级

Beyond standalone usage, some of the most powerful applications involve running multiple Claude instances in parallel:  
除了独立使用之外，一些最强大的应用程序还涉及并行运行多个 Claude 实例：

## a. Have one Claude write code; use another Claude to verify一个。让一个 Claude 编写代码;使用另一个 Claude 进行验证

A simple but effective approach is to have one Claude write code while another reviews or tests it. Similar to working with multiple engineers, sometimes having separate context is beneficial:  
一种简单但有效的方法是让一个 Claude 编写代码，而另一个 Claude 审查或测试它。与与多个工程师一起工作类似，有时拥有单独的上下文是有益的：

1. Use Claude to write code  
	使用 Claude 编写代码
2. Run */clear* or start a second Claude in another terminal  
	在另一个终端中运行 */clear* 或启动第二个 Claude
3. Have the second Claude review the first Claude's work  
	让第二个 Claude 审阅第一个 Claude 的作品
4. Start another Claude (or */clear* again) to read both the code and review feedback  
	启动另一个 Claude（或再次 */clear* ）以读取代码和查看反馈
5. Have this Claude edit the code based on the feedback  
	让这个 Claude 根据反馈编辑代码

You can do something similar with tests: have one Claude write tests, then have another Claude write code to make the tests pass. You can even have your Claude instances communicate with each other by giving them separate working scratchpads and telling them which one to write to and which one to read from.  
您可以对测试执行类似的作：让一个 Claude 编写测试，然后让另一个 Claude 编写代码以使测试通过。您甚至可以让 Claude 实例相互通信，方法是为它们提供单独的工作便笺簿，并告诉它们要写入哪个实例和从哪个实例读取。

This separation often yields better results than having a single Claude handle everything.  
这种分离通常比让一个 Claude 处理所有事情产生更好的结果。

## b. Have multiple checkouts of your repob.对存储库进行多次签出

Rather than waiting for Claude to complete each step, something many engineers at Anthropic do is:  
Anthropic 的许多工程师不是等待 Claude 完成每个步骤，而是要做的是：

1. **Create 3-4 git checkouts** in separate folders  
	在单独的文件夹中 **创建 3-4 个 git 签出**
2. **Open each folder** in separate terminal tabs  
	在单独的终端选项卡中 **打开每个文件夹**
3. **Start Claude in each folder** with different tasks  
	**在每个文件夹中启动 Claude** ，其中包含不同的任务
4. **Cycle through** to check progress and approve/deny permission requests  
	**循环检查** 进度并批准/拒绝权限请求

## c. Use git worktrees c. 使用 git worktrees

This approach shines for multiple independent tasks, offering a lighter-weight alternative to multiple checkouts. Git worktrees allow you to check out multiple branches from the same repository into separate directories. Each worktree has its own working directory with isolated files, while sharing the same Git history and reflog.  
这种方法适用于多个独立任务，为多个结账提供了一种更轻量级的替代方案。Git 工作树允许您将同一存储库中的多个分支检出到单独的目录中。每个 worktree 都有自己的工作目录，其中包含隔离的文件，同时共享相同的 Git 历史记录和 reflog。

Using git worktrees enables you to run multiple Claude sessions simultaneously on different parts of your project, each focused on its own independent task. For instance, you might have one Claude refactoring your authentication system while another builds a completely unrelated data visualization component. Since the tasks don't overlap, each Claude can work at full speed without waiting for the other's changes or dealing with merge conflicts:  
使用 git worktrees 使您能够在项目的不同部分同时运行多个 Claude 会话，每个会话都专注于自己的独立任务。例如，您可能让一个 Claude 重构您的身份验证系统，而另一个 Claude 构建一个完全不相关的数据可视化组件。由于任务不重叠，因此每个 Claude 都可以全速工作，而无需等待对方的更改或处理合并冲突：

1. **Create worktrees**: *git worktree add../project-feature-a feature-a*  
	**创建工作树** ： *git worktree add../project-feature-a 功能 a*
2. **Launch Claude in each worktree**: *cd../project-feature-a && claude*  
	**在每个工作树中启动 Claude** ： *cd../project-feature-a & claude*
3. **Create additional worktrees** as needed (repeat steps 1-2 in new terminal tabs)  
	根据需要 **创建其他工作树** （在新的终端选项卡中重复步骤 1-2）

Some tips:一些提示：

- Use consistent naming conventions  
	使用一致的命名约定
- Maintain one terminal tab per worktree  
	为每个工作树维护一个终端选项卡
- If you’re using iTerm2 on Mac, [set up notifications](https://docs.anthropic.com/en/docs/agents-and-tools/claude-code/overview#notification-setup) for when Claude needs attention  
	如果您在 Mac 上使用 iTerm2， [请设置](https://docs.anthropic.com/en/docs/agents-and-tools/claude-code/overview#notification-setup) Claude 需要注意时的通知
- Use separate IDE windows for different worktrees  
	为不同的工作树使用单独的 IDE 窗口
- Clean up when finished: *git worktree remove../project-feature-a*  
	完成后清理： *git worktree remove../project-feature-a*

## d. Use headless mode with a custom harnessd. 将 Headless 模式与自定义线束结合使用

*claude -p* (headless mode) integrates Claude Code programmatically into larger workflows while leveraging its built-in tools and system prompt. There are two primary patterns for using headless mode:  
*claude -p* （无头模式）以编程方式将 Claude Code 集成到更大的工作流中，同时利用其内置工具和系统提示符。使用 Headless 模式有两种主要模式：

1\. **Fanning out** handles large migrations or analyses (e.g., analyzing sentiment in hundreds of logs or analyzing thousands of CSVs):  
1\. **扇出** 处理大型迁移或分析（例如，分析数百个日志中的情绪或分析数千个 CSV）：

1. Have Claude write a script to generate a task list. For example, generate a list of 2k files that need to be migrated from framework A to framework B.  
	让 Claude 编写一个脚本来生成任务列表。例如，生成一个需要从框架 A 迁移到框架 B 的 2k 文件的列表。
2. Loop through tasks, calling Claude programmatically for each and giving it a task and a set of tools it can use. For example: *claude -p “migrate foo.py from React to Vue. When you are done, you MUST return the string OK if you succeeded, or FAIL if the task failed.” --allowedTools Edit Bash(git commit:\*)”*  
	遍历任务，以编程方式为每个任务调用 Claude，并为其提供一个任务和一组可以使用的工具。例如： *claude -p “将 foo.py 从 React 迁移到 Vue。完成后，如果成功，则必须返回字符串 OK，如果任务失败，则必须返回字符串 FAIL。--allowedTools Edit Bash（git commit：\*）”*
3. Run the script several times and refine your prompt to get the desired outcome.  
	多次运行脚本并优化提示以获得所需的结果。

2\. **Pipelining** integrates Claude into existing data/processing pipelines:  
2\. **Pipelining** 将 Claude 集成到现有的数据 / 处理管道中：

1. Call *claude -p “<your prompt>” --json | your\_command*, where *your\_command* is the next step of your processing pipeline  
	调用 *claude -p “<your prompt>” --json | your\_command* ，其中 *your\_command* 是处理管道的下一步
2. That’s it! JSON output (optional) can help provide structure for easier automated processing.  
	就是这样！JSON 输出（可选）有助于提供结构，以便更轻松地进行自动化处理。

For both of these use cases, it can be helpful to use the *\--verbose* flag for debugging the Claude invocation. We generally recommend turning verbose mode off in production for cleaner output.  
对于这两个使用案例，使用 *\--verbose* 标志调试 Claude 调用可能会有所帮助。我们通常建议在生产环境中关闭详细模式以获得更清晰的输出。

What are your tips and best practices for working with Claude Code? Tag @AnthropicAI so we can see what you're building!  
您使用 Claude Code 的技巧和最佳实践是什么？标记@AnthropicAI 以便我们可以看到您正在构建的内容！

## Acknowledgements 确认

Written by Boris Cherny. This work draws upon best practices from across the broader Claude Code user community, whose creative approaches and workflows continue to inspire us. Special thanks also to Daisy Hollman, Ashwin Bhat, Cat Wu, Sid Bidasaria, Cal Rueb, Nodir Turakulov, Barry Zhang, Drew Hodun and many other Anthropic engineers whose valuable insights and practical experience with Claude Code helped shape these recommendations.  
作者：Boris Cherny。这项工作借鉴了更广泛的 Claude Code 用户社区的最佳实践，他们的创新方法和工作流程不断激励着我们。还要特别感谢 Daisy Hollman、Ashwin Bhat、Cat Wu、Sid Bidasaria、Cal Rueb、Nodir Turakulov、Barry Zhang、Drew Hodun 和许多其他 Anthropic 工程师，他们对 Claude Code 的宝贵见解和实践经验帮助形成了这些建议。


---
# Hooks
[[Hooks - Claude]]
[[Claude Code Hooks：变革你 2025 年的开发工作流程]]

## Lint post check Hook
### Installation Script
```shell
#!/bin/bash

echo "Installing linting and formatting tools..."

# Python tools
echo "Installing Python tools..."
pip install --upgrade black flake8 mypy isort pytest

# JavaScript/TypeScript tools
echo "Installing JS/TS tools..."
npm install -g prettier eslint typescript

# Go tools
echo "Installing Go tools..."
go install golang.org/x/tools/cmd/goimports@latest
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# Markdown tools
echo "Installing Markdown tools..."
npm install -g markdownlint-cli

# YAML tools
echo "Installing YAML tools..."
pip install yamllint

# Shell tools
echo "Installing Shell tools..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    brew install shellcheck jq
else
    sudo apt-get install -y shellcheck jq
fi

echo "✅ All tools installed!"
```
### Lint check Scrpt
```shell
#!/bin/bash

# 全局变量
HAS_LINT_ERRORS=0
LINT_OUTPUT=""
ORIGINAL_DIR=$(pwd)

# 从 stdin 读取 JSON
json_input=$(cat)
# 调试：显示收到的 JSON（可以在调试后注释掉）
#echo -e "${PURPLE}📍 DEBUG: Received JSON:${NC}"
#echo "$json_input" | jq '.' 2>/dev/null || echo "$json_input"

# 提取信息
tool_name=$(echo "$json_input" | jq -r '.tool_name // empty')
hook_event=$(echo "$json_input" | jq -r '.hook_event_name // empty')

[ "$hook_event" != "PostToolUse" ] && exit 0

# 提取文件路径 - 所有工具都使用 file_path
case "$tool_name" in
    "Write"|"Create"|"Edit"|"MultiEdit")
        file_path=$(echo "$json_input" | jq -r '.tool_input.file_path // empty')
        ;;
    *)
        exit 0
        ;;
esac

[ -z "$file_path" ] || [ "$file_path" = "null" ] && exit 0

# 展开路径
file_path="${file_path/#\~/$HOME}"

if [ ! -f "$file_path" ]; then
    echo "File does not exist: $file_path" >&2
    exit 1
fi

# 文件信息
filename=$(basename "$file_path")
extension="${filename##*.}"
dirname=$(dirname "$file_path")

# 改进的 run_linter 函数
run_linter() {
    local cmd="$1"
    local name="$2"
    local temp_file=$(mktemp)
    
    # 运行命令，合并 stdout 和 stderr
    if eval "$cmd" > "$temp_file" 2>&1; then
        rm -f "$temp_file"
        return 0
    else
        local exit_code=$?
        local output=$(cat "$temp_file")
        rm -f "$temp_file"
        
        if [ -n "$output" ]; then
            HAS_LINT_ERRORS=1
            LINT_OUTPUT="${LINT_OUTPUT}=== ${name} errors in ${file_path} ===\n${output}\n\n"
        fi
        return $exit_code
    fi
}

# 查找项目根目录（包含配置文件）
find_project_root() {
    local dir="$1"
    local config_files=("$@")
    
    while [ "$dir" != "/" ]; do
        for config in "${config_files[@]:1}"; do
            [ -f "$dir/$config" ] && echo "$dir" && return 0
        done
        dir=$(dirname "$dir")
    done
    
    # 检查主目录
    for config in "${config_files[@]:1}"; do
        [ -f "$HOME/$config" ] && echo "$HOME" && return 0
    done
    
    echo "$1"  # 默认返回文件所在目录
}

# 处理不同文件类型
case $extension in
    py)
        # Python
        command -v black &>/dev/null && black "$file_path" 2>/dev/null || true
        command -v isort &>/dev/null && isort "$file_path" 2>/dev/null || true
        command -v flake8 &>/dev/null && run_linter "flake8 '$file_path'" "Flake8"
        command -v mypy &>/dev/null && run_linter "mypy '$file_path'" "MyPy"
        
        if [[ "$file_path" == *test*.py ]] && command -v pytest &>/dev/null; then
            pytest -v "$file_path" 2>/dev/null || true
        fi
        ;;
    
    js|jsx|ts|tsx|mjs|cjs)
        # JavaScript/TypeScript
        command -v prettier &>/dev/null && prettier --write "$file_path" 2>/dev/null || true
        
        # ESLint - 在项目根目录运行
        if command -v eslint &>/dev/null; then
            project_root=$(find_project_root "$dirname" \
                "eslint.config.js" "eslint.config.mjs" ".eslintrc.js" \
                ".eslintrc.json" ".eslintrc.yml" ".eslintrc.yaml" \
                ".eslintrc" "package.json")
            
            # 保存当前目录并切换到项目根目录
            cd "$project_root"
            run_linter "eslint '$file_path' --no-ignore" "ESLint"
            cd "$ORIGINAL_DIR"
        fi
        
        # TypeScript 类型检查
        if [[ "$extension" =~ ^(ts|tsx)$ ]] && command -v tsc &>/dev/null; then
            project_root=$(find_project_root "$dirname" "tsconfig.json")
            if [ -f "$project_root/tsconfig.json" ]; then
                cd "$project_root"
                run_linter "tsc --noEmit" "TypeScript"
                cd "$ORIGINAL_DIR"
            fi
        fi
        ;;
    
    go)
        # Go
        gofmt -w "$file_path" 2>/dev/null || true
        command -v goimports &>/dev/null && goimports -w "$file_path" 2>/dev/null || true
        command -v go &>/dev/null && run_linter "go vet '$file_path'" "go vet"
        command -v golangci-lint &>/dev/null && run_linter "golangci-lint run '$file_path'" "golangci-lint"
        
        if [[ "$file_path" == *_test.go ]] && command -v go &>/dev/null; then
            go test -v "$dirname" 2>/dev/null || true
        fi
        ;;
    
    md|markdown)
        # Markdown
        if command -v markdownlint &>/dev/null; then
            markdownlint --fix "$file_path" 2>/dev/null || true
            run_linter "markdownlint '$file_path'" "markdownlint"
        fi
        ;;
    
    yml|yaml)
        # YAML
        command -v yamllint &>/dev/null && run_linter "yamllint '$file_path'" "yamllint"
        ;;
    
    json)
        # JSON
        if [[ "$file_path" != *".jsonl" ]] && command -v jq &>/dev/null; then
            if ! jq . "$file_path" > "$file_path.tmp" 2>/dev/null; then
                HAS_LINT_ERRORS=1
                LINT_OUTPUT="${LINT_OUTPUT}=== JSON validation errors in ${file_path} ===\nInvalid JSON syntax\n\n"
            else
                mv "$file_path.tmp" "$file_path"
            fi
            rm -f "$file_path.tmp"
        fi
        ;;
    
    sh|bash|zsh)
        # Shell
        command -v shellcheck &>/dev/null && run_linter "shellcheck '$file_path'" "ShellCheck"
        ;;
    
    *)
        # 检查 shebang
        if [ -x "$file_path" ] && head -n 1 "$file_path" | grep -q "^#!.*sh"; then
            command -v shellcheck &>/dev/null && run_linter "shellcheck '$file_path'" "ShellCheck"
        fi
        ;;
esac

# 返回结果
if [ $HAS_LINT_ERRORS -eq 1 ]; then
    printf "%b" "$LINT_OUTPUT" >&2
    exit 2
else
    echo "✓ Lint check passed: $filename"
    exit 0
fi
```
#### Lint check light version
```shell
#!/bin/bash

for file in $CLAUDE_FILE_PATHS; do
    [ -f "$file" ] || continue
    
    case "${file##*.}" in
        py)
            echo "🐍 Python: $file"
            command -v black &>/dev/null && black "$file"
            command -v flake8 &>/dev/null && flake8 "$file"
            ;;
        
        ts|tsx|js|jsx)
            echo "📘 JS/TS: $file"
            command -v prettier &>/dev/null && prettier --write "$file"
            command -v eslint &>/dev/null && eslint --fix "$file"
            ;;
        
        go)
            echo "🐹 Go: $file"
            gofmt -w "$file"
            command -v go &>/dev/null && go vet "$file" 2>/dev/null
            ;;
        
        md)
            echo "📝 Markdown: $file"
            command -v markdownlint &>/dev/null && markdownlint --fix "$file"
            ;;
    esac
done

echo "✅ Done!"
```

### Setup eslint config file
create under project folder
```
cat eslint.config.js
export default [
  {
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: {
        console: 'readonly',
        process: 'readonly',
        Buffer: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        global: 'readonly',
        module: 'readonly',
        require: 'readonly',
        exports: 'readonly'
      }
    },
    rules: {
      // Code quality
      'no-unused-vars': 'error',
      'no-undef': 'error',
      'no-console': 'off',
      'no-debugger': 'warn',
      
      // Style
      'indent': ['error', 2],
      'quotes': ['error', 'single'],
      'semi': ['error', 'always'],
      'comma-dangle': ['error', 'never'],
      
      // Best practices
      'eqeqeq': 'error',
      'no-eval': 'error',
      'no-implied-eval': 'error',
      'no-new-func': 'error',
      'no-var': 'error',
      'prefer-const': 'error'
    }
  }
];
```


### Hook Setup
```
{
  "hooks": {
    "PostToolUse": [
      {
        "matcher": "",
        "hooks": [
          {
            "type": "command",
            "command": "~/claude_post_tool_lint.sh"
          }
        ]
      }
    ]
  }
}
```

---
# Prompt
## Code reference
```
# USER REQUIREMENTS (Must follow)

## Notification Handling
1. After task done, Pls send detail current task stage related information and task status to line with line-notification tool
2. before you want to get user feedback, pls send detail current task stage related information and task status to line with line-notifcation tool

## Code References
**THIS IS OF UTTER IMPORTANCE THE USERS HAPPINESS DEPENDS ON IT!**

When referencing code locations, you MUST use clickable format that VS Code recognizes:
- `path/to/file.ts:123` format (file:line)
- `path/to/file.ts:123-456` (ranges)
- Always use relative paths from the project root
- Examples:
  - `src/server/fwd.ts:92` - single line reference
  - `src/server/pty/pty-manager.ts:274-280` - line range
  - `web/src/client/app.ts:15` - when in parent directory

NEVER give a code reference or location in any other format.

## CRITICAL
**IMPORTANT**: BEFORE YOU DO ANYTHING, READ spec.md IN FULL USING THE READ TOOL!
**IMPORTANT**: NEVER USE GREP. ALWAYS USE RIPGREP!

```

## Rules / Userr Guidelines
```
# User feedback
If requirements or instructions are unclear use the tool mcp-feedback-enhanced to ask clarifying questions to the user before proceeding, do not make assumptions. Whenever possible, present the user with predefined options through the mcp-feedback-enhanced MCP tool to facilitate quick decisions.
Whenever you're about to complete a user request, call the mcp-feedback-enhanced tool to request user feedback before ending the process. If the feedback is empty you can end the request and don't call the tool in loop.


# Notification Handling (as user may not be nearby)
1. After task done, Pls send detail current task stage related information and task status to line with line-notification tool
2. before you want to get user feedback, pls send detail current task stage related information and task status to line with line-notifcation tool

# Instructions for Reasoning
When performing complex reasoning or problem-solving tasks, apply a quantum-inspired thinking approach:

1. **Superposition Phase**: During intermediate reasoning steps, maintain multiple potential solution paths simultaneously, treating each possibility as existing in a "superposition state" where all viable approaches coexist.

2. **Parallel Path Exploration**: Explicitly consider and track multiple reasoning branches, hypotheses, or solution strategies without prematurely eliminating any that seem plausible.

3. **Uncertainty Acknowledgment**: Recognize and document areas where multiple interpretations or approaches are valid, rather than forcing early convergence on a single path.

4. **Collapse to Solution**: Only at the final stage, evaluate all maintained possibilities against the problem constraints and evidence to "collapse" to the most optimal or well-supported solution path.

5. **Transparent Process**: When presenting the final result to the user, briefly explain that multiple approaches were considered and why the chosen solution was selected over alternatives.

This approach helps ensure thorough exploration of the solution space before committing to a final answer, potentially leading to more robust and well-reasoned outcomes.
```
## Virutal Enviroment 
[[virtual-environment-management-prompt]]
---
# CodeBase Index and Search
https://github.com/ancoleman/qdrant-rag-mcp
https://github.com/johnhuang316/code-index-mcp

## code index mcp
git clone https://github.com/johnhuang316/code-index-mcp.git
uv sync
```
-- windows
	"code-index": {
      "command": "uv",
      "args": [
        "--directory",
        "C:\\Work\\Research\\AI\\code-index-mcp",
        "run",
        "code-index-mcp"
      ]
    }

-- ubuntu

```
Use in Claude
```
1. set Project Path
Please set the project path to C:\Users\<USER>\projects\my-react-app
2. Refresh the index
3. search

```

## Qdrant RAG MCP Server
-- Install uv
curl -LsSf https://astral.sh/uv/install.sh | sh

-- install
git clone https://github.com/ancoleman/qdrant-rag-mcp.git
./scripts/setup.sh
 docker compose -f docker/docker-compose.yml logs qdrant
./install_global.sh

-- maybe i just need to install docker
and uv sync

- With auto-indexing:
Option A - Per session:"
export QDRANT_RAG_AUTO_INDEX=true
claude

Option B - Always on:"
Add to your ~/.bashrc or ~/.zshrc:"
export QDRANT_RAG_AUTO_INDEX=true
export QDRANT_RAG_DEBOUNCE=5.0  # Optional: custom debounce"


-- add to .claude.json
```
-- claude desktop
	"qdrant-rag": {
      "command": "uv",
      "args": [
        "--directory",
        "/opt/app/mcp/qdrant-rag-mcp",
        "run",
        "/opt/app/mcp/qdrant-rag-mcp/src/qdrant_mcp_context_aware.py"
      ]
    }

-- claude code
    "qdrant-rag": {
      "type": "stdio",
      "command": "uv",
      "args": [
        "--directory",
        "/opt/app/mcp/qdrant-rag-mcp",
        "run",
        "/opt/app/mcp/qdrant-rag-mcp/src/qdrant_mcp_context_aware.py"
      ]
    }
```

-- start
Get current directory with pwd, export MCP_CLIENT_CWD to that value, then run health check
Index all code files in this project
Reindex this project
