---
title: "davila7/claude-code-templates: CLI tool for configuring and monitoring Claude Code"
source: https://github.com/davila7/claude-code-templates
author:
  - "[[davila7]]"
  - "[[claude]]"
published:
created: 2025-07-13
description: CLI tool for configuring and monitoring Claude Code - davila7/claude-code-templates
tags:
  - AI/SKOOL
  - AI/Claude
reference:
---
**[claude-code-templates](https://github.com/davila7/claude-code-templates)** Public

CLI tool for configuring and monitoring Claude Code

[danielavila.me](https://danielavila.me/ "https://danielavila.me")

[MIT license](https://github.com/davila7/claude-code-templates/blob/main/LICENSE)

[Code of conduct](https://github.com/davila7/claude-code-templates/blob/main/CODE_OF_CONDUCT.md)

[Security policy](https://github.com/davila7/claude-code-templates/blob/main/SECURITY.md)

[421 stars](https://github.com/davila7/claude-code-templates/stargazers) [45 forks](https://github.com/davila7/claude-code-templates/forks) [3 watching](https://github.com/davila7/claude-code-templates/watchers) [Branches](https://github.com/davila7/claude-code-templates/branches) [Tags](https://github.com/davila7/claude-code-templates/tags) [Activity](https://github.com/davila7/claude-code-templates/activity)

Public repository

[Open in github.dev](https://github.dev/) [Open in a new github.dev tab](https://github.dev/) [Open in codespace](https://github.com/codespaces/new/davila7/claude-code-templates?resume=1)

<table><thead><tr><th colspan="2"><span>Name</span></th><th colspan="1"><span>Name</span></th><th><p><span>Last commit message</span></p></th><th colspan="1"><p><span>Last commit date</span></p></th></tr></thead><tbody><tr><td colspan="3"><p><span>and</span></p><p><span><a href="https://github.com/davila7/claude-code-templates/commit/3dbeaec2e07a41f9d1d232666e36ad831e0ede52">feat: Enhance analytics dashboard with real-time conversation state d…</a></span></p><p><span><a href="https://github.com/davila7/claude-code-templates/commit/3dbeaec2e07a41f9d1d232666e36ad831e0ede52">3dbeaec</a> ·</span></p><p><a href="https://github.com/davila7/claude-code-templates/commits/main/"><span><span><span>86 Commits</span></span></span></a></p></td></tr><tr><td colspan="2"><p><a href="https://github.com/davila7/claude-code-templates/tree/main/.claude.backup">.claude.backup</a></p></td><td colspan="1"><p><a href="https://github.com/davila7/claude-code-templates/tree/main/.claude.backup">.claude.backup</a></p></td><td><p><a href="https://github.com/davila7/claude-code-templates/commit/97135ec48a7ba7025c1da4cb0dc3dfcfff28e9ea">Add Flask route generator and testing suite documentation</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/davila7/claude-code-templates/tree/main/.claude">.claude</a></p></td><td colspan="1"><p><a href="https://github.com/davila7/claude-code-templates/tree/main/.claude">.claude</a></p></td><td><p><a href="https://github.com/davila7/claude-code-templates/commit/51afd883ea42cfe985765b0399947b2eb790fdca">fix: Include templates in npm package to resolve missing commands issue</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/davila7/claude-code-templates/tree/main/.vscode">.vscode</a></p></td><td colspan="1"><p><a href="https://github.com/davila7/claude-code-templates/tree/main/.vscode">.vscode</a></p></td><td><p><a href="https://github.com/davila7/claude-code-templates/commit/05c4307cf78864e135f217160a405384389e48e9">fix: Update CodeGPT API key and increment package version to 1.6.3</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/davila7/claude-code-templates/tree/main/cli-tool">cli-tool</a></p></td><td colspan="1"><p><a href="https://github.com/davila7/claude-code-templates/tree/main/cli-tool">cli-tool</a></p></td><td><p><a href="https://github.com/davila7/claude-code-templates/commit/3dbeaec2e07a41f9d1d232666e36ad831e0ede52">feat: Enhance analytics dashboard with real-time conversation state d…</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/davila7/claude-code-templates/blob/main/.gitignore">.gitignore</a></p></td><td colspan="1"><p><a href="https://github.com/davila7/claude-code-templates/blob/main/.gitignore">.gitignore</a></p></td><td><p><a href="https://github.com/davila7/claude-code-templates/commit/6099ef14e6ef32b2b3cc96fabacf15190a2b9d82">first commit</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/davila7/claude-code-templates/blob/main/.mcp.json">.mcp.json</a></p></td><td colspan="1"><p><a href="https://github.com/davila7/claude-code-templates/blob/main/.mcp.json">.mcp.json</a></p></td><td><p><a href="https://github.com/davila7/claude-code-templates/commit/97135ec48a7ba7025c1da4cb0dc3dfcfff28e9ea">Add Flask route generator and testing suite documentation</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/davila7/claude-code-templates/blob/main/CLAUDE.md">CLAUDE.md</a></p></td><td colspan="1"><p><a href="https://github.com/davila7/claude-code-templates/blob/main/CLAUDE.md">CLAUDE.md</a></p></td><td><p><a href="https://github.com/davila7/claude-code-templates/commit/97135ec48a7ba7025c1da4cb0dc3dfcfff28e9ea">Add Flask route generator and testing suite documentation</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/davila7/claude-code-templates/blob/main/CODE_OF_CONDUCT.md">CODE_OF_CONDUCT.md</a></p></td><td colspan="1"><p><a href="https://github.com/davila7/claude-code-templates/blob/main/CODE_OF_CONDUCT.md">CODE_OF_CONDUCT.md</a></p></td><td><p><a href="https://github.com/davila7/claude-code-templates/commit/fb665bb4f59e9694ea446ff68efb693fceb7be24">feat: Add Code of Conduct and update contributing guidelines to ensur…</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/davila7/claude-code-templates/blob/main/CONTRIBUTING.md">CONTRIBUTING.md</a></p></td><td colspan="1"><p><a href="https://github.com/davila7/claude-code-templates/blob/main/CONTRIBUTING.md">CONTRIBUTING.md</a></p></td><td><p><a href="https://github.com/davila7/claude-code-templates/commit/fb665bb4f59e9694ea446ff68efb693fceb7be24">feat: Add Code of Conduct and update contributing guidelines to ensur…</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/davila7/claude-code-templates/blob/main/LICENSE">LICENSE</a></p></td><td colspan="1"><p><a href="https://github.com/davila7/claude-code-templates/blob/main/LICENSE">LICENSE</a></p></td><td><p><a href="https://github.com/davila7/claude-code-templates/commit/d2d3a13c62acd2eae54352a8b087f83601783a97">Complete internationalization and feature improvements for v1.1.1</a></p></td><td></td></tr><tr><td colspan="3"></td></tr></tbody></table>

**CLI tool for configuring and monitoring Claude Code** - Quick setup for any project with framework-specific commands and real-time monitoring dashboard. Open source and runs locally.

```
# Run the tool (no installation required!)

npx claude-code-templates@latest
```
[![Screenshot 2025-07-11 at 10 01 19](https://private-user-images.githubusercontent.com/6216945/*********-59dd9c74-8e84-4ed4-9bd4-cfbb88d49513.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Z7hvQfVebMSAKOJVoeJtNaFgzvwbc_yJbDqudOU-Z7Y)](https://private-user-images.githubusercontent.com/6216945/*********-59dd9c74-8e84-4ed4-9bd4-cfbb88d49513.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Z7hvQfVebMSAKOJVoeJtNaFgzvwbc_yJbDqudOU-Z7Y)

---

You'll see an interactive welcome screen:

```
██████╗██╗      █████╗ ██╗   ██╗██████╗ ███████╗
██╔════╝██║     ██╔══██╗██║   ██║██╔══██╗██╔════╝
██║     ██║     ███████║██║   ██║██║  ██║█████╗  
██║     ██║     ██╔══██║██║   ██║██║  ██║██╔══╝  
╚██████╗███████╗██║  ██║╚██████╔╝██████╔╝███████╗
 ╚═════╝╚══════╝╚═╝  ╚═╝ ╚═════╝ ╚═════╝ ╚══════╝

 ██████╗ ██████╗ ██████╗ ███████╗
██╔════╝██╔═══██╗██╔══██╗██╔════╝
██║     ██║   ██║██║  ██║█████╗  
██║     ██║   ██║██║  ██║██╔══╝  
╚██████╗╚██████╔╝██████╔╝███████╗
 ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝

████████╗███████╗███╗   ███╗██████╗ ██╗      █████╗ ████████╗███████╗███████╗
╚══██╔══╝██╔════╝████╗ ████║██╔══██╗██║     ██╔══██╗╚══██╔══╝██╔════╝██╔════╝
   ██║   █████╗  ██╔████╔██║██████╔╝██║     ███████║   ██║   █████╗  ███████╗
   ██║   ██╔══╝  ██║╚██╔╝██║██╔═══╝ ██║     ██╔══██║   ██║   ██╔══╝  ╚════██║
   ██║   ███████╗██║ ╚═╝ ██║██║     ███████╗██║  ██║   ██║   ███████╗███████║
   ╚═╝   ╚══════╝╚═╝     ╚═╝╚═╝     ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚══════╝╚══════╝

🚀 Welcome to Claude Code Templates!

¿What would you like to do?
❯ 📊 Analytics Dashboard - Monitor your Claude Code usage and sessions
  ⚙️  Project Setup - Configure Claude Code for your project
```

**Choose your path:**

- **📊 Analytics Dashboard**: View your Claude Code sessions, usage patterns, and performance metrics
- **⚙️ Project Setup**: Configure Claude Code for your project with framework-specific commands and hooks
```
# Start coding with Claude
claude
```

## Core Features

Monitor and optimize your Claude Code agents with our comprehensive analytics dashboard:

- **Live Session Tracking**: See active conversations and their status in real-time
- **Usage Statistics**: Total sessions, tokens, and project activity with trends
- **Conversation History**: Complete session logs with export capabilities (CSV/JSON)
- **Status Indicators**: PM2-style visual indicators for conversation health
- **File Watching**: Automatic updates as you work with Claude Code
- **Web Interface**: Clean, terminal-style dashboard at `http://localhost:3333`
- **Performance Monitoring**: Track Claude Code agent performance and optimization opportunities
- **Usage Patterns**: Identify your most productive coding sessions and workflows

Intelligent project setup with framework-specific commands:

- **Auto-Detection**: Automatically detect your project type and suggest optimal configurations
- **Quick Setup**: Framework-specific commands for testing, linting, building, debugging, and deployment
- **Optimized Workflows**: Pre-configured commands tailored to your development stack
- **Best Practices**: Industry-standard configurations and development patterns

### Core Files

- **`CLAUDE.md`** - Main configuration file with language-specific best practices
- **`.claude/settings.json`** - Automation hooks and Claude Code settings
- **`.claude/commands/`** - Custom commands for common development tasks
- **`.mcp.json`** - Model Context Protocol server configurations

| Language | Frameworks | Status | Commands | Hooks | MCP |
| --- | --- | --- | --- | --- | --- |
| **JavaScript/TypeScript** | React, Vue, Angular, Node.js | ✅ Ready | 7+ | 9+ | 4+ |
| **Python** | Django, Flask, FastAPI | ✅ Ready | 5+ | 8+ | 4+ |
| **Common** | Universal | ✅ Ready | 2+ | 1+ | 4+ |
| **Go** | Gin, Echo, Fiber | 🚧 Coming Soon | \- | \- | \- |
| **Rust** | Axum, Warp, Actix | 🚧 Coming Soon | \- | \- | \- |

### Automation Hooks

Execute at key moments during Claude Code workflow:

- **PreToolUse**: Security checks, logging, statement detection
- **PostToolUse**: Auto-formatting, type checking, testing
- **Stop**: Final linting, bundle analysis
- **Notification**: Activity logging and monitoring

### MCP Integration

Extend Claude Code with specialized capabilities:

- **IDE Integration**: VS Code diagnostics & Jupyter execution
- **Web Search**: Real-time information retrieval
- **Database Tools**: PostgreSQL, MySQL connections
- **Development Tools**: Docker, GitHub, filesystem operations

### Analysis Tools

Analyze and optimize your existing Claude Code configuration:

#### Command Analysis

```
# View detailed command statistics
npx claude-code-templates --commands-stats
```

**What you get:**

- Command name, file size, and token count
- Lines, words, and last modified date
- AI-powered optimization recommendations
- Project-specific improvement suggestions

#### Hook Analysis

```
# Analyze automation hooks configuration
npx claude-code-templates --hooks-stats
```

**What you get:**

- Hook name, type, and status (enabled/disabled)
- Hook descriptions and purpose
- Hook summary by type (PreToolUse, PostToolUse, etc.)
- AI-powered hook optimization suggestions
- Missing hook recommendations for your workflow
```
# Analyze MCP server configurations
npx claude-code-templates --mcps-stats
```

**What you get:**

- Server name, category, and status (enabled/disabled)
- Command, complexity rating, and descriptions
- Server summary by category (IDE, Database, Web, etc.)
- AI-powered MCP configuration optimization
- Missing server recommendations for your workflow

## Usage Examples

```
cd my-react-app
npx claude-code-templates
# Auto-detects React and suggests optimal configuration
```
```
# React + TypeScript project
npx claude-code-templates --language javascript-typescript --framework react --yes

# Python + Django project
npx claude-code-templates --language python --framework django --yes
```

### Advanced Options

```
# Preview installation without making changes
npx claude-code-templates --dry-run

# Skip all prompts and use defaults
npx claude-code-templates --yes

# Install to custom directory
npx claude-code-templates --directory /path/to/project

# Analyze existing commands 
npx claude-code-templates --commands-stats

# Analyze automation hooks
npx claude-code-templates --hooks-stats

# Analyze MCP server configurations 
npx claude-code-templates --mcps-stats

# Launch real-time analytics dashboard
npx claude-code-templates --analytics
npx cct --analytics
```

### Alternative Commands

All these commands work exactly the same way:

```
npx cctemplates              # Claude Code Templates
npx cct                      # ⚡ Super short (3 letters)
```

## Safety Features

- **Automatic Backups**: Existing files are backed up before changes
- **Confirmation Required**: Always asks before making changes (unless `--yes` flag)
- **Dry Run Mode**: Preview installation with `--dry-run`
- **Cancel Anytime**: Press Ctrl+C or answer 'No' to cancel
- **Back Navigation**: Modify previous selections during setup
- Hours of configuration research
- Manual CLAUDE.md creation
- Framework-specific command setup
- Automation hook configuration
- MCP server integration
```
npx claude-code-templates --language javascript-typescript --framework react --yes
# ✅ Done in 30 seconds!
```

### CLI Options

| Option | Description | Example |
| --- | --- | --- |
| `-l, --language` | Specify programming language | `--language python` |
| `-f, --framework` | Specify framework | `--framework react` |
| `-d, --directory` | Target directory | `--directory /path/to/project` |
| `-y, --yes` | Skip prompts and use defaults | `--yes` |
| `--dry-run` | Show what would be installed | `--dry-run` |
| `--command-stats, --commands-stats` | Analyze existing commands | `--command-stats` |
| `--hook-stats, --hooks-stats` | Analyze automation hooks | `--hook-stats` |
| `--mcp-stats, --mcps-stats` | Analyze MCP server configurations | `--mcp-stats` |
| `--analytics` | Launch real-time analytics dashboard | `--analytics` |
| `--help` | Show help information | `--help` |

- **Community-Driven**: Built by developers, for developers
- **Always Updated**: Latest best practices and framework support
- **Extensible**: Easy to add new languages and frameworks
- **Transparent**: All code is open and auditable
- **Free Forever**: MIT license, no vendor lock-in

## 🤝 Contributing

We welcome contributions from the open source community! This project thrives on community input and collaboration.

**Please read our [Code of Conduct](https://github.com/davila7/claude-code-templates/blob/main/CODE_OF_CONDUCT.md) before contributing to ensure a welcoming environment for everyone.****See [CONTRIBUTING.md](https://github.com/davila7/claude-code-templates/blob/main/CONTRIBUTING.md) for detailed guidelines**

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](https://github.com/davila7/claude-code-templates/blob/main/LICENSE) file for details.

This project is built by the community, for the community. We believe in:

- **🌍 Open Collaboration**: Everyone can contribute and improve the project
- **🔄 Knowledge Sharing**: Share best practices and learn from others
- **🤝 Inclusive Environment**: Welcome developers of all skill levels
- **📈 Continuous Improvement**: Always evolving with community feedback
- **🆓 Free Forever**: MIT license ensures it stays open and free

### Recognition

- **Contributors**: All contributors are recognized in our GitHub contributors page
- **Community**: Join discussions and help others in GitHub Discussions
- **Star History**: Show your support by starring the repository

## 📞 Support

- **🐛 Issues**: [Report bugs or request features](https://github.com/davila7/claude-code-templates/issues)
- **💬 Discussions**: [Join community discussions](https://github.com/davila7/claude-code-templates/discussions)
- **🔒 Security**: [Report security vulnerabilities](https://github.com/davila7/claude-code-templates/blob/main/SECURITY.md)
- **📖 Documentation**: [Claude Code Official Docs](https://docs.anthropic.com/en/docs/claude-code)
- **🤝 Contributing**: [Read our contribution guidelines](https://github.com/davila7/claude-code-templates/blob/main/CONTRIBUTING.md)

---

[![Star History Chart](https://camo.githubusercontent.com/214d9e4514f3258e2b38cc27f8bb375b11c4053f7648ce21d2f356a6882f0e76/68747470733a2f2f6170692e737461722d686973746f72792e636f6d2f7376673f7265706f733d646176696c61372f636c617564652d636f64652d74656d706c6174657326747970653d44617465)](https://star-history.com/#davila7/claude-code-templates&Date)

**⭐ Found this useful? Give us a star on GitHub to support the project!**

## Releases

No releases published

## Packages

No packages published