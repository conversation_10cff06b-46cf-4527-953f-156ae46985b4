---
created: 2025-07-03
tags:
  - AI/DependencyManagement
  - AI/UV
source: 
author: 
Reference:
---

# Dependency Management Options for Nested Git Repository

## Situation
- **Parent Project**: `readpal` (uses uv for dependency management)
- **Nested Repository**: `fish-speech` (also uses uv, located at `/opt/app/tools/readpal/fish-speech/`)
- **Challenge**: Both projects have their own `pyproject.toml` and dependencies

## Option 1: Separate Virtual Environments (Recommended)

Install fish-speech dependencies in a separate virtual environment to keep dependencies isolated.

### Steps:
```bash
# Navigate to the fish-speech directory
cd fish-speech

# Install dependencies using uv with Python 3.12 as specified in their docs
uv sync --python 3.12

# This will create a separate .venv directory inside fish-speech
```

### Pros:
- ✅ Keeps dependencies isolated
- ✅ Follows fish-speech's installation instructions exactly
- ✅ Prevents dependency conflicts between the two projects
- ✅ Allows each project to use different Python versions if needed
- ✅ Clean separation of concerns

### Cons:
- ❌ Need to manage two separate environments
- ❌ May need to switch environments when working across projects

## Option 2: UV Workspaces (Advanced)

Configure the parent project to manage both as a workspace by modifying the parent `pyproject.toml`.

### Steps:
```bash
# Add to readpal's pyproject.toml
[tool.uv.workspace]
members = [".", "fish-speech"]
```

### Pros:
- ✅ Single workspace management
- ✅ Unified dependency resolution
- ✅ Easier to manage from parent project

### Cons:
- ❌ Potential dependency conflicts between projects
- ❌ More complex setup
- ❌ May require resolving version incompatibilities
- ❌ Both projects have overlapping but potentially incompatible dependencies

## Option 3: Editable Local Dependency

Install fish-speech as an editable dependency in the parent readpal project.

### Steps:
```bash
# From the readpal root directory
uv add -e ./fish-speech
```
   above way NOT working
   cd fish-speech
   uv pip install -e .

### Pros:
- ✅ fish-speech becomes part of readpal's dependency tree
- ✅ Easy to import and use fish-speech modules
- ✅ Single environment to manage

### Cons:
- ❌ Dependency conflicts likely due to overlapping requirements
- ❌ May override fish-speech's specific version requirements
- ❌ Less control over fish-speech's environment

## Recommendation

**Option 1 (Separate Virtual Environments)** is recommended because:

1. **fish-speech** appears to be a complete, standalone TTS system with specific requirements
2. **readpal** project already has many TTS-related dependencies that might conflict
3. The fish-speech documentation specifically mentions using `uv sync --python 3.12`
4. Keeping them separate allows you to activate the fish-speech environment only when needed
5. Maintains the integrity of both projects' dependency specifications

## Usage Pattern with Option 1

```bash
# Working on readpal
cd /opt/app/tools/readpal
uv run python your_readpal_script.py

# Working with fish-speech
cd /opt/app/tools/readpal/fish-speech
uv run python tools/api_server.py  # or other fish-speech tools
```

## Next Steps

1. Choose the preferred option based on your integration needs
2. Test the chosen approach with a simple script
3. Document any integration patterns between the two projects
