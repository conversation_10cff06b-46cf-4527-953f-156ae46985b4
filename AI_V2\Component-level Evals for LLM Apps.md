---
created: 2025-07-01
tags:
  - AI/Performance
  - AI/Tools
source:
author:
Reference:
---

# Component-level Evals for LLM Apps

[blog.dailydoseofds.com](https://blog.dailydoseofds.com/p/component-level-evals-for-llm-apps) A<PERSON>

**In today's newsletter:**

* ​Build any MCP server in two steps​.

* ​Component-level evals for LLM apps​.

* KV caching in LLM, explained visually.

### **[Build any MCP server in two steps](https://www.factory.ai/)** [​](https://www.factory.ai/)

Here's the easiest way to build any MCP server:

* Download the FastMCP repo with GitIngest.

* Give it to [​](https://www.factory.ai/)**[FactoryAI](https://www.factory.ai/)** [​](https://www.factory.ai/) and specify the MCP server to build.

Factory's Droids handle the entire workflow to generate production-ready code with README, usage, error-handling---everything!

[Build with Factory!](https://www.factory.ai/)

Here's one of our test runs where we asked the Droids to build a stock analysis MCP server in Factory:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21o3yZ%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F9a3bb02f-c0e8-4b71-a729-d597c27fa499_1232x732.png&valid=true)](https://substackcdn.com/image/fetch/$s_!o3yZ!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F9a3bb02f-c0e8-4b71-a729-d597c27fa499_1232x732.png)

And it did it perfectly with zero errors, while creating a README and usage guide, and implementing error-handling, without asking:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21mgw3%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F3b5d1cc8-437a-4e2f-afa6-9997c737eaf5_1228x752.png&valid=true)](https://substackcdn.com/image/fetch/$s_!mgw3!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F3b5d1cc8-437a-4e2f-afa6-9997c737eaf5_1228x752.png)

[​](https://www.factory.ai/)**[Build your own MCP server here →](https://www.factory.ai/)**

### [​](https://github.com/confident-ai/deepeval)**[Component-level Evals for LLM apps](https://github.com/confident-ai/deepeval)** [​](https://github.com/confident-ai/deepeval)

Most LLM evals treat the app like a black box.

Feed the input → Get the output → Run evals on the overall end-to-end system.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%218Hep%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fd5ceda8d-e368-4819-8936-6562be219dfe_1416x314.gif&valid=true)](https://substackcdn.com/image/fetch/$s_!8Hep!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fd5ceda8d-e368-4819-8936-6562be219dfe_1416x314.gif)

But LLM apps need **[component-level evals](https://deepeval.com/docs/evaluation-component-level-llm-evals)** and tracing since the issue can be anywhere inside the box, like the retriever, tool call, or the LLM itself.

In **[DeepEval](https://github.com/confident-ai/deepeval)** (open-source), you can do that in just three steps:

* Trace individual LLM components (tools, retrievers, generators) with the `@observe` decorator.

* Attach different metrics to each part.

* Get a visual breakdown of what's working on a test-case-level and component-level.

See the example below for a RAG app:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21ctcE%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F7d97e640-d0a2-4ad3-9071-618183a6ee32_1182x1038.gif&valid=true)](https://substackcdn.com/image/fetch/$s_!ctcE!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F7d97e640-d0a2-4ad3-9071-618183a6ee32_1182x1038.gif)

Here's a quick explanation:

* Start with some standard import statements:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21HvSX%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F382f5ef1-e00a-4c32-bd15-22115b5f3dbe_2600x1240.png&valid=true)](https://substackcdn.com/image/fetch/$s_!HvSX!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F382f5ef1-e00a-4c32-bd15-22115b5f3dbe_2600x1240.png)

* Define your LLM app in a method decorated with the `@observe` decorator:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21Bj7l%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fcd0b1de4-5b44-4bee-9769-2619d391ca69_2208x1168.png&valid=true)](https://substackcdn.com/image/fetch/$s_!Bj7l!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fcd0b1de4-5b44-4bee-9769-2619d391ca69_2208x1168.png)

* Next, attach component-level metrics to each component you want to trace:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21-EG5%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F3bf2a836-f313-4447-93e4-b85da090e431_3016x1972.png&valid=true)](https://substackcdn.com/image/fetch/$s_!-EG5!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F3bf2a836-f313-4447-93e4-b85da090e431_3016x1972.png)

Done!

Finally, we define some test cases and run component-level evals on the LLM app:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21ptM5%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F9a6028a1-5365-49b9-b620-e86b0b6a2d8f_3024x1664.png&valid=true)](https://substackcdn.com/image/fetch/$s_!ptM5!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F9a6028a1-5365-49b9-b620-e86b0b6a2d8f_3024x1664.png)

This produces an evaluation report:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21_qYk%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fb0b2b229-b5fe-4137-a887-24366af757b0_1208x900.png&valid=true)](https://substackcdn.com/image/fetch/$s_!_qYk!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fb0b2b229-b5fe-4137-a887-24366af757b0_1208x900.png)

You can also inspect individual tests to understand why they failed/passed:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21sZ3I%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F68ace38d-f2bb-4bad-a4c5-9e28a2c79701_1208x732.png&valid=true)](https://substackcdn.com/image/fetch/$s_!sZ3I!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F68ace38d-f2bb-4bad-a4c5-9e28a2c79701_1208x732.png)

There are two good things about this:

* You don't have to refactor any of your existing LLM app's code.

* DeepEval is 100% open-source with 8500+ stars, and you can easily self-host it so your data stays where you want.

**[Here's the GitHub repo →](https://github.com/confident-ai/deepeval)**

**[You can read about component-level evals in the documentation here →](https://deepeval.com/docs/evaluation-component-level-llm-evals)**

### [​](https://www.dailydoseofds.com/p/kv-caching-in-llms-explained-visually/)**[KV caching in LLMs, explained visually](https://www.dailydoseofds.com/p/kv-caching-in-llms-explained-visually/)** [​](https://www.dailydoseofds.com/p/kv-caching-in-llms-explained-visually/)

KV caching is a popular technique to speed up LLM inference.

To get some perspective, look at the inference speed difference from our demo:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21qoXP%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F66896d1f-4a6a-4c2d-b5c7-b98e00be165f_1247x718.png&valid=true)](https://substackcdn.com/image/fetch/$s_!qoXP!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F66896d1f-4a6a-4c2d-b5c7-b98e00be165f_1247x718.png)

* with KV caching → 9 seconds

* without KV caching → 40 seconds (\~4.5x slower, and this gap grows as more tokens are produced).

The visual explains how it works:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21fdYa%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_lossy%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fb750d48e-ae4b-4a2c-8a57-e4287d1833eb_996x1016.gif&valid=true)](https://substackcdn.com/image/fetch/$s_!fdYa!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fb750d48e-ae4b-4a2c-8a57-e4287d1833eb_996x1016.gif)

[​](https://www.dailydoseofds.com/p/kv-caching-in-llms-explained-visually/)**[We covered this in detail in a recent issue here →](https://www.dailydoseofds.com/p/kv-caching-in-llms-explained-visually/)**

### **P.S. For those wanting to develop "Industry ML" expertise:**

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21cn8y%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F939bede7-b0de-4770-a3e9-34d39488e776_2733x1020.png&valid=true)](https://substackcdn.com/image/fetch/$s_!cn8y!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F939bede7-b0de-4770-a3e9-34d39488e776_2733x1020.png)

At the end of the day, all businesses care about *impact* . That's it!

* Can you reduce costs?

* Drive revenue?

* Can you scale ML models?

* Predict trends before they happen?

We have discussed several other topics (with implementations) that align with such topics.

[Develop "Industry ML" Skills](https://www.dailydoseofds.com/membership)

Here are some of them:

* Learn how to build Agentic systems in **[a crash course with 14 parts](https://www.dailydoseofds.com/ai-agents-crash-course-part-1-with-implementation/)** .

* Learn how to build real-world RAG apps and evaluate and scale them in **[this crash course](https://www.dailydoseofds.com/a-crash-course-on-building-rag-systems-part-1-with-implementations/)** .

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21HcHn%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fca6f5be8-3aa0-4754-8087-1929397898e6_697x920.png&valid=true)](https://substackcdn.com/image/fetch/$s_!HcHn!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fca6f5be8-3aa0-4754-8087-1929397898e6_697x920.png)

* Learn sophisticated graph architectures and how to train them on graph data.

* So many real-world NLP systems rely on pairwise context scoring. Learn scalable approaches **[here](https://www.dailydoseofds.com/bi-encoders-and-cross-encoders-for-sentence-pair-similarity-scoring-part-1/)** .

* Learn how to run large models on small devices using [​](https://www.dailydoseofds.com/quantization-optimize-ml-models-to-run-them-on-tiny-hardware/)**[Quantization techniques](https://www.dailydoseofds.com/quantization-optimize-ml-models-to-run-them-on-tiny-hardware/)** .

* Learn how to generate prediction intervals or sets with strong statistical guarantees for increasing trust using [​](https://www.dailydoseofds.com/conformal-predictions-build-confidence-in-your-ml-models-predictions/)**[Conformal Predictions](https://www.dailydoseofds.com/conformal-predictions-build-confidence-in-your-ml-models-predictions/)** .

* Learn how to identify causal relationships and answer business questions using causal inference in **[this crash course](https://www.dailydoseofds.com/a-crash-course-on-causality-part-1/)** .

* Learn how to scale and implement ML model training in this **[practical guide](https://www.dailydoseofds.com/how-to-scale-model-training/)** .

* Learn techniques to reliably **[test new models in production](https://www.dailydoseofds.com/5-must-know-ways-to-test-ml-models-in-production-implementation-included/)** .

* Learn how to build privacy-first ML systems using [​](https://www.dailydoseofds.com/federated-learning-a-critical-step-towards-privacy-preserving-machine-learning/)**[Federated Learning](https://www.dailydoseofds.com/federated-learning-a-critical-step-towards-privacy-preserving-machine-learning/)** .

* Learn 6 techniques with implementation to **[compress ML models](https://www.dailydoseofds.com/model-compression-a-critical-step-towards-efficient-machine-learning/)** .

All these resources will help you cultivate key skills that businesses and companies care about the most.

[Read in Cubox](https://cubox.cc/my/card?id=7339612652785634758)
