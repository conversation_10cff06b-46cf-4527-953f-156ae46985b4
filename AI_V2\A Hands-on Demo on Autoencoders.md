---
created: 2025-07-11
tags:
  - AI/Pytorch
  - AI/FineTuning
source:
author:
Reference:
---

# A Hands-on Demo on Autoencoders

[blog.dailydoseofds.com](https://blog.dailydoseofds.com/p/a-hands-on-demo-on-autoencoders) Avi <PERSON>wla

### [A Python decorator is all you need to deploy AI apps \[open-source\]](https://github.com/beam-cloud/beta9)

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21PUeY%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fcab46d57-c3e0-4042-b6d3-de1ae783d5bb_1220x1156.png&valid=true)](https://github.com/beam-cloud/beta9)

**[Beam](https://github.com/beam-cloud/beta9)** is an open-source alternative to Modal that makes deploying serverless AI workloads effortless with zero infrastructure overhead.

Just `pip install beam` and add a decorator to turn any function into a serverless endpoint!

Key features:

* Lightning-fast container launches \< 1s

* Distributed volume storage support

* Auto-scales from 0 to 100s of containers

* GPU support (4090s, H100s, or bring your own)

* Deploy inference endpoints with simple decorators

* Spin up isolated sandboxes for LLM-generated code

Completely open-source!

**[Beam GitHub repo →](https://github.com/beam-cloud/beta9)** (don't forget to star)

[Beam GitHub repo](https://github.com/beam-cloud/beta9)

### A Hands-on Demo of Autoencoders

One can do so many things with Autoencoders:

* Dimensionality reduction.减少维度。

* Anomaly detection where if reconstruction error is high, something's fishy!异常检测，如果重建错误很高，那就有些可变！

* Detect multivariate covariate shift, which we discussed **[here](https://www.dailydoseofds.com/8-fatal-yet-non-obvious-pitfalls-and-cautionary-measures-in-data-science/)** .检测多元协变量转移，我们在此处讨论。

* Data denoising (clean noisy data by training on noise).数据denoising（通过噪声培训训练清洁嘈杂的数据）。

They are simple yet so powerful!

At their core, Autoencoders have two main parts:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%211R1E%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F68a1433a-91bf-4cfa-b581-85cd7487d7a5_1456x846.png&valid=true)](https://substackcdn.com/image/fetch/$s_!1R1E!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F68a1433a-91bf-4cfa-b581-85cd7487d7a5_1456x846.png)

1. Encoder: Compresses the input into a dense representation (latent space).

2. Decoder: Reconstructs the input from this dense representation.

And the idea is to make the reconstructed output as close to the original input as possible:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21IEN2%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fd04a5cca-2d32-4e76-9be7-fb2b10f9c601_1456x394.png&valid=true)](https://substackcdn.com/image/fetch/$s_!IEN2!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fd04a5cca-2d32-4e76-9be7-fb2b10f9c601_1456x394.png)

On a side note, here's how the denoising autoencoder works:

* During training, add random noise to the original input, and train an autoencoder to predict the original input.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21fm0X%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F6fa16265-1060-4adf-bd93-3b0c6a6c43f8_1456x524.png&valid=true)](https://substackcdn.com/image/fetch/$s_!fm0X!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F6fa16265-1060-4adf-bd93-3b0c6a6c43f8_1456x524.png)

* During inference, provide a noisy input and the network will reconstruct it.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%212l-t%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fcd68f3be-60d4-444e-b621-f60ee232ada7_1456x500.png&valid=true)](https://substackcdn.com/image/fetch/$s_!2l-t!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fcd68f3be-60d4-444e-b621-f60ee232ada7_1456x500.png)

Coming back to the topic...

Below, let's quickly implement an autoencoder:

We'll use [​](https://www.dailydoseofds.com/a-detailed-and-beginner-friendly-introduction-to-pytorch-lightning-the-supercharged-pytorch/)**[PyTorch Lightning](https://www.dailydoseofds.com/a-detailed-and-beginner-friendly-introduction-to-pytorch-lightning-the-supercharged-pytorch/)** for this.

First, we define our autoencoder!

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%213On-%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F3c38f042-355c-46ba-b69a-6a8c5aa08982_1456x1667.png&valid=true)](https://substackcdn.com/image/fetch/$s_!3On-!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F3c38f042-355c-46ba-b69a-6a8c5aa08982_1456x1667.png)

Next, we define our dataset (MNIST):

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21VdSx%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fa883852f-f37c-401b-a8f3-ce234e648dbf_1456x424.png&valid=true)](https://substackcdn.com/image/fetch/$s_!VdSx!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fa883852f-f37c-401b-a8f3-ce234e648dbf_1456x424.png)

Finally, we train the model in two lines of code with [​](https://www.dailydoseofds.com/a-detailed-and-beginner-friendly-introduction-to-pytorch-lightning-the-supercharged-pytorch/)**[PyTorch Lightning](https://www.dailydoseofds.com/a-detailed-and-beginner-friendly-introduction-to-pytorch-lightning-the-supercharged-pytorch/)** [​](https://www.dailydoseofds.com/a-detailed-and-beginner-friendly-introduction-to-pytorch-lightning-the-supercharged-pytorch/):

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21mxJe%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F10012070-5ce3-45b8-b617-f6d21a6b0f7f_1456x416.png&valid=true)](https://substackcdn.com/image/fetch/$s_!mxJe!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F10012070-5ce3-45b8-b617-f6d21a6b0f7f_1456x416.png)

The Lightning Trainer automates 40+ training optimization techniques including:

* Epoch and batch iteration

* `optimizer.step()`, `loss.backward()` etc.

* Calling `model.eval()`

* enabling/disabling grads during evaluation

* Checkpoint saving and loading

* Multi-GPU

* 16-bit precision.

Since the model has been trained, we can visualize the performance.

Let's encode/decode an image using our trained model below:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21hiIZ%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F66cdc8c9-cc9d-400f-8113-8ccfd07b2d52_1456x1014.png&valid=true)](https://substackcdn.com/image/fetch/$s_!hiIZ!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F66cdc8c9-cc9d-400f-8113-8ccfd07b2d52_1456x1014.png)

Done!

And that's how you train an autoencoder.

As mentioned earlier, autoencoders are incredibly helpful in detecting multivariate covariate shifts.

This is important to address since almost all real-world ML models gradually degrade in performance due to covariate shift.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21n2WD%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F773a682f-b707-4a7b-8456-5f1678db8d9c_1000x408.png&valid=true)](https://substackcdn.com/image/fetch/$s_!n2WD!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F773a682f-b707-4a7b-8456-5f1678db8d9c_1000x408.png)

It is a serious problem because we trained the model on one distribution, but it is being used to predict on another distribution in production.

Autoencoders help in addressing this, and we discussed it **[here](https://www.dailydoseofds.com/8-fatal-yet-non-obvious-pitfalls-and-cautionary-measures-in-data-science/#8-multivariate-covariate-shift)** .

👉 Over to you: What are some other use cases of Autoencoder?

### \[Hands-on\] ​Build a Custom MCP Server for Cursor​

Lately, there has been a lot of interest in MCPs.

Here's a step-by-step video walkthrough of creating your own MCP server!

It connects to Cursor and lets the Agent perform deep web searches and RAG over a specified directory.

Cursor IDE is our MCP host, and we connect it to our custom server.

Here's the tech stack:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21LYof%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_lossy%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fcfef9b95-533f-4633-944a-2f03c7c7f680_1280x866.gif&valid=true)](https://substackcdn.com/image/fetch/$s_!LYof!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fcfef9b95-533f-4633-944a-2f03c7c7f680_1280x866.gif)

* Linkup for deep web search.

* Llama Index workflow to enable RAG.

[​](https://github.com/patchy631/ai-engineering-hub/tree/main/cursor_linkup_mcp)**[Find the code on GitHub here →](https://github.com/patchy631/ai-engineering-hub/tree/main/cursor_linkup_mcp)** [​](https://github.com/patchy631/ai-engineering-hub/tree/main/cursor_linkup_mcp)**(don't forget to star the repo)​**

Thanks for reading!

### **P.S. For those wanting to develop "Industry ML" expertise:**

[![](https://images.cubox.cc/cardImg/n2pch3bqdykaytf8bwv5csa9mnyfttprxwnx09awev93p89l9.png)](https://substackcdn.com/image/fetch/$s_!cn8y!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F939bede7-b0de-4770-a3e9-34d39488e776_2733x1020.png)

At the end of the day, all businesses care about *impact* . That's it!

* Can you reduce costs?

* Drive revenue?

* Can you scale ML models?

* Predict trends before they happen?

We have discussed several other topics (with implementations) that align with such topics.

[Develop "Industry ML" Skills](https://www.dailydoseofds.com/membership)

Here are some of them:

* Learn how to build Agentic systems in **[a crash course with 14 parts](https://www.dailydoseofds.com/ai-agents-crash-course-part-1-with-implementation/)** .

* Learn how to build real-world RAG apps and evaluate and scale them in **[this crash course](https://www.dailydoseofds.com/a-crash-course-on-building-rag-systems-part-1-with-implementations/)** .

[![](https://images.cubox.cc/cardImg/4spzixbq1vbvruz237umfz8x54m7osmy95qg7lwse4bg8723br.png)](https://substackcdn.com/image/fetch/$s_!HcHn!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fca6f5be8-3aa0-4754-8087-1929397898e6_697x920.png)

* Learn sophisticated graph architectures and how to train them on graph data.

* So many real-world NLP systems rely on pairwise context scoring. Learn scalable approaches **[here](https://www.dailydoseofds.com/bi-encoders-and-cross-encoders-for-sentence-pair-similarity-scoring-part-1/)** .

* Learn how to run large models on small devices using [​](https://www.dailydoseofds.com/quantization-optimize-ml-models-to-run-them-on-tiny-hardware/)**[Quantization techniques](https://www.dailydoseofds.com/quantization-optimize-ml-models-to-run-them-on-tiny-hardware/)** .

* Learn how to generate prediction intervals or sets with strong statistical guarantees for increasing trust using [​](https://www.dailydoseofds.com/conformal-predictions-build-confidence-in-your-ml-models-predictions/)**[Conformal Predictions](https://www.dailydoseofds.com/conformal-predictions-build-confidence-in-your-ml-models-predictions/)** .

* Learn how to identify causal relationships and answer business questions using causal inference in **[this crash course](https://www.dailydoseofds.com/a-crash-course-on-causality-part-1/)** .

* Learn how to scale and implement ML model training in this **[practical guide](https://www.dailydoseofds.com/how-to-scale-model-training/)** .

* Learn techniques to reliably **[test new models in production](https://www.dailydoseofds.com/5-must-know-ways-to-test-ml-models-in-production-implementation-included/)** .

* Learn how to build privacy-first ML systems using [​](https://www.dailydoseofds.com/federated-learning-a-critical-step-towards-privacy-preserving-machine-learning/)**[Federated Learning](https://www.dailydoseofds.com/federated-learning-a-critical-step-towards-privacy-preserving-machine-learning/)** .

* Learn 6 techniques with implementation to **[compress ML models](https://www.dailydoseofds.com/model-compression-a-critical-step-towards-efficient-machine-learning/)** .

All these resources will help you cultivate key skills that businesses and companies care about the most.

[Read in Cubox](https://cubox.cc/my/card?id=7343273833723332481)
