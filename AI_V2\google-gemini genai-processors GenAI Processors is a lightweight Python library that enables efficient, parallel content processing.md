---
title: "google-gemini/genai-processors: GenAI Processors is a lightweight Python library that enables efficient, parallel content processing."
source: https://github.com/google-gemini/genai-processors
author:
  - "[[kiber<PERSON>]]"
published:
created: 2025-07-11
description: GenAI Processors is a lightweight Python library that enables efficient, parallel content processing. - google-gemini/genai-processors
tags:
  - AI/Google
reference:
---
**[genai-processors](https://github.com/google-gemini/genai-processors)** Public

GenAI Processors is a lightweight Python library that enables efficient, parallel content processing.

[Apache-2.0 license](https://github.com/google-gemini/genai-processors/blob/main/LICENSE)

[Security policy](https://github.com/google-gemini/.github/blob/main/SECURITY.md)

[510 stars](https://github.com/google-gemini/genai-processors/stargazers) [53 forks](https://github.com/google-gemini/genai-processors/forks) [5 watching](https://github.com/google-gemini/genai-processors/watchers) [Branches](https://github.com/google-gemini/genai-processors/branches) [Tags](https://github.com/google-gemini/genai-processors/tags) [Activity](https://github.com/google-gemini/genai-processors/activity) [Custom properties](https://github.com/google-gemini/genai-processors/custom-properties)

Public repository

[Open in github.dev](https://github.dev/) [Open in a new github.dev tab](https://github.dev/) [Open in codespace](https://github.com/codespaces/new/google-gemini/genai-processors?resume=1)

<table><thead><tr><th colspan="2"><span>Name</span></th><th colspan="1"><span>Name</span></th><th><p><span>Last commit message</span></p></th><th colspan="1"><p><span>Last commit date</span></p></th></tr></thead><tbody><tr><td colspan="3"><p><span><a href="https://github.com/google-gemini/genai-processors/commit/abe70d2c1afb4d5035965091f2052d436c397373">Fix processor.source() invocation in colab.</a></span></p><p><span><a href="https://github.com/google-gemini/genai-processors/commit/abe70d2c1afb4d5035965091f2052d436c397373">abe70d2</a> ·</span></p><p><a href="https://github.com/google-gemini/genai-processors/commits/main/"><span><span><span>70 Commits</span></span></span></a></p></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/genai-processors/tree/main/.github/workflows"><span>.github/</span> <span>workflows</span></a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/genai-processors/tree/main/.github/workflows"><span>.github/</span> <span>workflows</span></a></p></td><td><p><a href="https://github.com/google-gemini/genai-processors/commit/ff5252635f8e58bbbde0f59562748a42fb92ad3a">Update required Python version. We use ExceptionGroup which has been …</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/genai-processors/tree/main/examples">examples</a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/genai-processors/tree/main/examples">examples</a></p></td><td><p><a href="https://github.com/google-gemini/genai-processors/commit/bcf526c88ff73b718f3021f4f4056713c002518e">Add core.text.terminal_input which yields lines from a terminal.</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/genai-processors/tree/main/genai_processors">genai_processors</a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/genai-processors/tree/main/genai_processors">genai_processors</a></p></td><td><p><a href="https://github.com/google-gemini/genai-processors/commit/abe70d2c1afb4d5035965091f2052d436c397373">Fix processor.source() invocation in colab.</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/genai-processors/tree/main/notebooks">notebooks</a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/genai-processors/tree/main/notebooks">notebooks</a></p></td><td><p><a href="https://github.com/google-gemini/genai-processors/commit/abe70d2c1afb4d5035965091f2052d436c397373">Fix processor.source() invocation in colab.</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/genai-processors/blob/main/CONTRIBUTING.md">CONTRIBUTING.md</a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/genai-processors/blob/main/CONTRIBUTING.md">CONTRIBUTING.md</a></p></td><td><p><a href="https://github.com/google-gemini/genai-processors/commit/feffe0696863fa6998be5e0cf5bd3a82853a08ef">Internal</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/genai-processors/blob/main/LICENSE">LICENSE</a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/genai-processors/blob/main/LICENSE">LICENSE</a></p></td><td><p><a href="https://github.com/google-gemini/genai-processors/commit/bbd0f3b7d3c3d90a69386a9f8ee16c0942ee691a">Remove extra punctuation in license file.</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/genai-processors/blob/main/README.md">README.md</a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/genai-processors/blob/main/README.md">README.md</a></p></td><td><p><a href="https://github.com/google-gemini/genai-processors/commit/60eade2ab87706f4876d3abd536ff1105f87c080">Update README.md</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/genai-processors/blob/main/README.pypi.md">README.pypi.md</a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/genai-processors/blob/main/README.pypi.md">README.pypi.md</a></p></td><td><p><a href="https://github.com/google-gemini/genai-processors/commit/f290e6eaee0d56626b070ba8a544ecd07e30f5ab">Add missing PyPI README referenced from pyproject.toml.</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/genai-processors/blob/main/pyproject.toml">pyproject.toml</a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/genai-processors/blob/main/pyproject.toml">pyproject.toml</a></p></td><td><p><a href="https://github.com/google-gemini/genai-processors/commit/1be27e58930eebcc9d29ec5658b76cf392c3665d">Remove paudio_io_test.py and pyaudio dependency as it is hard to inst…</a></p></td><td></td></tr><tr><td colspan="3"></td></tr></tbody></table>

**Build Modular, Asynchronous, and Composable AI Pipelines for Generative AI.**

GenAI Processors is a lightweight Python library that enables efficient, parallel content processing.

At the core of the GenAI Processors library lies the concept of a `Processor`. A `Processor` encapsulates a unit of work with a simple API: it takes a stream of `ProcessorPart` s (i.e. a data part representing a text, image, etc.) as input and returns a stream of `ProcessorPart` s (or compatible types) as output.

```
# Any class inheriting from processor.Processor and
# implementing this function is a processor.
async def call(
  content: AsyncIterable[ProcessorPart]
) -> AsyncIterable[ProcessorPartTypes]
```

You can apply a `Processor` to any input stream and easily iterate through its output stream:

```
from genai_processors import content_api
from genai_processors import streams

# Create an input stream (strings are automatically cast into Parts).
input_parts = ["Hello", content_api.ProcessorPart("World")]
input_stream = streams.stream_content(input_parts)

# Apply a processor to a stream of parts and iterate over the result
async for part in simple_text_processor(input_stream):
  print(part.text)
...
```

The concept of `Processor` provides a common abstraction for Gemini model calls and increasingly complex behaviors built around them, accommodating both turn-based interactions and live streaming.

- **Modular**: Breaks down complex tasks into reusable `Processor` and `PartProcessor` units, which are easily chained (`+`) or parallelized (`//`) to create sophisticated data flows and agentic behaviors.
- **Integrated with GenAI API**: Includes ready-to-use processors like `GenaiModel` for turn-based API calls and `LiveProcessor` for real-time streaming interactions.
- **Extensible**: Lets you create custom processors by inheriting from base classes or using simple function decorators.
- **Rich Content Handling**:
	- `ProcessorPart`: A wrapper around `genai.types.Part` enriched with metadata like MIME type, role, and custom attributes.
	- Supports various content types (text, images, audio, custom JSON).
- **Asynchronous & Concurrent**: Built on Python's familiar `asyncio` framework to orchestrate concurrent tasks (including network I/O and communication with compute-heavy subthreads).
- **Stream Management**: Has utilities for splitting, concatenating, and merging asynchronous streams of `ProcessorPart` s.

## 📦 Installation

The GenAI Processors library requires Python 3.10+.

Install it with:

```
pip install genai-processors
```

Check the following colabs to get familiar with GenAI processors (we recommend following them in order):

- [Content API Colab](https://colab.research.google.com/github/google-gemini/genai-processors/blob/main/notebooks/content_api_intro.ipynb) - explains the basics of `ProcessorPart`, `ProcessorContent`, and how to create them.
- [Processor Intro Colab](https://colab.research.google.com/github/google-gemini/genai-processors/blob/main/notebooks/processor_intro.ipynb) - an introduction to the core concepts of GenAI Processors.
- [Create Your Own Processor](https://colab.research.google.com/github/google-gemini/genai-processors/blob/main/notebooks/create_your_own_processor.ipynb) - a walkthrough of the typical steps to create a `Processor` or a `PartProcessor`.
- [Work with the Live API](https://colab.research.google.com/github/google-gemini/genai-processors/blob/main/notebooks/live_processor_intro.ipynb) - a couple of examples of real-time processors built from the Gemini Live API using the `LiveProcessor` class.

## 📖 Examples

Explore the [examples/](https://github.com/google-gemini/genai-processors/blob/main/examples) directory for practical demonstrations:

- [Real-Time Live Example](https://github.com/google-gemini/genai-processors/blob/main/examples/realtime_simple_cli.py) - an Audio-in Audio-out Live agent with google search as a tool. It is a client-side implementation of a Live processor (built with text-based [Gemini API](https://ai.google.dev/gemini-api/docs) models) that demonstrates the streaming and orchestration capabilities of GenAI Processors.
- [Research Agent Example](https://github.com/google-gemini/genai-processors/blob/main/examples/research/README.md) - a research agent built with Processors, comprising 3 sub-processors, chaining, creating `ProcessorPart` s, etc.
- [Live Commentary Example](https://github.com/google-gemini/genai-processors/blob/main/examples/live/README.md) - a description of a live commentary agent built with the [Gemini Live API](https://ai.google.dev/gemini-api/docs/live), composed of two agents: one for event detection and one for managing the conversation.

The [core/](https://github.com/google-gemini/genai-processors/blob/main/core) directory contains a set of basic processors that you can leverage in your own applications. It includes the generic building blocks needed for most real-time applications and will evolve over time to include more core components.

Community contributions expanding the set of built-in processors are located under [contrib/](https://github.com/google-gemini/genai-processors/blob/main/contrib) - see the section below on how to add code to the GenAI Processor library.

## 🤝 Contributing

Contributions are welcome! Please see [CONTRIBUTING.md](https://github.com/google-gemini/genai-processors/blob/main/CONTRIBUTING.md) for guidelines on how to contribute to this project.

## 📜 License

This project is licensed under the Apache License, Version 2.0. See the [LICENSE](https://github.com/google-gemini/genai-processors/blob/main/LICENSE) file for details.

If you make use of Gemini via the Genai Processors framework, please ensure you review the [Terms of Service](https://ai.google.dev/gemini-api/terms).

## Releases 4

[\+ 3 releases](https://github.com/google-gemini/genai-processors/releases)

## Packages

No packages published  

## Languages

- [Python 77.7%](https://github.com/google-gemini/genai-processors/search?l=python)
- [Jupyter Notebook 22.3%](https://github.com/google-gemini/genai-processors/search?l=jupyter-notebook)