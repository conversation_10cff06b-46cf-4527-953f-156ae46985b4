---
title: "15 Techniques to Optimize Neural Network Training"
source: "https://blog.dailydoseofds.com/p/15-techniques-to-optimize-neural"
author:
  - "[[<PERSON><PERSON>]]"
published: 2025-06-28
created: 2025-06-28
description: "...explained in a single frame."
tags:
  - "AI/ToDO"
reference:
---
### ...explained in a single frame.

**In today's newsletter:**

- The ultimate Python framework for multimodal AI
- 15 techniques to optimize neural network training.
- Fine-tuning, Transfer, Multitask & Federated Learning, explained visually.

---

### The ultimate Python framework for multimodal AI

![](https://substackcdn.com/image/fetch/$s_!8x5g!)

Data pipelines eat 90% of AI development time. They take weeks to deploy but can break in minutes when requirements change.

And it gets even worse when your data is multimodal.

**[Pixeltable](https://github.com/pixeltable/pixeltable)** is a framework that handles the entire multimodal pipeline (images, videos, audio, docs & structured data), from data storage to model execution.

It seamlessly manages images, videos, text, and tabular data—all in one place.

Fully open-source.

**[G<PERSON><PERSON><PERSON> repo →](https://github.com/pixeltable/pixeltable) (don’t forget to star)**

---

### 15 techniques to optimize neural network training

Here are 15 ways we could recall in 2 minutes to optimize neural network training:

![](https://substackcdn.com/image/fetch/$s_!9eAt!)

Some of them are pretty basic and obvious, like:

- Use efficient optimizers: AdamW, Adam, etc.
- Utilize hardware accelerators (GPUs/TPUs).
- Max out the batch size.

Here are other methods with more context:

> **On a side note, [we implemented all these techniques here →](https://www.dailydoseofds.com/15-ways-to-optimize-neural-network-training-with-implementation/)**

**#4) Use Bayesian Optimization if the hyperparameter search space is big:**

- Take informed steps using the results of previous hyperparameter configs.
- This lets it discard non-optimal configs, and the model converges faster.
- As shown in the results below, Bayesian optimization (green bar) takes the least number of iterations, consumes the lowest time, and still finds the configuration with the best F1 score:
	![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/fe4a1480-59ad-410e-8a2b-c23e3b807f65_1000x689.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:689,%22width%22:1000,%22resizeWidth%22:null,%22bytes%22:96526,%22alt%22:null,%22title%22:null,%22type%22:%22image/png%22,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:%22https://blog.dailydoseofds.com/i/166998837?img=https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Ffe4a1480-59ad-410e-8a2b-c23e3b807f65_1000x689.png%22,%22isProcessing%22:false,%22align%22:null,%22offset%22:false})

**#5) Use mixed precision training:**

![](https://substackcdn.com/image/fetch/$s_!fREY!)

- Use lower precision `float16` (wherever feasible, like in convolutions and matrix multiplications) along with `float32`.
- List of some models trained using mixed precision (indicating popularity):

![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/7223e89a-d0ac-4199-bd44-59f226acdaa5_898x432.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:432,%22width%22:898,%22resizeWidth%22:null,%22bytes%22:93344,%22alt%22:null,%22title%22:null,%22type%22:%22image/png%22,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:%22https://blog.dailydoseofds.com/i/166998837?img=https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F7223e89a-d0ac-4199-bd44-59f226acdaa5_898x432.png%22,%22isProcessing%22:false,%22align%22:null,%22offset%22:false})

#6) Use He or Xavier initialization for faster convergence (usually helps).

#7) Utilize multi-GPU training through Model/Data/Pipeline/Tensor parallelism.

#8) For large models, use techniques like DeepSpeed, FSDP, YaFSDP, etc.

#9) Always use `DistributedDataParallel`, not `DataParallel` in your data loaders, even if you are not using distributed training.

#10) Use activation checkpointing to optimize memory (run-time will go up).

![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/794aeb73-182b-425b-ac49-19df9b07d714_1200x1496.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:1496,%22width%22:1200,%22resizeWidth%22:562,%22bytes%22:491264,%22alt%22:null,%22title%22:null,%22type%22:%22image/png%22,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:%22https://blog.dailydoseofds.com/i/166998837?img=https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F794aeb73-182b-425b-ac49-19df9b07d714_1200x1496.png%22,%22isProcessing%22:false,%22align%22:null,%22offset%22:false})

- We don’t need to store all the intermediate activations in memory. Instead, storing a few of them and recomputing the rest when needed can significantly reduce the memory requirement.
- This can reduce memory usage by a factor of `sqrt(M)`, where `M` is the memory consumed without activation checkpointing.
- But due to recomputations, it increases run-time.

#11) Normalize data after transferring to GPU (for integer data, like pixels):

![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/4a57e18d-4760-4f7e-b94f-2cb92460c3cc_1456x727.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:727,%22width%22:1456,%22resizeWidth%22:null,%22bytes%22:90572,%22alt%22:null,%22title%22:null,%22type%22:%22image/png%22,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:%22https://blog.dailydoseofds.com/i/166998837?img=https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F4a57e18d-4760-4f7e-b94f-2cb92460c3cc_1456x727.png%22,%22isProcessing%22:false,%22align%22:null,%22offset%22:false})

- Consider image data, which has pixels (8-bit integer values).
- Normalizing it before transferring to the GPU would mean we need to transfer 32-bit floats.
- But normalizing after transfer means 8-bit integers are transferred, consuming less memory.

#12) Use gradient accumulation (may have marginal improvement at times).

![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/8216dfa4-6c42-4875-b56d-830b61cf5f88_2120x688.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:473,%22width%22:1456,%22resizeWidth%22:null,%22bytes%22:187727,%22alt%22:null,%22title%22:null,%22type%22:%22image/png%22,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:%22https://blog.dailydoseofds.com/i/166998837?img=https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F8216dfa4-6c42-4875-b56d-830b61cf5f88_2120x688.png%22,%22isProcessing%22:false,%22align%22:null,%22offset%22:false})

- Under memory constraints, it is always recommended to train the neural network with a small batch size.
- Despite that, there’s a technique called **gradient accumulation**, which lets us (logically) increase batch size without explicitly increasing the batch size.

**#13)** `torch.rand(2, 2, device = ...)` creates a tensor directly on the `GPU`. But `torch.rand(2,2).cuda()` first creates on the CPU, then transfers to the GPU, which is slow. The speedup is evident from the image below:

![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/0a9cfbab-b599-43a6-ab38-4c15450f4525_1456x690.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:690,%22width%22:1456,%22resizeWidth%22:null,%22bytes%22:110400,%22alt%22:null,%22title%22:null,%22type%22:%22image/png%22,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:%22https://blog.dailydoseofds.com/i/166998837?img=https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F0a9cfbab-b599-43a6-ab38-4c15450f4525_1456x690.png%22,%22isProcessing%22:false,%22align%22:null,%22offset%22:false})

#14-15) Set `max_workers` and `pin_memory` in DataLoader.

- The typical neural network training procedure is as follows:

![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/e437cb84-f778-46d5-a010-5e254cff7d5f_1740x676.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:566,%22width%22:1456,%22resizeWidth%22:null,%22bytes%22:null,%22alt%22:null,%22title%22:null,%22type%22:null,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:null,%22isProcessing%22:false,%22align%22:null,%22offset%22:false%7D)

- As shown above, when the GPU is working, the CPU is idle, and when the CPU is working, the GPU is idle.
- But here’s what we can do to optimize this:
	![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/00a246a7-bcd3-4fb6-91f2-8e0bf76e56f7_1916x676.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:514,%22width%22:1456,%22resizeWidth%22:null,%22bytes%22:null,%22alt%22:null,%22title%22:null,%22type%22:null,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:null,%22isProcessing%22:false,%22align%22:null,%22offset%22:false%7D)
	- When the model is being trained on the 1st mini-batch, the CPU can transfer the 2nd mini-batch to the GPU.
	- This ensures that the GPU does not have to wait for the next mini-batch of data as soon as it completes processing an existing mini-batch.
	- While the CPU may remain idle, this process ensures that the GPU (which is the actual accelerator for our model training) always has data to work with.

Of course, the above is not an all-encompassing list.

👉 Over to you: Can you add more techniques?

---

### Fine-tuning, Transfer, Multitask & Federated Learning

Most ML models are trained independently without any interaction with other models.

But real-world ML uses many powerful learning techniques that rely on model interactions.

The following animation summarizes four such well-adopted and must-know training methodologies:

![](https://substackcdn.com/image/fetch/$s_!eHoo!)

**1) Transfer learning**

![](https://substackcdn.com/image/fetch/$s_!3Bxz!)

Useful when:

- The task of interest has less data.
- But a related task has abundant data.

This is how it works:

- Train a neural network model (base model) on the related task.
- Replace the last few layers on the base model with new layers.
- Train the network on the task of interest, but don’t update the weights of the unreplaced layers.

Training on a related task first allows the model to capture the core patterns of the task of interest.

Next, it can adapt the last few layers to capture task-specific behavior.

2) Fine-tuning

![](https://substackcdn.com/image/fetch/$s_!vBG9!)

Update the weights of some or all layers of the pre-trained model to adapt it to the new task.

The idea may appear similar to transfer learning. But here, the whole pretrained model is typically adjusted to the new data.

3) Multi-task learning (MTL)

![](https://substackcdn.com/image/fetch/$s_!Z9US!)

A model is trained to perform multiple related tasks simultaneously.

Architecture-wise, the model has:

- A shared network
- And task-specific branches

The rationale is to share knowledge across tasks to improve generalization.

In fact, we can also save computing power with MTL:

- Imagine training 2 independent models on related tasks.
- Now compare it to having a network with shared layers and then task-specific branches.

Option 2 will typically result in:

- Better generalization across all tasks.
- Less memory to store model weights.
- Less resource usage during training.**[4) Federated learning](https://www.dailydoseofds.com/federated-learning-a-critical-step-towards-privacy-preserving-machine-learning/)**![](https://substackcdn.com/image/fetch/$s_!O_yo!)

This is a decentralized approach to ML. Here, the training data remains on the user’s device.

So in a way, it’s like sending the model to the data. To preserve privacy, only model updates are gathered from devices and sent to the server.

The keyboard of our smartphone is a great example of this.

It uses FL to learn typing patterns. This happens without transmitting sensitive keystrokes to a central server.

Note: Here, the model is trained on small devices. Thus, it MUST be lightweight yet useful.**[We implemented federated learning here →](https://www.dailydoseofds.com/federated-learning-a-critical-step-towards-privacy-preserving-machine-learning/)**

Thanks for reading!