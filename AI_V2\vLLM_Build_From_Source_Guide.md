---
title: "vLLM Build From Source Guide"
created: 2025-07-01
tags:
  - AI/vLLM
  - AI/Build
  - AI/Installation
  - AI/CUDA
description: Comprehensive guide for building vLLM from source code with CUDA support
---

# vLLM Build From Source Guide

## Overview

This guide covers building vLLM from source code, based on documentation found in the codebase. Building from source is useful for:
- Custom CUDA architectures
- Development and debugging
- Platform-specific optimizations
- Latest features not in releases

## Prerequisites

### System Requirements
- Python 3.10+ (tested with 3.10, 3.11)
- CUDA Toolkit (12.1+ recommended)
- CMake
- Ninja build system
- GCC/G++ compiler

### Environment Setup
```bash
# Install build dependencies
pip install setuptools_scm
pip install --upgrade build
pip install cmake ninja
```

## Method 1: Manual Build (Recommended)

### Step 1: Clone Repository
```bash
git clone https://github.com/vllm-project/vllm.git
cd vllm
```

### Step 2: Modify Configuration Files

#### Edit pyproject.toml
```bash
vi pyproject.toml
```
Update torch/torchaudio/torchvision versions to match your environment:
```toml
# Example versions from codebase
# torch                             2.5.0
# torchaudio                        2.5.0
# torchvision                       0.20.0
```

#### Edit requirements-cuda.txt
```bash
vi requirements-cuda.txt
```
Ensure CUDA requirements match your system.

### Step 3: Build Package
```bash
# Build without isolation (recommended for custom builds)
python -m build --no-isolation
```

**Note:** Avoid direct installation with `pip install --no-build-isolation -e .` during build process.

### Step 4: Install Built Package
```bash
# Install the built wheel
pip install dist/vllm-*.whl
```

## Method 2: Development Installation

For development purposes:
```bash
git clone https://github.com/vllm-project/vllm.git
cd vllm
pip install -e . --no-build-isolation
```

## Platform-Specific Instructions

### Jetson AGX Orin (ARM64)

#### Special Considerations
- Use ARM64-specific wheels when available
- May require custom CUDA architecture settings
- NumPy compatibility issues with some PyTorch versions

#### Build Commands
```bash
# For Jetson with CUDA support
export TORCH_CUDA_ARCH_LIST="8.7"  # For Orin
USE_CUDA=1 pip install -e . --no-build-isolation
```

#### NumPy Compatibility Fix
```bash
# Add to pyproject.toml if needed
numpy<2  # For compatibility with Jetpack PyTorch wheels
```

### WSL/Linux Build

#### Standard Build
```bash
git clone https://github.com/vllm-project/vllm.git
cd vllm
pip install setuptools_scm build
python -m build --no-isolation
```

#### With CUDA Support
```bash
# Ensure CUDA is available
export CUDA_HOME=/usr/local/cuda
export PATH=$CUDA_HOME/bin:$PATH
export LD_LIBRARY_PATH=$CUDA_HOME/lib64:$LD_LIBRARY_PATH

# Build with CUDA
python -m build --no-isolation
```

## Environment Variables

### Important Build Variables
```bash
# CUDA architecture (adjust for your GPU)
export TORCH_CUDA_ARCH_LIST="7.5,8.0,8.6,8.7,9.0"

# CUDA home directory
export CUDA_HOME=/usr/local/cuda

# For development builds
export PYTHONPATH=$PYTHONPATH:/path/to/vllm
```

### GPU-Specific Settings
```bash
# For RTX 30xx series
export TORCH_CUDA_ARCH_LIST="8.6"

# For RTX 40xx series  
export TORCH_CUDA_ARCH_LIST="8.9"

# For A100
export TORCH_CUDA_ARCH_LIST="8.0"

# For H100
export TORCH_CUDA_ARCH_LIST="9.0"
```

## Troubleshooting

### Common Issues

#### 1. CUDA Architecture Mismatch
```bash
# Error: No CUDA-capable device is detected
# Solution: Set correct TORCH_CUDA_ARCH_LIST
export TORCH_CUDA_ARCH_LIST="8.6"  # Adjust for your GPU
```

#### 2. NumPy Version Conflicts
```bash
# Error: NumPy version incompatibility
# Solution: Downgrade NumPy
pip install "numpy<2"
```

#### 3. Build Dependencies Missing
```bash
# Install missing dependencies
pip install setuptools_scm wheel build ninja cmake
```

#### 4. CUDA Toolkit Issues
```bash
# Verify CUDA installation
nvcc --version
nvidia-smi

# Set CUDA paths
export CUDA_HOME=/usr/local/cuda
export PATH=$CUDA_HOME/bin:$PATH
```

### Memory Issues During Build
```bash
# Reduce parallel jobs if running out of memory
export MAX_JOBS=2
python -m build --no-isolation
```

## Verification

### Test Installation
```python
import vllm
print(vllm.__version__)

# Test CUDA availability
import torch
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"CUDA devices: {torch.cuda.device_count()}")
```

### Basic Usage Test
```python
from vllm import LLM, SamplingParams

# Initialize model (use small model for testing)
llm = LLM(model="facebook/opt-125m")

# Generate text
prompts = ["Hello, my name is"]
sampling_params = SamplingParams(temperature=0.8, top_p=0.95)
outputs = llm.generate(prompts, sampling_params)

for output in outputs:
    print(f"Generated text: {output.outputs[0].text}")
```

## Related Documentation

- [vLLM Official Documentation](https://vllm.readthedocs.io/)
- [PyTorch CUDA Installation](https://pytorch.org/get-started/locally/)
- [NVIDIA CUDA Toolkit](https://developer.nvidia.com/cuda-toolkit)

## Notes from Codebase

- Build process tested on Jetson AGX Orin with CUDA 12.6
- Compatible with PyTorch 2.5.0+ 
- Requires careful version matching for torch/torchaudio/torchvision
- Manual build preferred over direct pip installation for custom configurations
- NumPy <2.0 required for some platform-specific PyTorch builds
