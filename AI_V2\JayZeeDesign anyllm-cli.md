---
title: <PERSON><PERSON><PERSON>D<PERSON>/anyllm-cli
source: https://github.com/JayZeeDesign/anyllm-cli/tree/main
author:
  - "[[JayZeeDesign]]"
published:
created: 2025-06-28
description:
tags:
  - AI/Gemini
  - AI/Claude
  - AI/Tools
  - AI/SKOOL
reference:
---
**[anyllm-cli](https://github.com/JayZeeDesign/anyllm-cli)** Public

[Apache-2.0 license](https://github.com/JayZeeDesign/anyllm-cli/blob/main/LICENSE)

[Open in github.dev](https://github.dev/) [Open in a new github.dev tab](https://github.dev/) [Open in codespace](https://github.com/codespaces/new/JayZeeDesign/anyllm-cli/tree/main?resume=1)

<table><thead><tr><th colspan="2"><span>Name</span></th><th colspan="1"><span>Name</span></th><th><p><span>Last commit message</span></p></th><th colspan="1"><p><span>Last commit date</span></p></th></tr></thead><tbody><tr><td colspan="3"></td></tr><tr><td colspan="2"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/tree/main/.gcp">.gcp</a></p></td><td colspan="1"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/tree/main/.gcp">.gcp</a></p></td><td><p><a href="https://github.com/JayZeeDesign/anyllm-cli/commit/d9892ada7f313c10f3206d286091803126f3ea72">fix: add repository field to package.jsons (#2032)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/tree/main/.gemini">.gemini</a></p></td><td colspan="1"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/tree/main/.gemini">.gemini</a></p></td><td><p><a href="https://github.com/JayZeeDesign/anyllm-cli/commit/dcaecde844fd21d101bbb76c41163919373543d2">toggle off (#809)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/tree/main/.github">.github</a></p></td><td colspan="1"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/tree/main/.github">.github</a></p></td><td><p><a href="https://github.com/JayZeeDesign/anyllm-cli/commit/2e20effb43657de3944091f164f9fbd72b4b2977">Fix typos (#1629)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/tree/main/.vscode">.vscode</a></p></td><td colspan="1"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/tree/main/.vscode">.vscode</a></p></td><td><p><a href="https://github.com/JayZeeDesign/anyllm-cli/commit/99d521569d392e660f8dbbb9a50cad9c9062b75b">Scotdensmore/first run auth fix (#1322)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/tree/main/docs">docs</a></p></td><td colspan="1"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/tree/main/docs">docs</a></p></td><td><p><a href="https://github.com/JayZeeDesign/anyllm-cli/commit/150df382f8e0b84aa6028622480c28186c99b8a7">Upgrade to Ink 6 and React 19 (#2096)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/tree/main/eslint-rules">eslint-rules</a></p></td><td colspan="1"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/tree/main/eslint-rules">eslint-rules</a></p></td><td><p><a href="https://github.com/JayZeeDesign/anyllm-cli/commit/e351baf10f06d2a1d1872bf2a6d7e9e709effed9">feat: add custom eslint rule for cross-package imports (#77)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/tree/main/integration-tests">integration-tests</a></p></td><td colspan="1"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/tree/main/integration-tests">integration-tests</a></p></td><td><p><a href="https://github.com/JayZeeDesign/anyllm-cli/commit/891116a6c2ab0a35ccabba9cbc70b7d3aea3587e">Flaky test (#1405)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/tree/main/packages">packages</a></p></td><td colspan="1"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/tree/main/packages">packages</a></p></td><td><p><a href="https://github.com/JayZeeDesign/anyllm-cli/commit/a6a27b7d0c33948e9c2b13908eff3738456e0794">updated tool execution handling for aisdk</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/tree/main/scripts">scripts</a></p></td><td colspan="1"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/tree/main/scripts">scripts</a></p></td><td><p><a href="https://github.com/JayZeeDesign/anyllm-cli/commit/ad7839ea4c6484485678049e8539f109304582fb">quiet dotenv log message (#2239)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/blob/main/.gitattributes">.gitattributes</a></p></td><td colspan="1"><p><a href="https://github.com/JayZeeDesign/anyllm-cli/blob/main/.gitattributes">.gitattributes</a></p></td><td><p><a href="https://github.com/JayZeeDesign/anyllm-cli/commit/d0800ab9177bf522555e25047629635dd15190b0">Adding.gitattributes (#1303)</a></p></td><td></td></tr><tr><td colspan="3"></td></tr></tbody></table>

## AnyLLM CLI

A powerful multi-model AI CLI that supports both Gemini and Claude models with full tool integration.

> **Note**: This is a fork of [Google's Gemini CLI](https://github.com/google-gemini/gemini-cli) with added Claude support and multi-model capabilities.

## Quick Start

## Environment Setup

Set your API keys directly in your terminal session:

```
# For Claude models (Anthropic)
export ANTHROPIC_API_KEY="sk-ant-api03-your-key-here"

# For Gemini models (Google AI Studio)  
export GEMINI_API_KEY="your-gemini-api-key-here"

export GOOGLE_CLOUD_PROJECT="your-gcp-project"
export GOOGLE_CLOUD_LOCATION="us-central1"

# Verify your key is set
echo $ANTHROPIC_API_KEY
```

> **Note**: Environment variables set with `export` only persist for your current terminal session. For permanent setup, add these commands to your shell profile (`~/.bashrc`, `~/.zshrc`, etc.) or use Method 2.

Create a `.env` file in the project root:

```
# For Claude models (Anthropic)
ANTHROPIC_API_KEY=sk-ant-api03-your-key-here

# For Gemini models (Google AI Studio)
GEMINI_API_KEY=your-gemini-api-key-here

# For Vertex AI (Google Cloud - optional)
GOOGLE_CLOUD_PROJECT=your-gcp-project
GOOGLE_CLOUD_LOCATION=us-central1
```
```
# 1. Install dependencies
npm install

# 2. Build the project
npm run build

# 3. Start the CLI
npm start
```

## Changing Models

```
# Use Claude Sonnet 4
npm start -- --model claude-sonnet-4-20250514

# Use Claude 3.5 Sonnet
npm start -- --model claude-3-5-sonnet-20241022

# Use Gemini 2.5 Pro (default)
npm start -- --model gemini-2.5-pro
```
1. Start the CLI: `npm start`
2. Type `/auth` and select your preferred authentication method
3. The model will automatically switch based on your auth selection

## Supported Models

- `claude-sonnet-4-20250514` - Latest Claude 4 Sonnet
- `claude-3-5-sonnet-20241022` - Claude 3.5 Sonnet
- `claude-3-5-haiku-20241022` - Claude 3.5 Haiku
- `claude-3-opus-20240229` - Claude 3 Opus
- `gemini-2.5-pro` - Latest Gemini Pro (default)
- `gemini-2.5-flash` - Faster Gemini model
- `gemini-1.5-pro` - Previous generation Pro

## Available Tools

The CLI includes powerful tools for both model providers:

- 📁 File operations (read, write, edit)
- 🖥️ Shell command execution
- 🔍 Web search and fetch
- 🧠 Memory management
- 📊 Project analysis
- 🔧 And many more...

## Usage

```
# Ask questions
> How do I optimize this Python code?

# Read files
> @src/main.py What does this file do?

# Execute commands
> !ls -la

# Get help
> /help
```

## Development

```
# Run tests
npm test

# Development mode with auto-rebuild
npm run dev

# Type checking
npm run typecheck
```

## API Keys

- **Claude**: Get your API key from [Anthropic Console](https://console.anthropic.com/)
- **Gemini**: Get your API key from [Google AI Studio](https://aistudio.google.com/app/apikey)
- **Vertex AI**: Set up through [Google Cloud Console](https://console.cloud.google.com/)

## Troubleshooting

1. **Check if your key is set**: `echo $ANTHROPIC_API_KEY`
2. **If empty, export it**: `export ANTHROPIC_API_KEY="your-key-here"`
3. **Verify it's working**: Test with `node bundle/gemini.js --model claude-3-5-sonnet-20241022 --prompt "Hello"`

If you see module not found errors, rebuild the project:

```
npm install
npm run build
```

---

Built with ❤️ using Vercel AI SDK for multi-model support.

## Releases

No releases published

## Packages

No packages published  

## Languages

- [TypeScript 95.0%](https://github.com/JayZeeDesign/anyllm-cli/search?l=typescript)
- [JavaScript 4.7%](https://github.com/JayZeeDesign/anyllm-cli/search?l=javascript)
- Other 0.3%