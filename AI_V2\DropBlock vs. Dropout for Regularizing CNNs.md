---
created: 2025-07-11
tags:
  - AI/FineTuning
  - AI/Pytorch
source:
author:
Reference:
---

# DropBlock vs. Dropout for Regularizing CNNs

[blog.dailydoseofds.com](https://blog.dailydoseofds.com/p/dropblock-vs-dropout-for-regularizing-b2c) A<PERSON>

**In today's newsletter:**

* State of Developer Experience Report 2025.

* DropBlock vs. Dropout for Regularizing CNNs.

* Label Smoothing for Regularization.

### **[State of Developer Experience 2025](https://fnf.dev/3Gy4UTh)**

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%213639%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F315ba024-06b6-48af-b0b6-12ead81ae80c_887x1166.png&valid=true)](https://substackcdn.com/image/fetch/$s_!3639!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F315ba024-06b6-48af-b0b6-12ead81ae80c_887x1166.png)

Atlassian's new **[State of Developer Experience Report 2025](https://fnf.dev/3Gy4UTh)** shares insights from 3500+ developers and developer managers globally.

Here's a preview of some interesting nuggets:

* 68% of developers reported saving 10+ hours a week from using AI, significantly more than last year's results

* Despite reporting increased productivity with AI, developers are also reporting more time wasted. 50% of developers lose 10+ hours a week due to organizational inefficiencies.

The full report dives deep into how AI is changing workflows, how to solve common friction points in dev teams, and the widening disconnect between leadership expectations and developer experience.

**[Read the Full Report here →](https://fnf.dev/3Gy4UTh)**

[State of Developer Experience Report](https://fnf.dev/3Gy4UTh)

*Thanks to Atlassian for partnering today!*

### DropBlock vs. Dropout for Regularizing CNNs

Applying Dropout over the input features in an image (pixels) isn't that effective when you use convolution layers:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%213jqD%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F1869a607-1c65-46d3-9e9e-003728d9006c_1728x676.png&valid=true)](https://substackcdn.com/image/fetch/$s_!3jqD!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F1869a607-1c65-46d3-9e9e-003728d9006c_1728x676.png)

To understand this, if we zoom in on the pixel level of the digit '9' above, the red pixel (or feature) will be highly correlated with other features in its vicinity:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%211QQq%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fc4ebb1f4-6a06-4604-8671-e036bbc65701_1416x768.png&valid=true)](https://substackcdn.com/image/fetch/$s_!1QQq!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fc4ebb1f4-6a06-4604-8671-e036bbc65701_1416x768.png) The red pixel and blue pixels are actually not red and blue in color. They just denote the pixel in consideration (red) and its neighboring pixels (blue).

Thus, dropping the red feature using Dropout will have no effect since its information can still be sent to the next layer due to the convolution operation.

DropBlock is a much better, effective, and intuitive way to regularize CNNs.

The core idea in DropBlock is to drop a contiguous region of features (or pixels) rather than individual pixels:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21eUKM%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F99c22949-00a4-42e3-8377-06e575d10a06_2200x676.png&valid=true)](https://substackcdn.com/image/fetch/$s_!eUKM!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F99c22949-00a4-42e3-8377-06e575d10a06_2200x676.png)

This forces the network to learn more robust representations that don't rely on small local patches.

By removing an entire block, the spatial redundancy is disrupted, encouraging the model to look at a broader context.

DropBlock has two main parameters:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21cTKH%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F76120db6-d086-449c-9619-84807c04db75_1200x772.png&valid=true)](https://substackcdn.com/image/fetch/$s_!cTKH!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F76120db6-d086-449c-9619-84807c04db75_1200x772.png)

* `Block_size`: The size of the box to be dropped.

* `Drop_rate`: The drop probability of the central pixel.

To apply DropBlock, first, we create a binary mask on the input sampled from the Bernoulli distribution:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21_cyo%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F1704d5f3-073f-45fc-b5a1-265546a61730_2004x676.png&valid=true)](https://substackcdn.com/image/fetch/$s_!_cyo!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F1704d5f3-073f-45fc-b5a1-265546a61730_2004x676.png)

Next, we create a block of size `block_size*block_size` which has the sampled pixels at the center:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%2183Ye%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F670ec24b-040d-4640-97c1-0d95ad876a13_2016x676.png&valid=true)](https://substackcdn.com/image/fetch/$s_!83Ye!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F670ec24b-040d-4640-97c1-0d95ad876a13_2016x676.png)

The efficacy of DropBlock over Dropout is evident from the results table below:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21Lt_w%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F64791a03-28bd-4633-8879-088667816910_2676x676.png&valid=true)](https://substackcdn.com/image/fetch/$s_!Lt_w!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F64791a03-28bd-4633-8879-088667816910_2676x676.png) Results table taken from the [DropBlock research paper](https://arxiv.org/pdf/1810.12890.pdf)

On the ImageNet classification dataset:

* DropBlock provides a 1.33% gain over Dropout.

* DropBlock with Label smoothing (covered below) provides a 1.55% gain over Dropout.

Thankfully, DropBlock is also integrated with [PyTorch](https://docs.pytorch.org/vision/master/generated/torchvision.ops.DropBlock2d.html). There's also a library for DropBlock, called [dropblock](https://github.com/miguelvr/dropblock).

Further reading:

* **[We covered 8 fatal (yet non-obvious) pitfalls and cautionary measures in data science here →](https://www.dailydoseofds.com/8-fatal-yet-non-obvious-pitfalls-and-cautionary-measures-in-data-science/)**

* **[We discussed 11 uncommon but powerful techniques to supercharge ML models here →](https://www.dailydoseofds.com/11-powerful-techniques-to-supercharge-your-ml-models/)**

**👉** Over to you: What are some other ways to regularize CNNs specifically?

Let's understand Label smoothing next!

### **Label Smoothing for Regularization**

The entire probability mass belongs to just one class in typical classification problems, and the rest are zero:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21OXbJ%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Ff5232117-31b5-415b-b4e6-b36dd9a07a3d_1000x525.png&valid=true)](https://substackcdn.com/image/fetch/$s_!OXbJ!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Ff5232117-31b5-415b-b4e6-b36dd9a07a3d_1000x525.png)

This can sometimes impact its generalization capabilities since it can **excessively motivate** the model to learn the true class for every sample.

[​](https://www.dailydoseofds.com/the-origin-of-regularization/)**[Regularising](https://www.dailydoseofds.com/the-origin-of-regularization/)** [​](https://www.dailydoseofds.com/the-origin-of-regularization/) with Label smoothing addresses this issue by reducing the probability mass of the true class and distributing it to other classes:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%211OX7%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F97c24752-111c-406d-bcd2-37eaff5c1d45_1000x483.png&valid=true)](https://substackcdn.com/image/fetch/$s_!1OX7!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F97c24752-111c-406d-bcd2-37eaff5c1d45_1000x483.png)

In the experiment below, I trained two neural networks on the Fashion MNIST dataset with the same weight initialization.

* One without label smoothing.

* Another with label smoothing.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21e280%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F694adccc-51c1-451b-ab5f-37541dde7df5_1456x1230.png&valid=true)](https://substackcdn.com/image/fetch/$s_!e280!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F694adccc-51c1-451b-ab5f-37541dde7df5_1456x1230.png)

The model with label smoothing (right) resulted in better test accuracy, i.e., better generalization.

#### **When not to use label smoothing?**

Label smoothing is recommended only if you only care about getting the final prediction correct.

But don't use it if you also care about the **model's confidence** since label smoothing directs the model to become "less overconfident" in its predictions, resulting in a drop in the confidence values for every prediction:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21fJni%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fc15093b4-8eb6-455a-b23f-91391bc530e7_1456x718.png&valid=true)](https://substackcdn.com/image/fetch/$s_!fJni!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fc15093b4-8eb6-455a-b23f-91391bc530e7_1456x718.png) Confidence drops with label smoothing

That said, L2 regularization is another common way to regularize models. Here's a guide that explains its probabilistic origin: [​](https://www.dailydoseofds.com/the-origin-of-regularization/)**[The Probabilistic Origin of Regularization](https://www.dailydoseofds.com/the-origin-of-regularization/)** [​](https://www.dailydoseofds.com/the-origin-of-regularization/).

Thanks for reading!

### **P.S. For those wanting to develop "Industry ML" expertise:**

[![](https://images.cubox.cc/cardImg/n2pch3bqdykaytf8bwv5csa9mnyfttprxwnx09awev93p89l9.png)](https://substackcdn.com/image/fetch/$s_!cn8y!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F939bede7-b0de-4770-a3e9-34d39488e776_2733x1020.png)

At the end of the day, all businesses care about *impact* . That's it!

* Can you reduce costs?

* Drive revenue?

* Can you scale ML models?

* Predict trends before they happen?

We have discussed several other topics (with implementations) that align with such topics.

[Develop "Industry ML" Skills](https://www.dailydoseofds.com/membership)

Here are some of them:

* Learn how to build Agentic systems in **[a crash course with 14 parts](https://www.dailydoseofds.com/ai-agents-crash-course-part-1-with-implementation/)** .

* Learn how to build real-world RAG apps and evaluate and scale them in **[this crash course](https://www.dailydoseofds.com/a-crash-course-on-building-rag-systems-part-1-with-implementations/)** .

[![](https://images.cubox.cc/cardImg/4spzixbq1vbvruz237umfz8x54m7osmy95qg7lwse4bg8723br.png)](https://substackcdn.com/image/fetch/$s_!HcHn!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fca6f5be8-3aa0-4754-8087-1929397898e6_697x920.png)

* Learn sophisticated graph architectures and how to train them on graph data.

* So many real-world NLP systems rely on pairwise context scoring. Learn scalable approaches **[here](https://www.dailydoseofds.com/bi-encoders-and-cross-encoders-for-sentence-pair-similarity-scoring-part-1/)** .

* Learn how to run large models on small devices using [​](https://www.dailydoseofds.com/quantization-optimize-ml-models-to-run-them-on-tiny-hardware/)**[Quantization techniques](https://www.dailydoseofds.com/quantization-optimize-ml-models-to-run-them-on-tiny-hardware/)** .

* Learn how to generate prediction intervals or sets with strong statistical guarantees for increasing trust using [​](https://www.dailydoseofds.com/conformal-predictions-build-confidence-in-your-ml-models-predictions/)**[Conformal Predictions](https://www.dailydoseofds.com/conformal-predictions-build-confidence-in-your-ml-models-predictions/)** .

* Learn how to identify causal relationships and answer business questions using causal inference in **[this crash course](https://www.dailydoseofds.com/a-crash-course-on-causality-part-1/)** .

* Learn how to scale and implement ML model training in this **[practical guide](https://www.dailydoseofds.com/how-to-scale-model-training/)** .

* Learn techniques to reliably **[test new models in production](https://www.dailydoseofds.com/5-must-know-ways-to-test-ml-models-in-production-implementation-included/)** .

* Learn how to build privacy-first ML systems using [​](https://www.dailydoseofds.com/federated-learning-a-critical-step-towards-privacy-preserving-machine-learning/)**[Federated Learning](https://www.dailydoseofds.com/federated-learning-a-critical-step-towards-privacy-preserving-machine-learning/)** .

* Learn 6 techniques with implementation to **[compress ML models](https://www.dailydoseofds.com/model-compression-a-critical-step-towards-efficient-machine-learning/)** .

All these resources will help you cultivate key skills that businesses and companies care about the most.

[Read in Cubox](https://cubox.cc/my/card?id=7343287857173233698)
