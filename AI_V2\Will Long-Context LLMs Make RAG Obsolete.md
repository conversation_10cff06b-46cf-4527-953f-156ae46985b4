---
created: 2025-07-07
tags:
  - AI/RAG
source:
author:
Reference:
---
# Will Long-Context LLMs Make RAG Obsolete?

[blog.dailydoseofds.com](https://blog.dailydoseofds.com/p/will-long-context-llms-make-rag-obsolete) A<PERSON>

**In today's newsletter:**

* Declarative Data Infrastructure for Multimodal AI

* \[Hands-on\] ​Package AI/ML Projects with KitOps MCP Server.

* Will long-context LLMs make RAG obsolete?

### **[Declarative Data Infrastructure for Multimodal AI](https://github.com/pixeltable/pixeltable)**

**[Pixeltable](https://github.com/pixeltable/pixeltable)** is the only open-source Python library that lets you define your entire multimodal workflow (from storage to transformation to inference) as computed columns on a table.

It automates the data plumbing, versioning, and incremental updates, letting you focus on logic instead of complex pipelines.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%218x5g%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F1a14656b-a9e8-4ea8-8c09-9465e67ed7bc_1236x748.png&valid=true)](https://github.com/pixeltable/pixeltable)

What this means for you:

* Incremental computation: Only processes changes, saving time and cost.

* Automatic versioning: Full lineage tracking for reproducibility.

* Unified system: Handles data, transformations, and AI models in one place.

* Focus on your application logic, not the infrastructure.

Fully open-source.

[Pixeltable GitHub repo](https://github.com/pixeltable/pixeltable)

**[GitHub repo →](https://github.com/pixeltable/pixeltable) (don't forget to star)**

### [​](https://github.com/kitops-ml/kitops)**[Package AI/ML Projects with KitOps MCP Server](https://github.com/kitops-ml/kitops)** [​](https://github.com/kitops-ml/kitops)

ML projects aren't just code.

They are code + datasets + model weights + parameters + config, and whatnot!

* Docker isn't well-suited to package them since you cannot selectively pull what you need.

* And GitHub enforces size limits.

To solve this, we recently built an MCP server that all AI/ML Engineers will love.

The video below gives a detailed walk-through.

We created ModelKits (powered by open-source [​](https://github.com/kitops-ml/kitops)**[KitOps](https://github.com/kitops-ml/kitops)** [​](https://github.com/kitops-ml/kitops)) to package an AI/ML project (models, datasets, code, and config) into a single, shareable unit.

Think of it as Docker for AI, but smarter.

While Docker containers package applications, [​](https://github.com/kitops-ml/kitops)**[ModelKits](https://github.com/kitops-ml/kitops)** are purpose-built for AI/ML workflows.

Key advantages that we observed:

* Lets you selectively unpack kits and skip pulling what you don't need.

* Acts as your private model registry

* Gives you one-command deployment

* Works with your existing container registry

* Lets you create RAG pipelines as well

* Has built-in LLM fine-tuning support.

* Supports Kubernetes/KServe config generation

We have wrapped up KitOps CLI and their Python SDK in an MCP server, and the video above gives a detailed walkthrough of how you can use it.

Here are the relevant links:

* [​](https://github.com/kitops-ml/kitops)**[KitOps GitHub repo →](https://github.com/kitops-ml/kitops)** [​](https://github.com/kitops-ml/kitops)

* [​](https://github.com/patchy631/ai-engineering-hub/tree/main/kitops-mcp)**[The code we wrote to build the MCP server →](https://github.com/patchy631/ai-engineering-hub/tree/main/kitops-mcp)** [​](https://github.com/patchy631/ai-engineering-hub/tree/main/kitops-mcp)

### Will long-context LLMs make RAG obsolete?

Consider this:

* GPT-3.5-turbo had a context window of 4,096 tokens.

* Later, GPT-4 took that to 8,192 tokens.

* Claude 2 reached 100,000 tokens.

* Llama 3.1 → 128,000 tokens.

* Gemini → 1M+ tokens.

We have been making great progress in extending the context window of LLMs.

This raises an obvious question about the relevance of RAG and researchers remain divided on whether long-context LLMs make RAG obsolete.

Today, let's explore the debate, comparing RAG and long-context LLMs while analyzing academic research.

#### What is a long-context LLM and RAG?

RAG retrieves relevant information from external sources, while long-context LLMs process extensive input directly within their context windows.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21t-uH%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F1fec22c0-bc81-4542-a8b4-c086b429e7d5_1456x609.webp&valid=true)](https://substackcdn.com/image/fetch/$s_!t-uH!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F1fec22c0-bc81-4542-a8b4-c086b429e7d5_1456x609.webp)

While LLMs can summarize entire documents and perform multi-hop reasoning across passages, RAG excels at handling large-scale, cost-efficient retrieval tasks.

#### Comparison based on academic research

**[Paper 1) Can Long-Context Language Models Subsume Retrieval, RAG, SQL, and More?](https://arxiv.org/pdf/2406.13121)**

The LOFT benchmark evaluates retrieval and reasoning tasks requiring up to millions of tokens.

While Gemini 1.5 Pro outperforms the RAG pipeline on multi-hop datasets (e.g., HotpotQA, MusiQue), RAG retains an edge in scalability for larger corpus sizes (1M tokens).

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21bN9z%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fe3c9347f-360d-4ec0-9c31-dc45a6351dd1_1892x1524.png&valid=true)](https://substackcdn.com/image/fetch/$s_!bN9z!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fe3c9347f-360d-4ec0-9c31-dc45a6351dd1_1892x1524.png)

**[Paper 2) RAG vs. Long Context: Examining Frontier LLMs for Environmental Review](https://arxiv.org/pdf/2407.07321)**

The NEPAQuAD1.0 benchmark evaluates RAG and long-context LLMs on environmental impact statements.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21B8Vy%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F4fddb474-7889-4b46-b354-75d4ad6306a8_1892x732.png&valid=true)](https://substackcdn.com/image/fetch/$s_!B8Vy!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F4fddb474-7889-4b46-b354-75d4ad6306a8_1892x732.png)

Results show that RAG-driven models outperform long-context LLMs in accuracy, particularly in domain-specific tasks.

**[Paper 3) A Comprehensive Study and Hybrid Approach](https://arxiv.org/pdf/2407.16833)**

This paper benchmarks RAG and long-context LLMs, emphasizing their strengths. SELF-ROUTE, a hybrid method combining both, reduces costs while maintaining competitive performance.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21cWuH%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F112b5720-82fa-4bb6-a08d-1925f7bb51cd_1892x1380.png&valid=true)](https://substackcdn.com/image/fetch/$s_!cWuH!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F112b5720-82fa-4bb6-a08d-1925f7bb51cd_1892x1380.png)

The trade-off between token percentage and performance highlights RAG's efficiency at smaller retrieval scales.

**[Paper 4) ChatQA 2: Bridging Open-Source and Proprietary LLMs](https://arxiv.org/pdf/2407.14482)**

ChatQA 2, based on Llama3, evaluates long-context solutions.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21a17n%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F9e8e9411-3047-4b2e-b843-d44ad8a28ea4_2980x676.png&valid=true)](https://substackcdn.com/image/fetch/$s_!a17n!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F9e8e9411-3047-4b2e-b843-d44ad8a28ea4_2980x676.png)

Long-context LLMs perform marginally poor than RAG while also requiring more token context.

Here are some key insights:

1. Cost efficiency: Handling 200K-1M tokens per request with long-context LLMs can cost up to $20, making RAG a more affordable option for many applications.

2. Domain-specific knowledge: RAG outperforms in niche areas requiring precise, curated retrieval.

3. Complementary integration: Most RAG pipelines fail due to poor retrieval, which, in turn, happens due to poor chunking. Combining RAG with long-context LLMs can enhance retrieval and processing efficiency, potentially eliminating the need for chunking or chunk-level recall.

#### CAG vs. RAG

A recently released mechanism called CAG (cache-augmented generation) has been trending lately.

The core idea is to replace real-time document retrieval with preloaded knowledge in the extended context of LLMs. This approach ensures faster, more accurate, and consistent generation by avoiding retrieval errors and latency.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21Y-MJ%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F78bd9de7-8d38-4eed-9dee-910e7d044394_680x430.png&valid=true)](https://substackcdn.com/image/fetch/$s_!Y-MJ!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F78bd9de7-8d38-4eed-9dee-910e7d044394_680x430.png)

Key advantages:

* Little latency: All data is preloaded, so there's no waiting for retrieval.

* Fewer mistakes: Precomputed values avoid ranking or document selection errors.

* Simpler architecture: No separate retriever---just load the cache and go.

* Faster inference: Once cached, responses come at lightning speed.

* Higher accuracy: The model processes a unified, complete context upfront.

But it also has two major limitations:

* Inflexibility to dynamic data

* Constrained by the context length of an LLM.

Long-context LLMs offer flexibility but face limitations in cost and scalability. Meanwhile, RAG remains indispensable for large-scale retrieval tasks.

We feel that a hybrid approach that integrates RAG and long-context LLMs could redefine the information retrieval landscape, leveraging the strengths of both systems.

* Retrieval will help reduce costs that long-context LLMs alone will incur.

* A decent context window still allows the LLM to reason over retrieved chunks more effectively, reducing fragmentation and hallucination.

What is your opinion on this debate? Let us know :)

Thanks for reading!

[Read in Cubox](https://cubox.cc/my/card?id=7341352344933630497)
