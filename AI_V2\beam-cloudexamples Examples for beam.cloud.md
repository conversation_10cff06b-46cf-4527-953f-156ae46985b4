---
title: "beam-cloud/examples: Examples for beam.cloud"
source: https://github.com/beam-cloud/examples
author:
  - "[[jsun-m]]"
published:
created: 2025-07-12
description: Examples for beam.cloud. Contribute to beam-cloud/examples development by creating an account on GitHub.
tags:
  - AI/Infra
reference:
---
[Skip to content](https://github.com/beam-cloud/#start-of-content)

**[examples](https://github.com/beam-cloud/examples)** Public

- [Fork 11 Fork your own copy of beam-cloud/examples](https://github.com/beam-cloud/examples/fork)

Examples for beam.cloud

[docs.beam.cloud](https://docs.beam.cloud/ "https://docs.beam.cloud")

[Open in github.dev](https://github.dev/) [Open in a new github.dev tab](https://github.dev/) [Open in codespace](https://github.com/codespaces/new/beam-cloud/examples?resume=1)

## beam-cloud/examples

## Folders and files

<table><thead><tr><th colspan="2"><span>Name</span></th><th colspan="1"><span>Name</span></th><th><p><span>Last commit message</span></p></th><th colspan="1"><p><span>Last commit date</span></p></th></tr></thead><tbody><tr><td colspan="3"><h2>Latest commit</h2><p><span><a href="https://github.com/beam-cloud/examples/commit/36ae5728cd160db8dfa31d007dc1f5517e4cc797">36ae572</a> ·</span></p><div><h2>History</h2><a href="https://github.com/beam-cloud/examples/commits/main/"><span><span><span>87 Commits</span></span></span></a></div></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/examples/tree/main/.github/workflows"><span>.github/</span> <span>workflows</span></a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/examples/tree/main/.github/workflows"><span>.github/</span> <span>workflows</span></a></p></td><td><p><a href="https://github.com/beam-cloud/examples/commit/592007a5042b9f6b40ae2d6613ec2fe989271cb2">Add CD for integration tests (</a><a href="https://github.com/beam-cloud/examples/pull/82">#82</a><a href="https://github.com/beam-cloud/examples/commit/592007a5042b9f6b40ae2d6613ec2fe989271cb2">)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/examples/tree/main/audio_and_transcription">audio_and_transcription</a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/examples/tree/main/audio_and_transcription">audio_and_transcription</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/examples/tree/main/bioinformatics">bioinformatics</a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/examples/tree/main/bioinformatics">bioinformatics</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/examples/tree/main/blender">blender</a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/examples/tree/main/blender">blender</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/examples/tree/main/callbacks">callbacks</a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/examples/tree/main/callbacks">callbacks</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/examples/tree/main/custom_images">custom_images</a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/examples/tree/main/custom_images">custom_images</a></p></td><td><p><a href="https://github.com/beam-cloud/examples/commit/36076751f48533722e08bc8af87d2efd0572583d">Update tests</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/examples/tree/main/endpoints">endpoints</a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/examples/tree/main/endpoints">endpoints</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/examples/tree/main/experimental/signals"><span>experimental/</span> <span>signals</span></a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/examples/tree/main/experimental/signals"><span>experimental/</span> <span>signals</span></a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/examples/tree/main/finetuning">finetuning</a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/examples/tree/main/finetuning">finetuning</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/examples/tree/main/functions">functions</a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/examples/tree/main/functions">functions</a></p></td><td></td><td></td></tr><tr><td colspan="3"></td></tr></tbody></table>

[![Logo](https://camo.githubusercontent.com/4d09c377110f742bc492712ccb24b52a62425a1f85f49893ffc1bc9907df26c8/68747470733a2f2f736c61692d64656d6f2d64617461736574732e73332e616d617a6f6e6177732e636f6d2f6265616d2d62616e6e65722e737667)](https://camo.githubusercontent.com/4d09c377110f742bc492712ccb24b52a62425a1f85f49893ffc1bc9907df26c8/68747470733a2f2f736c61692d64656d6f2d64617461736574732e73332e616d617a6f6e6177732e636f6d2f6265616d2d62616e6e65722e737667)

## Get Started

The `examples` folder in this repo contains various examples of programs built with Beam.

You can run any of these examples yourself. All you need is a free account on [Beam](https://beam.cloud/).

## Releases

No releases published

## Packages

No packages published