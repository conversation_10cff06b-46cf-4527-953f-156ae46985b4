---
title: NomenAK/SuperClaude
source: https://github.com/NomenAK/SuperClaude
author:
  - "[[<PERSON>men<PERSON><PERSON>]]"
  - "[[claude]]"
published:
created: 2025-06-25
description: Contribute to NomenAK/SuperClaude development by creating an account on GitHub.
tags:
  - AI/Claude
reference:
---
**[SuperClaude](https://github.com/NomenAK/SuperClaude)** Public

[MIT license](https://github.com/NomenAK/SuperClaude/blob/master/LICENSE)

[Code of conduct](https://github.com/NomenAK/SuperClaude/blob/master/CODE_OF_CONDUCT.md)

[Security policy](https://github.com/NomenAK/SuperClaude/blob/master/SECURITY.md)

[1.3k stars](https://github.com/NomenAK/SuperClaude/stargazers) [110 forks](https://github.com/NomenAK/SuperClaude/forks) [19 watching](https://github.com/NomenAK/SuperClaude/watchers) [Branches](https://github.com/NomenAK/SuperClaude/branches) [Tags](https://github.com/NomenAK/SuperClaude/tags) [Activity](https://github.com/NomenAK/SuperClaude/activity)

Public repository

[Open in github.dev](https://github.dev/) [Open in a new github.dev tab](https://github.dev/) [Open in codespace](https://github.com/codespaces/new/NomenAK/SuperClaude?resume=1)

<table><thead><tr><th colspan="2"><span>Name</span></th><th colspan="1"><span>Name</span></th><th><p><span>Last commit message</span></p></th><th colspan="1"><p><span>Last commit date</span></p></th></tr></thead><tbody><tr><td colspan="3"><p><span>and</span></p><p><span><a href="https://github.com/NomenAK/SuperClaude/commit/84ad5b60d15759f0a5cace170b8ac7ced43c4dd3">Update all command files to use optimized template system</a></span></p><p><span><a href="https://github.com/NomenAK/SuperClaude/commit/84ad5b60d15759f0a5cace170b8ac7ced43c4dd3">84ad5b6</a> ·</span></p><p><a href="https://github.com/NomenAK/SuperClaude/commits/master/"><span><span><span>14 Commits</span></span></span></a></p></td></tr><tr><td colspan="2"><p><a href="https://github.com/NomenAK/SuperClaude/tree/master/.claude/commands"><span>.claude/</span> <span>commands</span></a></p></td><td colspan="1"><p><a href="https://github.com/NomenAK/SuperClaude/tree/master/.claude/commands"><span>.claude/</span> <span>commands</span></a></p></td><td><p><a href="https://github.com/NomenAK/SuperClaude/commit/84ad5b60d15759f0a5cace170b8ac7ced43c4dd3">Update all command files to use optimized template system</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/NomenAK/SuperClaude/tree/master/.github">.github</a></p></td><td colspan="1"><p><a href="https://github.com/NomenAK/SuperClaude/tree/master/.github">.github</a></p></td><td><p><a href="https://github.com/NomenAK/SuperClaude/commit/a13673969a83620e6240a0886a301c71d946ec30">Add community interaction files</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/NomenAK/SuperClaude/blob/master/.gitignore">.gitignore</a></p></td><td colspan="1"><p><a href="https://github.com/NomenAK/SuperClaude/blob/master/.gitignore">.gitignore</a></p></td><td><p><a href="https://github.com/NomenAK/SuperClaude/commit/dc0f22607a40491b0d82046827cb9ef942149452">Update installation scripts and documentation</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/NomenAK/SuperClaude/blob/master/CHANGELOG.md">CHANGELOG.md</a></p></td><td colspan="1"><p><a href="https://github.com/NomenAK/SuperClaude/blob/master/CHANGELOG.md">CHANGELOG.md</a></p></td><td><p><a href="https://github.com/NomenAK/SuperClaude/commit/dc0f22607a40491b0d82046827cb9ef942149452">Update installation scripts and documentation</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/NomenAK/SuperClaude/blob/master/CLAUDE.md">CLAUDE.md</a></p></td><td colspan="1"><p><a href="https://github.com/NomenAK/SuperClaude/blob/master/CLAUDE.md">CLAUDE.md</a></p></td><td><p><a href="https://github.com/NomenAK/SuperClaude/commit/dc0f22607a40491b0d82046827cb9ef942149452">Update installation scripts and documentation</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/NomenAK/SuperClaude/blob/master/CODE_OF_CONDUCT.md">CODE_OF_CONDUCT.md</a></p></td><td colspan="1"><p><a href="https://github.com/NomenAK/SuperClaude/blob/master/CODE_OF_CONDUCT.md">CODE_OF_CONDUCT.md</a></p></td><td><p><a href="https://github.com/NomenAK/SuperClaude/commit/a13673969a83620e6240a0886a301c71d946ec30">Add community interaction files</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/NomenAK/SuperClaude/blob/master/CONTRIBUTING.md">CONTRIBUTING.md</a></p></td><td colspan="1"><p><a href="https://github.com/NomenAK/SuperClaude/blob/master/CONTRIBUTING.md">CONTRIBUTING.md</a></p></td><td><p><a href="https://github.com/NomenAK/SuperClaude/commit/dc0f22607a40491b0d82046827cb9ef942149452">Update installation scripts and documentation</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/NomenAK/SuperClaude/blob/master/Commands_Cheat_Sheet.md">Commands_Cheat_Sheet.md</a></p></td><td colspan="1"><p><a href="https://github.com/NomenAK/SuperClaude/blob/master/Commands_Cheat_Sheet.md">Commands_Cheat_Sheet.md</a></p></td><td><p><a href="https://github.com/NomenAK/SuperClaude/commit/dc0f22607a40491b0d82046827cb9ef942149452">Update installation scripts and documentation</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/NomenAK/SuperClaude/blob/master/LICENSE">LICENSE</a></p></td><td colspan="1"><p><a href="https://github.com/NomenAK/SuperClaude/blob/master/LICENSE">LICENSE</a></p></td><td><p><a href="https://github.com/NomenAK/SuperClaude/commit/dc0f22607a40491b0d82046827cb9ef942149452">Update installation scripts and documentation</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/NomenAK/SuperClaude/blob/master/MCP.md">MCP.md</a></p></td><td colspan="1"><p><a href="https://github.com/NomenAK/SuperClaude/blob/master/MCP.md">MCP.md</a></p></td><td><p><a href="https://github.com/NomenAK/SuperClaude/commit/dc0f22607a40491b0d82046827cb9ef942149452">Update installation scripts and documentation</a></p></td><td></td></tr><tr><td colspan="3"></td></tr></tbody></table>

**A lightweight framework that transforms generic Claude Code into your specialized development partner – no external tools, no complex setup, just pure configuration magic.**

Claude Code is powerful, but let's be honest – it's generic. You find yourself:

- Losing context mid-debugging session
- Repeating the same instructions every project
- Wishing it understood YOUR coding style
- Watching tokens vanish on verbose responses

Think of it as a brain upgrade for Claude Code. Drop it in once, and suddenly Claude:

- **Remembers everything** with Git-based checkpoints
- **Thinks like you want** with 9 specialized personas
- **Works 70% more efficiently** with token optimization
- **Never guesses** – always finds the official docs first
```
git clone https://github.com/NomenAK/SuperClaude.git
cd SuperClaude
./install.sh                           # Default: ~/.claude/
# OR
./install.sh --dir /opt/claude          # Custom location
./install.sh --dir ./project-claude    # Project-specific
```

That's it. No databases, no services, no dependencies. Installs to `~/.claude/` by default or any directory you choose. The installer even backs up your existing config automatically!

Git-integrated checkpoint system lets you jump back to any point in your conversation. Debugging a nasty bug at 3am? Create a checkpoint. Need to try a different approach? Roll back and branch off.

Your docs write themselves using our token-optimized templates. Project docs go in `/docs`, while Claude's working notes live in `/.claudedocs`. Everything stays organized, nothing gets lost.

Switch Claude's entire mindset with one command:

```
/persona:architect    # Big-picture system design mode
/persona:frontend     # Pixel-perfect UI obsession mode
/persona:security     # Paranoid threat-modeling mode
/persona:analyzer     # Sherlock Holmes debugging mode
```

Each persona thinks differently, asks different questions, and solves problems their own way.

Real shortcuts for real work:

```
/user:build --react        # Spin up a React app with YOUR standards
/user:troubleshoot --prod  # Production fire? This knows what to do
/user:analyze --security   # Full security audit in seconds
/user:git --checkpoint     # Save your progress before that risky refactor
```
- **Context7** finds library docs instantly (no more "I think this is how it works")
- **Sequential** thinking for complex problems (watch it reason step-by-step)
- **Magic** generates UI components that actually match your style
- **Puppeteer** tests everything in a real browser

Our UltraCompressed mode strips unnecessary tokens without losing clarity. More context, longer conversations, bigger projects – same token budget.

```
/persona:architect
/user:design --api --ddd     # Domain-driven design from the start
/user:estimate --detailed    # Know what you're getting into
/persona:backend
/user:build --api --tdd      # Build it right the first time
```
```
/persona:analyzer
/user:troubleshoot --investigate --prod
/user:analyze --profile      # Find the real bottleneck
/persona:performance
/user:improve --performance --threshold 90%
```
```
/persona:frontend
/user:build --react --magic  # AI-generated components
/user:test --e2e --pup       # See it work in a real browser
/user:improve --quality      # Polish until it shines
```

SuperClaude is pure configuration – no code, no external dependencies. It works by:

1. **Loading specialized instructions** when Claude Code starts
2. **Activating different rulesets** based on your commands
3. **Switching cognitive modes** through personas
4. **Optimizing token usage** automatically

The framework includes:

- **CLAUDE.md** – Core configuration and behaviors
- **RULES.md** – Engineering standards and practices
- **PERSONAS.md** – 9 specialized thinking modes
- **MCP.md** – Smart tool orchestration
- **18 Commands** – Ready-made workflows
- **26 Shared Resources** – Battle-tested patterns

| Persona | Superpower | Activate When You Need... |
| --- | --- | --- |
| **architect** | Sees the big picture | System design that scales |
| **frontend** | UX perfectionist | Interfaces users love |
| **backend** | Performance obsessed | APIs that never fail |
| **security** | Professional paranoid | Code that's bulletproof |
| **analyzer** | Root cause detective | To solve the unsolvable |
| **qa** | Bug hunter supreme | Testing that catches everything |
| **performance** | Speed demon | Every millisecond to count |
| **refactorer** | Code beautifier | To simplify the complex |
| **mentor** | Patient teacher | To understand, not just copy |

### Thinking Modes

Control how deep Claude analyzes:

```
"think about X"              # Standard analysis
"think hard about Y"         # Architecture-level depth
"ultrathink Z"               # When you need EVERYTHING considered
```
```
--c7           # Force documentation lookup
--seq          # Force step-by-step reasoning
--magic        # Force UI component generation
--no-mcp       # Use only native tools
--all-mcp      # Kitchen sink mode
```

### Evidence-Based Everything

No more "this is better" without proof. SuperClaude enforces:

- Metrics for performance claims
- Documentation for library usage
- Test results for quality assertions
- Security scans for safety claims

SuperClaude is MIT-licensed and built by developers, for developers. We welcome:

- New personas for specialized workflows
- Commands that solve your daily pain points
- Patterns that make Claude Code smarter
- Ideas that push the boundaries

Check out our [Contributing Guide](https://github.com/NomenAK/SuperClaude/blob/master/CONTRIBUTING.md) and join the conversation!

| Feature | Without SuperClaude | With SuperClaude |
| --- | --- | --- |
| **Context** | Lost after errors | Git checkpoints preserve everything |
| **Personas** | Generic responses | Specialized thinking modes |
| **Tokens** | Verbose outputs | 70% reduction, same information |
| **Docs** | "I think this works" | Always finds official sources |
| **Workflows** | Repeat instructions | One command, complete flow |
| **Quality** | Hope for the best | Evidence-based standards |

- VS Code extension for deeper integration
- Persona marketplace for community contributions
- Team sync for shared configurations
- Analytics dashboard (privacy-first)

> "I was debugging a production issue at 2am. Created a checkpoint, tried three different approaches, rolled back to the one that worked. Saved my sanity." – *Backend Dev*

> "The frontend persona just *gets* UX. It asks questions I didn't even think of." – *Full-Stack Dev*

> "70% token reduction means I can keep entire codebases in context. Game changer." – *Tech Lead*

Perfect if you:

- ✅ Want consistent AI assistance across projects
- ✅ Value evidence over opinions
- ✅ Need specialized thinking modes
- ✅ Care about token efficiency
- ✅ Like tools that just work

Skip if you:

- ❌ Prefer completely manual control
- ❌ Don't use Claude Code regularly
- ❌ Happy with generic AI responses
1. **Install**
	```
	git clone https://github.com/NomenAK/SuperClaude.git && cd SuperClaude && ./install.sh
	# Or custom location: ./install.sh --dir /your/path
	```
2. **Test Drive**
	```
	/user:analyze --code        # See what it finds
	/persona:architect          # Try a new mindset
	```
3. **Go Deeper**
	- Explore commands: `/user:load`
	- Read the guides: `~/.claude/commands/`
	- Join the community: [Discussions](https://github.com/NomenAK/SuperClaude/discussions)
- **Installation issues?** Run `./install.sh` again – it's idempotent. Use `./install.sh --help` for options
- **Commands not working?** Check `ls ~/.claude/commands/`
- **Want to contribute?** See [CONTRIBUTING.md](https://github.com/NomenAK/SuperClaude/blob/master/CONTRIBUTING.md)
- **Found a bug?**[Open an issue](https://github.com/NomenAK/SuperClaude/issues)

SuperClaude isn't just a tool – it's a movement to make AI assistance actually useful for real developers. Every persona added, every command refined, every pattern shared makes Claude Code better for everyone.

**What would make YOUR workflow better? Let's build it together.**

---

*SuperClaude v4.0.0 – Because generic AI assistance isn't good enough anymore.*

[⭐ Star us on GitHub](https://github.com/NomenAK/SuperClaude) | [💬 Join the Discussion](https://github.com/NomenAK/SuperClaude/discussions) | [🐛 Report an Issue](https://github.com/NomenAK/SuperClaude/issues)

## Releases

No releases published

## Packages

No packages published  

## Languages

- [Shell 100.0%](https://github.com/NomenAK/SuperClaude/search?l=shell)