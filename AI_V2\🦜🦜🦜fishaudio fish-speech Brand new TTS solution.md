---
created: 2024-09-17T16:10:11 (UTC +09:00)
tags:
  - AI/Voice
  - AI/Clone
source: https://github.com/fishaudio/fish-speech
author: 
Comments:
  - https://fish.audio/zh-CN/text-to-speech/
Reference:
  - https://ayousanz.hatenadiary.jp/entry/2023/12/18/213302
  - https://speech.fish.audio/#requirements
---
# Fish-speech -> OpenAudio
https://speech.fish.audio/zh/

# Memo Test
## Prepare Model
```
cd /opt/workspace/app/cursor/highlights
huggingface-cli download fishaudio/fish-speech-1.4 --local-dir checkpoints/fish-speech-1.4/
huggingface-cli download fishaudio/fish-speech-1.5 --local-dir checkpoints/fish-speech-1.5/
```

conda create -n fish-speech python=3.10
conda activate fish-speech
install pytorch /vision/audio and flash-attn
-- remove pytorch and pyaudio from pyproject.toml
pip install -e .

export HUGGING_FACE_HUB_TOKEN=YOUR_TOKEN
pip install huggingface-hub

huggingface-cli login 
huggingface-cli download fishaudio/fish-speech-1.4 --local-dir checkpoints/fish-speech-1.4/
huggingface-cli download fishaudio/fish-speech-1.5 --local-dir checkpoints/fish-speech-1.5/


## Inference
https://speech.fish.audio/inference/

1. create sample wav file
ffmpeg -i sample.mp3 sample.wav
1. create sample prompt
```
python tools/vqgan/inference.py -i "sample/same-lang-fish.wav" --checkpoint-path "checkpoints/fish-speech-1.5/firefly-gan-vq-fsq-8x1024-21hz-generator.pth" --device cuda

python tools/vqgan/inference.py -i "sample/sample.wav" --checkpoint-path "checkpoints/fish-speech-1.5/firefly-gan-vq-fsq-8x1024-21hz-generator.pth" --device cuda

!python tools/vqgan/inference.py
-i {src_A_audio}
--checkpoint-path "checkpoints/fish-speech-1.4/firefly-gan-vq-fsq-8x1024-21hz-generator.pth"
--output-path {reference_A_audio}
--device cpu #cuda

```
2. create token from text
```
python tools/llama/generate.py --text "从古至今，庸君最怕朝纲了，可明君他就不怕，不但不怕，反能借助。要我说，你就让李四张三互相争宠，只要你心里清楚，左右周旋，你就能处于不败之境。" --prompt-text "他闭上眼睛，期望这一切都能过去。然而，当他再次睁开眼睛，眼前的景象让他不禁倒吸一口气。雾气中出现的禁闭岛，陌生又熟悉，充满未知的危险。他握紧拳头，心知他的生活即将发生翻天覆地的改变。" --prompt-tokens "fake.npy" --checkpoint-path "checkpoints/fish-speech-1.5" --num-samples 2 --device cuda

python tools/llama/generate.py --text "从古至今，庸君最怕朝纲了，可明君他就不怕，不但不怕，反能借助。要我说，你就让李四张三互相争宠，只要你心里清楚，左右周旋，你就能处于不败之境。" --prompt-text "他闭上眼睛，期望这一切都能过去。然而，当他再次睁开眼睛，眼前的景象让他不禁倒吸一口气。雾气中出现的禁闭岛，陌生又熟悉，充满未知的危险。他握紧拳头，心知他的生活即将发生翻天覆地的改变。" --prompt-tokens "fake.npy" --checkpoint-path "checkpoints/fish-speech-1.5" --num-samples 2 --device cuda

!python tools/llama/generate.py
--text {target_text}
--prompt-text "The text corresponding to reference audio"
--prompt-tokens {reference_A_token}
--checkpoint-path "checkpoints/fish-speech-1.4"
--num-samples 2
--device cpu #cuda
```
3. create wave file
```
python tools/vqgan/inference.py -i "input/codes_0.npy" --checkpoint-path "checkpoints/fish-speech-1.5/firefly-gan-vq-fsq-8x1024-21hz-generator.pth" -d cuda -o "output/final.wav"

python tools/vqgan/inference.py -i "input" --checkpoint-path "checkpoints/fish-speech-1.5/firefly-gan-vq-fsq-8x1024-21hz-generator.pth" -d cuda -o "output/final.wav"
```

## Running as serve mode
```
python -m tools.api_server \
    --listen 0.0.0.0:8080 \
    --llama-checkpoint-path "checkpoints/fish-speech-1.5" \
    --decoder-checkpoint-path "checkpoints/fish-speech-1.5/firefly-gan-vq-fsq-8x1024-21hz-generator.pth" \
    --decoder-config-name firefly_gan_vq
```
#### CLIENT example
/opt/app/fish-speech/tools/api_client.py


# fishaudio/fish-speech: Brand new TTS solution

> ## Excerpt
> Brand new TTS solution. Contribute to fishaudio/fish-speech development by creating an account on GitHub.

---
This codebase and all models are released under CC-BY-NC-SA-4.0 License. Please refer to [LICENSE](https://github.com/fishaudio/fish-speech/blob/main/LICENSE) for more details.

We do not hold any responsibility for any illegal usage of the codebase. Please refer to your local laws about DMCA and other related laws.

## Quick Start for Local Inference
