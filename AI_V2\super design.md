---
title: superdesigndev/superdesign
source: https://github.com/superdesigndev/superdesign
author:
  - "[[GitHub]]"
published:
created: 2025-06-27
description: Contribute to superdesigndev/superdesign development by creating an account on GitHub.
tags:
  - AI/Cursor
  - AI/NoCode
  - AI/Tools
  - AI/SKOOL
reference:
---
**[superdesign](https://github.com/superdesigndev/superdesign)** Public

[View license](https://github.com/superdesigndev/superdesign/blob/main/LICENSE)

[Open in github.dev](https://github.dev/) [Open in a new github.dev tab](https://github.dev/) [Open in codespace](https://github.com/codespaces/new/superdesigndev/superdesign?resume=1)

<table><thead><tr><th colspan="2"><span>Name</span></th><th colspan="1"><span>Name</span></th><th><p><span>Last commit message</span></p></th><th colspan="1"><p><span>Last commit date</span></p></th></tr></thead><tbody><tr><td colspan="3"><p><span><a href="https://github.com/superdesigndev/superdesign/commit/1423f5574650754ce71f07527537751dac238568">Update package version to 0.0.6, add asset copying functionality in e…</a></span></p><p><span><a href="https://github.com/superdesigndev/superdesign/commit/1423f5574650754ce71f07527537751dac238568">1423f55</a> ·</span></p><p><a href="https://github.com/superdesigndev/superdesign/commits/main/"><span><span><span>59 Commits</span></span></span></a></p></td></tr><tr><td colspan="2"><p><a href="https://github.com/superdesigndev/superdesign/tree/main/.cursor">.cursor</a></p></td><td colspan="1"><p><a href="https://github.com/superdesigndev/superdesign/tree/main/.cursor">.cursor</a></p></td><td><p><a href="https://github.com/superdesigndev/superdesign/commit/b5ab31375dcb5bc723c11e21256682e7072f1684">Add git commit message guidelines</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/superdesigndev/superdesign/tree/main/.github/workflows"><span>.github/</span> <span>workflows</span></a></p></td><td colspan="1"><p><a href="https://github.com/superdesigndev/superdesign/tree/main/.github/workflows"><span>.github/</span> <span>workflows</span></a></p></td><td><p><a href="https://github.com/superdesigndev/superdesign/commit/a939e259a62b2a53bb432c5a0930447a0ae1ee9f">Enhance README and add GitHub Actions for publishing</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/superdesigndev/superdesign/tree/main/.superdesign">.superdesign</a></p></td><td colspan="1"><p><a href="https://github.com/superdesigndev/superdesign/tree/main/.superdesign">.superdesign</a></p></td><td><p><a href="https://github.com/superdesigndev/superdesign/commit/f2f693ddbfed170e47851f681c289aba32faf12e">fix canva interactions + layout</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/superdesigndev/superdesign/tree/main/.taskmaster">.taskmaster</a></p></td><td colspan="1"><p><a href="https://github.com/superdesigndev/superdesign/tree/main/.taskmaster">.taskmaster</a></p></td><td><p><a href="https://github.com/superdesigndev/superdesign/commit/1689849c3681ef4bad0156bdeafa993b1dfc49e8">zoom in/out control</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/superdesigndev/superdesign/tree/main/.vscode">.vscode</a></p></td><td colspan="1"><p><a href="https://github.com/superdesigndev/superdesign/tree/main/.vscode">.vscode</a></p></td><td><p><a href="https://github.com/superdesigndev/superdesign/commit/270c2a4012334fa5f1a52ef606fe116c284c62c0">remove preLuanchTask</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/superdesigndev/superdesign/tree/main/src">src</a></p></td><td colspan="1"><p><a href="https://github.com/superdesigndev/superdesign/tree/main/src">src</a></p></td><td><p><a href="https://github.com/superdesigndev/superdesign/commit/1423f5574650754ce71f07527537751dac238568">Update package version to 0.0.6, add asset copying functionality in e…</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/superdesigndev/superdesign/blob/main/.env.example">.env.example</a></p></td><td colspan="1"><p><a href="https://github.com/superdesigndev/superdesign/blob/main/.env.example">.env.example</a></p></td><td><p><a href="https://github.com/superdesigndev/superdesign/commit/ded99872a09fe5d08d76b5cee9d0d1f29af3fdf7">task master setup</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/superdesigndev/superdesign/blob/main/.gitignore">.gitignore</a></p></td><td colspan="1"><p><a href="https://github.com/superdesigndev/superdesign/blob/main/.gitignore">.gitignore</a></p></td><td><p><a href="https://github.com/superdesigndev/superdesign/commit/c84cba3c203bd963ce1c37d9b9514c87695a11ac">setup canva + file scan</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/superdesigndev/superdesign/blob/main/.vscode-test.mjs">.vscode-test.mjs</a></p></td><td colspan="1"><p><a href="https://github.com/superdesigndev/superdesign/blob/main/.vscode-test.mjs">.vscode-test.mjs</a></p></td><td><p><a href="https://github.com/superdesigndev/superdesign/commit/a52d8518b9fef5d3941f1380a73c67d2ceda3715">Initial setup of the superdesign VS Code extension, including core fu…</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/superdesigndev/superdesign/blob/main/.vscodeignore">.vscodeignore</a></p></td><td colspan="1"><p><a href="https://github.com/superdesigndev/superdesign/blob/main/.vscodeignore">.vscodeignore</a></p></td><td><p><a href="https://github.com/superdesigndev/superdesign/commit/a52d8518b9fef5d3941f1380a73c67d2ceda3715">Initial setup of the superdesign VS Code extension, including core fu…</a></p></td><td></td></tr><tr><td colspan="3"></td></tr></tbody></table>

SuperDesign is the first **open-source design agent** that lives right inside your IDE.  
Generate UI mockups, components, and wireframes directly from natural language prompts.  
Works seamlessly with Cursor, Windsurf, Claude Code, and plain VS Code.

> ✨ "Why design one option when you can explore ten?" — SuperDesign

---

## 🚀 Features

- 🖼️ **Product Mock**: Instantly generate full UI screens from a single prompt
- 🧩 **UI Components**: Create reusable components you can drop into your code
- 📝 **Wireframes**: Explore low-fidelity layouts for fast iteration
- 🔁 **Fork & Iterate**: Duplicate and evolve designs easily
- 📥 **Prompt-to-IDE**: Copy prompts into your favorite AI IDE (Cursor, Windsurf, Claude Code)

---

- [Cursor](https://www.cursor.sh/)
- [Windsurf](https://windsurf.ai/)
- [Claude Code](https://www.anthropic.com/index/claude-code)
- [Visual Studio Code](https://code.visualstudio.com/)

---

1. **Install the Extension** from the Cursor/VS Code Marketplace
2. Open the `SuperDesign` sidebar panel
3. Type a prompt (e.g., *"Design a modern login screen"*)
4. View generated mockups, components, and wireframes
5. Fork, tweak, and paste into your project

---

Your generated designs are saved locally inside `.superdesign/`.

---

## ❓ FAQ

**Is it free and open source?**  
Yes! Licensed under MIT — fork it, extend it, remix it.

**Can I customize the design agent?**  
Yes — use your own prompt templates, modify behaviors, or add commands.

**Can SuperDesign update existing UI?**  
Absolutely — select a component, describe the change, and let the agent do the rest.

**How can I contribute?**  
Pull requests are welcome. Star the repo and join us on [Discord](https://discord.gg/XYZ)!

---

## 🔗 Links

- 🌐 Website: [https://superdesign.dev](https://superdesign.dev/)
- 📦 GitHub: [https://github.com/your-org/superdesign](https://github.com/your-org/superdesign)
- 💬 Discord: [Join the Community](https://discord.gg/XYZ)
- 🐦 Twitter / X: [@SuperDesignDev](https://x.com/SuperDesignDev)

---

## 🪪 License

MIT — Built with ❤️ by the SuperDesign team

## Releases

[6 tags](https://github.com/superdesigndev/superdesign/tags)

## Packages

No packages published