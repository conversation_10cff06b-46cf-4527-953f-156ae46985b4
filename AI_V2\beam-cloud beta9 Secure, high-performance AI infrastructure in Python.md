---
title: "beam-cloud/beta9: Secure, high-performance AI infrastructure in Python."
source: https://github.com/beam-cloud/beta9
author:
  - "[[dils2k]]"
published:
created: 2025-07-11
description: Secure, high-performance AI infrastructure in Python. - beam-cloud/beta9
tags:
  - AI/Infra
reference:
---
**[beta9](https://github.com/beam-cloud/beta9)** Public

Secure, high-performance AI infrastructure in Python.

[beam.cloud](https://beam.cloud/ "https://beam.cloud")

[AGPL-3.0 license](https://github.com/beam-cloud/beta9/blob/main/LICENSE)

[Code of conduct](https://github.com/beam-cloud/beta9/blob/main/CODE_OF_CONDUCT.md)

[1k stars](https://github.com/beam-cloud/beta9/stargazers) [77 forks](https://github.com/beam-cloud/beta9/forks) [4 watching](https://github.com/beam-cloud/beta9/watchers) [Branches](https://github.com/beam-cloud/beta9/branches) [Tags](https://github.com/beam-cloud/beta9/tags) [Activity](https://github.com/beam-cloud/beta9/activity) [Custom properties](https://github.com/beam-cloud/beta9/custom-properties)

Public repository

[Open in github.dev](https://github.dev/) [Open in a new github.dev tab](https://github.dev/) [Open in codespace](https://github.com/codespaces/new/beam-cloud/beta9?resume=1)

<table><thead><tr><th colspan="2"><span>Name</span></th><th colspan="1"><span>Name</span></th><th><p><span>Last commit message</span></p></th><th colspan="1"><p><span>Last commit date</span></p></th></tr></thead><tbody><tr><td colspan="3"><p><span><a href="https://github.com/beam-cloud/beta9/commit/0f73614ac3323c9e792ff559dc98344d5f5a3d29">Fix panic when sending a request to an non-existent endpoint (</a><a href="https://github.com/beam-cloud/beta9/pull/1310">#1310</a><a href="https://github.com/beam-cloud/beta9/commit/0f73614ac3323c9e792ff559dc98344d5f5a3d29">)</a></span></p><p><span><a href="https://github.com/beam-cloud/beta9/commit/0f73614ac3323c9e792ff559dc98344d5f5a3d29">0f73614</a> ·</span></p><p><a href="https://github.com/beam-cloud/beta9/commits/main/"><span><span><span>1,422 Commits</span></span></span></a></p></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/beta9/tree/main/.github">.github</a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/beta9/tree/main/.github">.github</a></p></td><td><p><a href="https://github.com/beam-cloud/beta9/commit/55861dbaba35de28b12da78d80400a05c1cd2fa4">switch to uv for development (</a><a href="https://github.com/beam-cloud/beta9/pull/1067">#1067</a><a href="https://github.com/beam-cloud/beta9/commit/55861dbaba35de28b12da78d80400a05c1cd2fa4">)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/beta9/tree/main/.vscode">.vscode</a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/beta9/tree/main/.vscode">.vscode</a></p></td><td><p><a href="https://github.com/beam-cloud/beta9/commit/569f7020a6f95d4291a700215372d6c5707f64c3">Refactor build and switch to uv (</a><a href="https://github.com/beam-cloud/beta9/pull/1069">#1069</a><a href="https://github.com/beam-cloud/beta9/commit/569f7020a6f95d4291a700215372d6c5707f64c3">)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/beta9/tree/main/bin">bin</a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/beta9/tree/main/bin">bin</a></p></td><td><p><a href="https://github.com/beam-cloud/beta9/commit/a277288a1e82a46ba8f9f9e8237936654a55965a">Remove go get from setup script (</a><a href="https://github.com/beam-cloud/beta9/pull/1296">#1296</a><a href="https://github.com/beam-cloud/beta9/commit/a277288a1e82a46ba8f9f9e8237936654a55965a">)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/beta9/tree/main/cmd">cmd</a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/beta9/tree/main/cmd">cmd</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/beta9/tree/main/deploy">deploy</a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/beta9/tree/main/deploy">deploy</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/beta9/tree/main/docker">docker</a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/beta9/tree/main/docker">docker</a></p></td><td><p><a href="https://github.com/beam-cloud/beta9/commit/89cb7f26ae13586aa31fc56b78f82444f32752e8">include debug worker (w/ symbols) in all build images (</a><a href="https://github.com/beam-cloud/beta9/pull/1316">#1316</a><a href="https://github.com/beam-cloud/beta9/commit/89cb7f26ae13586aa31fc56b78f82444f32752e8">)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/beta9/tree/main/docs">docs</a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/beta9/tree/main/docs">docs</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/beta9/tree/main/e2e">e2e</a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/beta9/tree/main/e2e">e2e</a></p></td><td><p><a href="https://github.com/beam-cloud/beta9/commit/b36b7422ebfa9ccf3a83ce38af08f64a5fac7524">Fix: fix memory leak in endpoint buffer (</a><a href="https://github.com/beam-cloud/beta9/pull/808">#808</a><a href="https://github.com/beam-cloud/beta9/commit/b36b7422ebfa9ccf3a83ce38af08f64a5fac7524">)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/beta9/tree/main/hack">hack</a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/beta9/tree/main/hack">hack</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/beam-cloud/beta9/tree/main/manifests">manifests</a></p></td><td colspan="1"><p><a href="https://github.com/beam-cloud/beta9/tree/main/manifests">manifests</a></p></td><td></td><td></td></tr><tr><td colspan="3"></td></tr></tbody></table>

**[Beam](https://beam.cloud/?utm_source=github_readme)** is a fast, open-source runtime for serverless AI workloads. It gives you a Pythonic interface to deploy and scale AI applications with zero infrastructure overhead.

## ✨ Features

- **Fast Image Builds**: Launch containers in under a second using a custom container runtime
- **Parallelization and Concurrency**: Fan out workloads to 100s of containers
- **First-Class Developer Experience**: Hot-reloading, webhooks, and scheduled jobs
- **Scale-to-Zero**: Workloads are serverless by default
- **Volume Storage**: Mount distributed storage volumes
- **GPU Support**: Run on our cloud (4090s, H100s, and more) or bring your own GPUs

## 📦 Installation

```
pip install beam-client
```

## ⚡️ Quickstart

1. Create an account [here](https://beam.cloud/?utm_source=github_readme)
2. Follow our [Getting Started Guide](https://platform.beam.cloud/onboarding?utm_source=github_readme)

Spin up isolated containers to run LLM-generated code:

```
from beam import Image, Sandbox

sandbox = Sandbox(image=Image()).create()
response = sandbox.process.run_code("print('I am running remotely')")

print(response.result)
```

Create an autoscaling endpoint for your custom model:

```
from beam import Image, endpoint
from beam import QueueDepthAutoscaler

@endpoint(
    image=Image(python_version="python3.11"),
    gpu="A10G",
    cpu=2,
    memory="16Gi",
    autoscaler=QueueDepthAutoscaler(max_containers=5, tasks_per_container=30)
)
def handler():
    return {"label": "cat", "confidence": 0.97}
```

Schedule resilient background tasks (or replace your Celery queue) by adding a simple decorator:

```
from beam import Image, TaskPolicy, schema, task_queue

class Input(schema.Schema):
    image_url = schema.String()

@task_queue(
    name="image-processor",
    image=Image(python_version="python3.11"),
    cpu=1,
    memory=1024,
    inputs=Input,
    task_policy=TaskPolicy(max_retries=3),
)
def my_background_task(input: Input, *, context):
    image_url = input.image_url
    print(f"Processing image: {image_url}")
    return {"image_url": image_url}

if __name__ == "__main__":
    # Invoke a background task from your app (without deploying it)
    my_background_task.put(image_url="https://example.com/image.jpg")

    # You can also deploy this behind a versioned endpoint with:
    # beam deploy app.py:my_background_task --name image-processor
```

> Beta9 is the open-source engine powering [Beam](https://beam.cloud/), our fully-managed cloud platform. You can self-host Beta9 for free or choose managed cloud hosting through Beam.

## 👋 Contributing

We welcome contributions big or small. These are the most helpful things for us:

- Submit a [feature request](https://github.com/beam-cloud/beta9/issues/new?assignees=&labels=&projects=&template=feature-request.md&title=) or [bug report](https://github.com/beam-cloud/beta9/issues/new?assignees=&labels=&projects=&template=bug-report.md&title=)
- Open a PR with a new feature or improvement
[![](https://camo.githubusercontent.com/fefe0d85282a0ae747be3ed3b49a55b85eb3765314983739550f15db90cb8eb7/68747470733a2f2f636f6e747269622e726f636b732f696d6167653f7265706f3d6265616d2d636c6f75642f6265746139)](https://github.com/beam-cloud/beta9/graphs/contributors)

## Releases 985

[\+ 984 releases](https://github.com/beam-cloud/beta9/releases)

## Packages

No packages published  

## Deployments 500+

- [Release inactive](https://github.com/beam-cloud/beta9/deployments/Release)

[\+ more deployments](https://github.com/beam-cloud/beta9/deployments)