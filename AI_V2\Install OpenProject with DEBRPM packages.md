---
title: Install OpenProject with DEB/RPM packages
source:
  - https://www.openproject.org/docs/installation-and-operations/installation/packaged/#ubuntu-2204
author:
  - "[[OpenProject.org]]"
published: 
created: 2025-04-11
description: Open source project management software 
tags:
  - AI/OpenProject
---

# Memo Test

## Installation 
https://www.openproject.org/docs/installation-and-operations/installation/packaged/
### os installation
>  sudo apt-get update
 > sudo apt-get install apt-transport-https ca-certificates wget
 > sudo wget -O /etc/apt/trusted.gpg.d/openproject.asc https://dl.packager.io/srv/opf/openproject/key
 > sudo wget -O /etc/apt/sources.list.d/openproject.list \
 >  https://dl.packager.io/srv/opf/openproject/stable/15/installer/ubuntu/22.04.repo
 > sudo apt-get update
 > sudo apt-get install openproject

### generate cert key
sudo mkdir -p /etc/ssl/openproject
sudo openssl genrsa -out /etc/ssl/openproject/openproject.key 2048
sudo openssl req -new -x509 -key /etc/ssl/openproject/openproject.key -out /etc/ssl/openproject/openproject.crt -days 9999

### setup openproject
sudo openproject reconfigure #interactive - manual choices are stored in /etc/openproject/installer.dat
sudo openproject configure #non-interactive - using values stored in /etc/openproject/installer.dat

<span style="background:#40a9ff">FQDN name, we setup pmt.sbigenai.com</span>

![[Pasted image 20250414120126.png]]

You should be able to reach the OpenProject instance by visiting your installation at http://<openproject.example.com>/

You can then log in using the default user/password combination:

username = admin
password = admin

https://pmt.sbigenai.com/


---

# AWS setup

1. Security group for load balance and enable 0.0.0.0/0 able to accesss 443 port
2. Prepare public ip and dns name ptm.sbigenai.com
3. Security group for pmt and enable load balance ip 0.0.0.0/0 443 access to this pmt server
4. create target grp and Protcol TLS:443 health check method https:/
5. create load balance (NLB type)

---
# Backup and Restore
ubuntu@SBIHD-GenAI-prd-LVM-PMT:~$ sudo openproject run backup
* Generating database backup... done
/var/db/openproject/backup/postgresql-dump-20250424130501.pgdump
* No SVN repositories folder. Ignoring.
* Generating Git repositories backup... done
/var/db/openproject/backup/git-repositories-20250424130501.tar.gz
* Generating attachments backup... done
/var/db/openproject/backup/attachments-20250424130501.tar.gz
* Saving configuration... done
/var/db/openproject/backup/conf-20250424130501.tar.gz

https://www.openproject.org/docs/installation-and-operations/operation/restoring/



# OpenProject MCP

https://www.openproject.org/docs/api/example/
https://github.com/jessebautista/mcp-openproject-smithery


---
# Japanese Font Support

## OS installation and OpenProject initialization script
sudo apt-get install -y \
  fonts-noto-cjk \
  fonts-ipafont \
  fonts-ipafont-gothic \
  fonts-ipafont-mincho \
  language-pack-ja \
  language-pack-ja-base \
  fontconfig \
  fonts-takao \
  fonts-vlgothic \
  fonts-liberation \
  fonts-liberation2
  ```
  # Update font cache
  sudo fc-cache -fv
  
  # Verify Japanese fonts are installed
  fc-list | grep -E "Noto|IPA|Takao" | grep -i jp
  
  # Check for specific Japanese fonts
  fc-list :lang=ja
  
  # Install wkhtmltopdf dependencies if needed
  sudo apt-get install -y libxrender1 libxext6 libx11-6
  
  # Set locale (optional but recommended)
  -- sudo locale-gen ja_JP.UTF-8
  -- sudo update-locale
  
  
  sudo openproject restart
  
  
  /etc/fonts/local.conf
  <?xml version="1.0"?>
  <!DOCTYPE fontconfig SYSTEM "fonts.dtd">
  <fontconfig>
    <alias>
      <family>sans-serif</family>
      <prefer>
        <family>Noto Sans CJK JP</family>
        <family>IPAGothic</family>
      </prefer>
    </alias>
  </fontconfig>

cat custom_pdf_fonts.rb
# config/initializers/custom_pdf_fonts.rb
# Simplified version - no complex method aliasing

Rails.application.config.after_initialize do
  # Find Japanese font
  font_path = [
    "/usr/share/fonts/opentype/ipafont-gothic/ipag.ttf",
    "/usr/share/fonts/opentype/ipafont-mincho/ipam.ttf"
  ].find { |path| File.exist?(path) }

  if font_path
    Rails.logger.info "Found Japanese font at: #{font_path}"
    
    # Simple approach: Set font path as a constant
    if defined?(Prawn)
      JAPANESE_FONT_PATH = font_path
      
      # Monkey patch to register font on each document
      Prawn::Document.class_eval do
        old_initialize = instance_method(:initialize)
        
        define_method(:initialize) do |*args, &block|
          old_initialize.bind(self).call(*args, &block)
          
          # Register font after initialization
          if respond_to?(:font_families) && defined?(JAPANESE_FONT_PATH)
            font_families.update(
              "IPAGothic" => {
                normal: JAPANESE_FONT_PATH,
                bold: JAPANESE_FONT_PATH,
                italic: JAPANESE_FONT_PATH,
                bold_italic: JAPANESE_FONT_PATH
              }
            )
          end
        end
      end
      
      Rails.logger.info "Japanese font support enabled"
    end
  else
    Rails.logger.error "No Japanese font found!"
  end
end

sudo cat pdf_metadata_encoding_jp.rb
# config/initializers/pdf_metadata_encoding_fix.rb
# Fix PDF metadata encoding for Japanese characters

Rails.application.config.after_initialize do
  if defined?(Prawn::Document)
    Prawn::Document.class_eval do
      # Override the info setter to handle UTF-8 properly
      alias_method :original_info=, :info= if method_defined?(:info=)
      
      def info=(info_hash)
        # Process each metadata field
        encoded_info = {}
        info_hash.each do |key, value|
          if value.is_a?(String) && value.encoding == Encoding::UTF_8
            # Check if string contains non-ASCII characters
            if value.ascii_only?
              encoded_info[key] = value
            else
              # Convert to UTF-16BE with BOM for PDF metadata
              # This is the standard encoding for non-ASCII PDF metadata
              encoded_value = "\xFE\xFF" + value.encode('UTF-16BE', 'UTF-8')
              encoded_info[key] = encoded_value.force_encoding('ASCII-8BIT')
            end
          else
            encoded_info[key] = value
          end
        end
        
        # Call original method with encoded values
        if respond_to?(:original_info=)
          self.original_info = encoded_info
        else
          @info = encoded_info
        end
      end
    end
    
    Rails.logger.info "PDF metadata encoding fix applied"
  end
end
  
vi  /opt/openproject/app/models/work_package/pdf_export/export/standard.yml 
  
  cat /opt/openproject/app/models/work_package/pdf_export/export/standard.yml
  page:
    page_size: "A4"
    margin_top: 60
    margin_bottom: 60
    margin_left: 36
    margin_right: 36
    page_break_threshold: 200
    link_color: "175A8E"
  
  page_header:
    offset: 20
    size: 8
  
  page_footer:
    offset: -30
    size: 8
    spacing: 6
  
  page_logo:
    height: 20
    align: "right"
  
  page_heading:
    font: "IPAGothic"
    size: 14
    styles: [ "bold" ]
    margin_bottom: 10
  
  overview:
    group_heading:
      size: 11
      styles: [ "bold" ]
      margin_bottom: 10
    table:
      subject_indent: 8
      margin_bottom: 20
      cell:
        size: 9
        color: "000000"
        padding_left: 5
        padding_right: 5
        padding_top: 0
        padding_bottom: 5
        border_width: 0.25
      cell_header:
        size: 9
        styles: [ "bold" ]
      cell_sums:
        size: 8
        styles: [ "bold" ]
  
  toc:
    subject_indent: 4
    indent_mode: "stairs"
    margin_top: 10
    margin_bottom: 20
    item:
      size: 9
      color: "000000"
      margin_bottom: 4
    item_level_1:
      size: 10
      styles: [ "bold" ]
      margin_top: 4
      margin_bottom: 4
    item_level_2:
      size: 10
  
  work_package:
    margin_bottom: 14
    subject:
      font: "IPAGothic"
      size: 10
      margin_bottom: 10
    subject_level_1:
      font: "IPAGothic"
      size: 14
      styles: [ "bold" ]
    subject_level_2:
      size: 13
      styles: [ "bold" ]
    subject_level_3:
      size: 12
      styles: [ "bold" ]
    subject_level_4:
      size: 11
      styles: [ "bold" ]
    subject_level_5:
      styles: [ "bold" ]
    subject_level_6:
      styles: [ "bold" ]
    subject_level_7:
      styles: [ "bold" ]
    subject_level_8:
      styles: [ "bold" ]
    subject_level_9:
      styles: [ "bold" ]
    subject_level_10:
      styles: [ "bold" ]
    attributes_table:
      margin_bottom: 16
      cell:
        size: 9
        color: "000000"
        padding_left: 5
        padding_right: 5
        padding_top: 0
        padding_bottom: 5
        border_color: "4B4B4B"
        border_width: 0.25
      cell_label:
        styles: [ "bold" ]
    markdown_label:
      font: "IPAGothic"
      size: 12
      styles: [ "bold" ]
      margin_top: 0
      margin_bottom: 4
    markdown_margin:
      margin_bottom: 16
    markdown:
      font: 
        font: "IPAGothic"
        size: 10
        leading: 3
      header:
        font: "IPAGothic"
        size: 8
        styles: [ "bold" ]
        padding_top: 4
      header_1:
        size: 10
      header_2:
        size: 10
      header_3:
        size: 9
      paragraph:
        font: "IPAGothic"
        size: 10
        color: '000000'
        align: "left"
      unordered_list:
        spacing: 1
      unordered_list_point:
        spacing: 4
      ordered_list:
        spacing: 1
      ordered_list_point:
        spacing: 4
        spanning: true
        list_style_type: decimal
      ordered_list_point_2:
        list_style_type: lower-latin
      ordered_list_point_3:
        list_style_type: lower-roman
      ordered_list_point_4:
        list_style_type: upper-latin
      ordered_list_point_5:
        list_style_type: upper-roman
      task_list:
        spacing: 1
      task_list_point:
        spacing: 4
        checked: "☑"
        unchecked: "☐"
      link:
        color: "175A8E"
        styles: [ ]
      code:
        color: "880000"
        size: 9
        font: "SpaceMono"
      blockquote:
        background_color: "f4f9ff"
        size: 10
        styles: [ "italic" ]
        color: "0f3b66"
        border_color: "b8d6f4"
        border_width: 1
        padding: 4
        padding_left: 6
        margin_top: 4
        margin_bottom: 4
        no_border_left: false
        no_border_right: true
        no_border_bottom: true
        no_border_top: true
      image:
        align: "center"
        margin_bottom: 4
        caption:
          size: 8
          align: "center"
      codeblock:
        background_color: "F5F5F5"
        color: "880000"
        padding: 10
        size: 8
        margin_top: 10
        margin_bottom: 10
        font: "SpaceMono"
      table:
        auto_width: true
        margin_top: 4
        margin_bottom: 4
        header:
          size: 9
          styles: [ "bold" ]
          background_color: "F0F0F0"
        cell:
          size: 8
          border_width: 0.25
          padding: 5
      html_table:
        auto_width: true
        margin_top: 4
        margin_bottom: 4
        header:
          size: 9
          styles: [ "bold" ]
        cell:
          size: 8
          border_width: 0.25
          padding: 5
      alerts:
        NOTE:
          border_color: '0969da'
          alert_color: '0969da'
          padding: '4mm'
          size: 10
          styles: [ ]
          border_width: 2
          no_border_right: true
          no_border_left: false
          no_border_bottom: true
          no_border_top: true
        TIP:
          border_color: '1a7f37'
          alert_color: '1a7f37'
          padding: '4mm'
          size: 10
          styles: [ ]
          border_width: 2
          no_border_right: true
          no_border_left: false
          no_border_bottom: true
          no_border_top: true
        IMPORTANT:
          border_color: '8250df'
          alert_color: '8250df'
          padding: '4mm'
          size: 10
          styles: [ ]
          border_width: 2
          no_border_right: true
          no_border_left: false
          no_border_bottom: true
          no_border_top: true
        WARNING:
          border_color: 'bf8700'
          alert_color: 'bf8700'
          padding: '4mm'
          size: 10
          styles: [ ]
          border_width: 2
          no_border_right: true
          no_border_left: false
          no_border_bottom: true
          no_border_top: true
        CAUTION:
          border_color: 'd1242f'
          alert_color: 'd1242f'
          size: 10
          styles: [ ]
          padding: '4mm'
          border_width: 2
          no_border_right: true
          no_border_left: false
          no_border_bottom: true
          no_border_top: true
  
  cover:
    header:
      logo_height: 25
      border:
        color: 'd3dee3'
        height: 1
        offset: 6
    footer:
      size: 10
      color: '414d5f'
      offset: 30
    hero:
      padding_right: 144
      padding_top: 120
      title:
        max_height: 30
        spacing: 10
        font: 'SpaceMono'
        color: '414d5f'
        size: 10
      heading:
        spacing: 14
        color: '414d5f'
        styles:
          - bold
        size: 16
      dates:
        spacing: 4
        max_height: 16
        color: '414d5f'
        size: 10
        styles:
          - bold
      subheading:
        max_height: 30
        color: '414d5f'
        size: 10

# 3. Restart OpenProject (CRITICAL - changes won't take effect without restart)
sudo openproject restart

# 4. Clear cache (if OpenProject uses Rails cache)
sudo openproject run rails runner "Rails.cache.clear"


  ```
  ## Build 3.4.2 ruby version under ubuntu user
  ```
  # build same 3.4.2 verson under ubuntu user 
  sudo apt update && sudo apt install build-essential ruby-dev
  sudo apt install zlib1g-dev libssl-dev libyaml-dev libreadline-dev
  sudo apt install libpq-dev
  sudo cp /opt/openproject/.ruby-version.backup /opt/openproject/.ruby-version
  export PATH="$HOME/.rbenv/bin:$PATH" && eval "$(rbenv init -)" && rbenv install 3.4.2
  export PATH="$HOME/.rbenv/bin:$PATH" && eval "$(rbenv init -)" && rbenv global 3.4.2 && gem install bundler
  sudo chown -R ubuntu:ubuntu /opt/openproject
  export PATH="$HOME/.rbenv/bin:$PATH" && eval "$(rbenv init -)" && cd /opt/openproject && bundle install
   Installed ruby-3.4.2 to /home/<USER>/.rbenv/versions/3.4.2
   
  # change back to openproject user
  systemctl status openproject-web-1.service
  cat /etc/systemd/system/openproject-web-1.service
  sudo chown -R openproject:openproject /opt/openproject
   
  #run validate_styles 
   export PATH="$HOME/.rbenv/bin:$PATH" && eval "$(rbenv init -)" && cd /opt/openproject && bundle exec script/pdf_export/validate_styles
  Valid: /opt/openproject/app/models/work_package/pdf_export/export/standard.yml
  bundle exec /opt/openproject/script/pdf_export/validate_styles
  
  
  
  ```

---

# Plugin
https://www.openproject.org/docs/system-admin-guide/integrations/

https://www.openproject.org/docs/system-admin-guide/integrations/gitlab-integration/

---
> Important
> 
> We will not build packages for new Linux versions, such as Ubuntu 24.04. We will, however, keep releasing new package versions for the currently supported Linux versions until their EOL (end of life).

The packaged installation of OpenProject is the recommended way to install and maintain OpenProject using DEB or RPM packages.

The package will:

- guide you through all the required steps
- install all the required libraries and dependencies
- install a local PostgreSQL database or allow you to connect to an existing PostgreSQL database
- allow you to install and configure an outer Apache web server (recommended)
- setup SSL/TLS encryption for the Apache server (optional)
- configure repositories (Git/SVN) (optional)
- configure email settings

The package is available for the following Linux distributions:

| Distribution (64 bits only) |
| --- |
| [Ubuntu 22.04 Jammy](https://www.openproject.org/docs/installation-and-operations/installation/packaged/#ubuntu-2204) |
| [Ubuntu 20.04 Focal](https://www.openproject.org/docs/installation-and-operations/installation/packaged/#ubuntu-2004) |
| [Debian 12 Bookworm](https://www.openproject.org/docs/installation-and-operations/installation/packaged/#debian-12) |
| [Debian 11 Bullseye](https://www.openproject.org/docs/installation-and-operations/installation/packaged/#debian-11) |
| [CentOS/RHEL 9.x](https://www.openproject.org/docs/installation-and-operations/installation/packaged/#centos-9--rhel-9) |
| [Suse Linux Enterprise Server 15](https://www.openproject.org/docs/installation-and-operations/installation/packaged/#sles-15) |

Please ensure that you are running on a 64bit system before proceeding with the installation. You can check by running the `uname -i` command on the target server and verifying that it outputs `x86_64`:

```shell
$ uname -m
x86_64
```

> Important
> 
> Please note that the packaged installation works best when running on a dedicated server or virtual machine, as we cannot ensure that the components installed and configured by the OpenProject installer will work on systems that have been already customized. If you must install OpenProject on a server where other software is running, or with an already configured Apache or NginX server, then you should have a look at the Docker-based installation instead.

## Ubuntu Installation

### Ubuntu 22.04

Update the `apt` package index and install packages to allow `apt` to use a repository over HTTPS:

```shell
sudo apt-get update
sudo apt-get install apt-transport-https ca-certificates wget
```

Import the PGP key used to sign our packages:

```shell
sudo wget -O /etc/apt/trusted.gpg.d/openproject.asc https://dl.packager.io/srv/opf/openproject/key
```

Add the OpenProject package source:

```shell
sudo wget -O /etc/apt/sources.list.d/openproject.list \
  https://dl.packager.io/srv/opf/openproject/stable/15/installer/ubuntu/22.04.repo
```

Download the OpenProject package:

```shell
sudo apt-get update
sudo apt-get install openproject
```

Then finish the installation by reading the [*Initial configuration*](https://www.openproject.org/docs/installation-and-operations/installation/packaged/#initial-configuration) section.

 <video controls=""><source src="https://openproject-docs.s3.eu-central-1.amazonaws.com/videos/openproject-installation-ubuntu.mp4" type="video/mp4"> Your browser does not support the video tag.</video>

### Ubuntu 20.04

Update the `apt` package index and install packages to allow `apt` to use a repository over HTTPS:

```shell
sudo apt-get update
sudo apt-get install apt-transport-https ca-certificates wget
```

Import the PGP key used to sign our packages:

```shell
wget -qO- https://dl.packager.io/srv/opf/openproject/key | sudo apt-key add -
```

Add the OpenProject package source:

```shell
sudo wget -O /etc/apt/sources.list.d/openproject.list \
  https://dl.packager.io/srv/opf/openproject/stable/15/installer/ubuntu/20.04.repo
```

Download the OpenProject package:

```shell
sudo apt-get update
sudo apt-get install openproject
```

Then finish the installation by reading the [*Initial configuration*](https://www.openproject.org/docs/installation-and-operations/installation/packaged/#initial-configuration) section.

## Debian Installation

### Debian 12

As root update the `apt` package index and install packages to allow `apt` to use a repository over HTTPS:

```shell
su -
apt update
apt install apt-transport-https ca-certificates wget gpg
```

Import the PGP key used to sign our packages:

```shell
wget -qO- https://dl.packager.io/srv/opf/openproject/key | gpg --dearmor > /etc/apt/trusted.gpg.d/packager-io.gpg
```

Add the OpenProject package source:

```shell
wget -O /etc/apt/sources.list.d/openproject.list \
  https://dl.packager.io/srv/opf/openproject/stable/15/installer/debian/12.repo
```

Download the OpenProject package:

```shell
apt update
apt install openproject
```

Then finish the installation by reading the [*Initial configuration*](https://www.openproject.org/docs/installation-and-operations/installation/packaged/#initial-configuration) section.

### Debian 11

As root update the `apt` package index and install packages to allow `apt` to use a repository over HTTPS:

```shell
su -
apt update
apt install apt-transport-https ca-certificates wget gpg
```

Import the PGP key used to sign our packages:

```shell
wget -qO- https://dl.packager.io/srv/opf/openproject/key | gpg --dearmor > /etc/apt/trusted.gpg.d/packager-io.gpg
```

Add the OpenProject package source:

```shell
wget -O /etc/apt/sources.list.d/openproject.list \
  https://dl.packager.io/srv/opf/openproject/stable/15/installer/debian/11.repo
```

Download the OpenProject package:

```shell
apt update
apt install openproject
```

Then finish the installation by reading the [*Initial configuration*](https://www.openproject.org/docs/installation-and-operations/installation/packaged/#initial-configuration) section.

## CentOS Installation

### CentOS 9 / RHEL 9

Add the OpenProject package source:

```shell
sudo wget -O /etc/yum.repos.d/openproject.repo \
  https://dl.packager.io/srv/opf/openproject/stable/15/installer/el/9.repo
```

If it is not already enabled, make sure to enable [Extra Packages for Enterprise Linux](https://fedoraproject.org/wiki/EPEL) (EPEL).

```shell
sudo subscription-manager repos --enable codeready-builder-for-rhel-9-$(arch)-rpms
sudo dnf install https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm

# if using CentOS 9:
# sudo dnf config-manager --set-enabled crb
# sudo dnf install https://dl.fedoraproject.org/pub/epel/epel{,-next}-release-latest-9.noarch.rpm
```

Download the OpenProject package:

```shell
sudo yum install openproject
```

Then finish the installation by reading the [*Initial configuration*](https://www.openproject.org/docs/installation-and-operations/installation/packaged/#initial-configuration) section.

> Note
> 
> On this distribution full-text extraction for attachments [*is not supported*](https://www.openproject.org/docs/installation-and-operations/installation/packaged/#full-text-extraction-not-supported) by default.

## SUSE Linux Enterprise Server (SLES) Installation

> Note
> 
> On SLES, full-text extraction for attachments [*is not supported*](https://www.openproject.org/docs/installation-and-operations/installation/packaged/#full-text-extraction-not-supported) by default.

### SLES 15

Add the OpenProject package source:

```shell
wget -O /etc/zypp/repos.d/openproject.repo \
  https://dl.packager.io/srv/opf/openproject/stable/15/installer/sles/15.repo
```

If you already had an old package source that is being updated you must refresh your source next. It can’t hurt to do this in any case, though.

```shell
sudo zypper refresh openproject
```

Next, download the OpenProject package:

```shell
sudo zypper install openproject
```

Then finish the installation by reading the [*Initial configuration*](https://www.openproject.org/docs/installation-and-operations/installation/packaged/#initial-configuration) section.

## Full-text extraction not supported

For some distributions we do not provide the required dependencies for full-text extraction of attachments. If you need this feature, please install the required dependencies ( `catdoc unrtf poppler-utils tesseract-ocr` ) manually. For more information, [please see the plaintext gem](https://github.com/planio-gmbh/plaintext). Once installed, check `Administration > Information` to see if OpenProject is able to pick up these dependencies.

## Initial Configuration

After you have successfully installed the OpenProject package, you can now perform the initial configuration of OpenProject, using the wizard that ships with the OpenProject package.

## Prerequisites

- If you wish to connect to an existing database server instead of setting up a local database server, please make sure to have your database hostname, port, username and password ready. The database username used to connect to the existing database must have the CREATE DATABASE privilege.
- If you want to enable HTTPS, then you will need to provide the path (on the server) to your certificate file, private key file, and CA bundle file.

## Step 0: Start the wizard

To start the configuration wizard, please run the following command with `sudo`, or as root:

```shell
sudo openproject reconfigure #interactive - manual choices are stored in /etc/openproject/installer.dat
sudo openproject configure #non-interactive - using values stored in /etc/openproject/installer.dat
```

> Note
> 
> - Every time you will run the OpenProject wizard, by using `sudo openproject reconfigure` your choices will be persisted in a configuration file at `/etc/openproject/installer.dat` and subsequent executions of `sudo openproject configure` will re-use these values, only showing you the wizard steps for options you have not yet been asked for.
> - In the interactive way you can skip dialogs you do not want to change simply by confirming them with `ENTER`.

## Step 1: Select your OpenProject edition

OpenProject comes in two editions:

- the default edition, which is targeted at general project management.
- the BIM edition, which is specifically target at the construction industry.

![select-edition](https://www.openproject.org/docs/installation-and-operations/installation/packaged/select-edition-93fad7fc.png)

You can find more about the BIM edition on [this page](https://www.openproject.org/bim-project-management/).

> This wizard step is only available on the following distributions:
> 
> - RHEL/CentOS 9
> - Ubuntu 22.04
> - Ubuntu 20.04
> - Debian 11
> 
> On older distributions, this wizard step won’t be displayed, and the installation will default to the default edition.

## Step 2: PostgreSQL database configuration

OpenProject requires a PostgreSQL database to store your data. This wizard step allows you to choose an option for the PostgreSQL database connection:

![01-postgres](https://www.openproject.org/docs/installation-and-operations/installation/packaged/01-postgres-ab79a402.png)

The dialog allows you to choose from three options:

### Install a new PostgreSQL server and database locally (default)

Choose this option if you want OpenProject to set up and configure a local database server manually. This is the best choice if you are unfamiliar with administering databases, or do not have a separate PostgreSQL database server installed that you want to connect to.

> Note
> 
> If you would like to use the database that was automatically installed by OpenProject at time of installation just choose `install` again

### Use an existing PostgreSQL database

Choose this option if you have a PostgreSQL database server installed either on the same host as the OpenProject package is being installed on, or on another server you can connect to from this machine.

The wizard will show you multiple additional steps in this case to enter the hostname, username & password as well as the database name for the PostgreSQL database.

The wizard will not try to connect to any database. You will have to specify a database manually through the `DATABASE_URL` environment variable. If you choose skip and did not set a `DATABASE_URL`, the configuration process will fail.

You can set this `DATABASE_URL` parameter yourself to a PostgreSQL database URL.

```shell
sudo openproject config:set DATABASE_URL="postgresql://[user[:password]@][host][:port][/dbname][?param1=value1&...]
```

## Step 3: Apache2 web server and SSL termination

OpenProject comes with an internal ruby application server, but this server only listens on a local interface, usually on port 6000. To receive connections from the outside world, it needs a web server that will act as a proxy to forward incoming connections to the OpenProject application server.

This wizard step allows you to auto-install an Apache2 web server to function as that reverse proxy.

![02a-apache](https://www.openproject.org/docs/installation-and-operations/installation/packaged/02a-apache-3c082cfd.png)

The available options are:

### Install Apache2 web server (default)

We recommend that you let OpenProject install and configure the outer web server, in which case we will install an Apache2 web server with a VirtualHost listening to the domain name you specify, optionally providing SSL/TLS termination.

> Note
> 
> In case you re-run `sudo openproject reconfigure` later it is mandatory to select `install` at the webserver again

In case you have selected to install Apache2, multiple dialogs will request the parameters for setting it up:

#### Domain name

Enter the fully qualified domain (FQDN) where your OpenProject installation will be reached at. This will become the `ServerName` of your apache VirtualHost and is also used to generate full links from OpenProject, such as in emails.

![02b-hostname](https://www.openproject.org/docs/installation-and-operations/installation/packaged/02b-hostname-6b1ed70c.png)

#### Server path prefix

If you wish to install OpenProject under a server path prefix, such as `yourdomain.example.com/openproject`, please specify that prefix here with a leading slash. For example: `/openproject`. If OpenProject should respond to `http(s)://yourdomain.example.com` as specified in the previous dialog, simply leave this dialog empty and confirm by pressing `ENTER`.

![02c-prefix](https://www.openproject.org/docs/installation-and-operations/installation/packaged/02c-prefix-d457fe71.png)

#### SSL/TLS configuration

> Note
> 
> With OpenProject version 12.2 **HTTPS configuration** was set to be **default** for every installation. **Now best practice is to proceed by selecting `yes` for using HTTPS (SSL/TLS)** and generating the needed certificates, otherwise you will have to manually deactivate HTTPS on the command line.

OpenProject can configure Apache to support HTTPS (SSL/TLS). If you have SSL certificates and want to use SSL/TLS (recommended), select **Yes**.

In that case, you will be shown three additional dialogs to enter the certificate details:

1. The absolute SSL certificate path
2. The absolute SSL private key path
3. The path to the Certificate Authority bundle for the certificate (optional, leave empty unless needed)

![02d-ssl](https://www.openproject.org/docs/installation-and-operations/installation/packaged/02d-ssl-37fba2f8.png)

Enabling this mode will result in OpenProject only responding to HTTPS requests, and upgrade any non-secured requests to HTTPS. It will also output HTTP Strict Transport Security (HSTS) headers to the client.

#### External SSL/TLS termination

> Note
> 
> If you terminate SSL externally before the request hits the OpenProject server, you need to follow the following instructions to avoid errors in routing. If you want to use SSL on the server running OpenProject, skip this section.

If you have a separate server that is terminating SSL and only forwarding/proxying to the OpenProject server, you must select “No” in this dialog. However, there are some parameters you need to put into your outer configuration.

- If you’re proxying to the openproject server, you need to forward the HOST header to the internal server. This ensures that the host name of the outer request gets forwarded to the internal server. Otherwise you might see redirects in your browser to the internal host that OpenProject is running on.
	- In Apache2, set the `ProxyPreserveHost On` directive
	- In NginX, use the following value: `proxy_set_header X-Forwarded-Host $host:$server_port;`
- If you’re terminating SSL on the outer server, you need to set the `X-Forwarded-Proto https` header to let OpenProject know that the request is HTTPS, even though its been terminated earlier in the request on the outer server.
	- In Apache2, use `RequestHeader set "X-Forwarded-Proto" https`
	- In Nginx, use `proxy_set_header X-Forwarded-Proto https;`
- Finally, to let OpenProject know that it should create links with ‘https’ when no request is available (for example, when sending emails), you need to set the following setting: `openproject config:set SERVER_PROTOCOL_FORCE_HTTPS="true"` followed by an `openproject configure`. This ensures that OpenProject responds correctly with secure cookies even though it was not configured for https in the server configuration.

Here an example for external SSL/TLS termination with apache (httpd):

> Note
> 
> There is [another example](https://www.openproject.org/docs/installation-and-operations/installation/docker/#1-virtual-host-root) for external SSL/TLS termination for **docker-compose** installations

```shell
<VirtualHost *:443>
   ServerName openproject.example.com
   
   # Logging
   LogLevel Warn
   ErrorLog /var/log/httpd/openproject.example.com-error.log
   CustomLog /var/log/httpd/openproject.example.com-access.log combined
   
   # Reverse Proxy
   ProxyPreserveHost On
   ProxyRequests Off
   ProxyPass / http://[OPENPROJECT-HOST-IP]/
   ProxyPassReverse / http://[OPENPROJECT-HOST-IP]/
   #ProxyPass / https://[OPENPROJECT-HOST-IP]/               # if openproject's internal apache2 server/ssl is YES 
   #ProxyPassReverse / https://[OPENPROJECT-HOST-IP]/        # if openproject's internal apache2 server/ssl is YES
   
   # Request Header
   RequestHeader set "X-Forwarded-Proto" https
   
   # SSL Certificate that was created by LetsEncrypt
   Include /etc/letsencrypt/options-ssl-apache.conf
   SSLEngine On
   #SSLProxyEngine On                                        # if openproject's internal apache2 server/ssl is YES
   SSLCertificateFile /etc/letsencrypt/live/openproject.example.com/cert.pem
   SSLCertificateKeyFile /etc/letsencrypt/live/openproject.example.com/privkey.pem
   SSLCertificateChainFile /etc/letsencrypt/live/openproject.example.com/chain.pem                       # optional
</VirtualHost>
```

> Note
> 
> Skipping step 3 Apache2 web server install will ask later in step 7 for information about the hostname and HTTPS

The installer will not set up an external web server for accessing. You will need to either install and set up a web server such as Apache2 or Nginx to function as the web server forwarding to our internal server listening at `localhost:6000` by proxying.

Only choose this option if you have a local Apache2 installed that the OpenProject package may not control, or need to use a different web server such as Nginx. Please note that not all functionality (especially regarding Repositories) are supported on Nginx.

When installing with an existing Apache2, you can take a look at the source of our [installation templates](https://github.com/pkgr/addon-apache2/tree/master/conf) for guidance on how to set up the integration.

[Here’s an exemplary configuration](https://www.openproject.org/docs/installation-and-operations/installation/packaged/openproject-apache-example.conf) that might work for you.

[For a minimal nginx config, please see this gist](https://gist.github.com/seLain/********************************) as as starting point.

> Important
> 
> If you reconfigure the OpenProject application and switch to `skip`, you might run into errors with the Apache configuration file, as that will not be automatically remove. Please double-check you removed references to the `openproject.conf` if you do reconfigure.

## Step 4: SVN/Git integration server

If you have selected to auto-install an Apache2 web server, you will be asked whether you want to install Git and Subversion repository support. In case you do not need it or when in doubt, choose **Skip** for both options.

For more information, [see our help on repositories](https://www.openproject.org/docs/user-guide/repository)

![03-repos](https://www.openproject.org/docs/installation-and-operations/installation/packaged/03-repos-33d793d7.png)

## Step 5: Administrator email

The wizard will ask you for an administrative email address so that it can create the administrator account with that email for the initial login. Enter your email address to have it tied to the admin account.

![05-admin](https://www.openproject.org/docs/installation-and-operations/installation/packaged/05-admin-8449619f.png)

## Step 6: Memcached server

OpenProject heavily relies on caching, which is why the wizard suggests you to install a local memcached server the OpenProject instances can connect to. You should always set this to `install` unless you have a reason to configure another caching mechanism - for example when configuring multiple shared instances of OpenProject.

![06-cache](https://www.openproject.org/docs/installation-and-operations/installation/packaged/06-cache-03f7bd54.png)

## Step 7: Host name and Protocol (if step 3 was skipped)

> Note
> 
> This step is only shown if you decided to skip step 3, the Apache2 installation. OpenProject still needs to know what external host name you’re running on, as well as if you’re using HTTPS or not.

First, enter the fully qualified domain where your OpenProject installation will be reached at. This will be used to generate full links from OpenProject, such as in emails.

![Select the OpenProject host name](https://www.openproject.org/docs/installation-and-operations/installation/packaged/07a-hostname-a98d8d48.png)

Next, tell OpenProject whether you have SSL termination enabled somewhere in your stack. Please note that you need to set up protocol forwarding by the means mentioned in the [Skip Apache2 Installation](https://www.openproject.org/docs/installation-and-operations/installation/packaged/#skip-apache2-web-server-install-not-recommended) at step 3 above.

![HTTPS setting](https://www.openproject.org/docs/installation-and-operations/installation/packaged/07b-protocol-bb9aa376.png)

## Step 8: Default language

> Note
> 
> This step is only shown on the very first installation of OpenProject, as it affects only the initial seeding of the basic and demo data. Changing this value after installation will have no effect.

OpenProject can be used with a wide variety of languages. The initial data of the instance (basic data such as status names, types, etc.) as well as data for demonstrational purposes will be created in the language you select in this screen. Move through the list using the arrow keys and select the default language.

Also, this setting will control what is the default language for new users if their browser language is not available in the system.

![Default language screen](https://www.openproject.org/docs/installation-and-operations/installation/packaged/08-default-language-e6d2bffb.png)

## Result

With this last step confirmed, the OpenProject wizard will complete, and apply all the configuration options that you have just selected. This might take a few minutes depending on your machine and internet connection, as OpenProject might need to install additional packages (such as the web server, database) depending on your selections.

In case this process crashes or exits with an obvious error, please keep the output and send your configuration from `/etc/openproject/installer.dat` (removing any passwords from it) to us at [<EMAIL>](https://www.openproject.org/docs/installation-and-operations/installation/packaged/), or [reach out to the community forums](https://community.openproject.org/projects/openproject/forums).

When this process completes, it will have started the internal application and web servers, the background jobs to process work-intensive jobs, and set up the connection to the database.

You should be able to reach the OpenProject instance by visiting your installation at `http://<openproject.example.com>/<server prefix>`.

You can then log in using the default user/password combination:

- username = `admin`
- password = `admin`

You will be asked to change this password immediately after the first login.

## Post-installation configuration

Here are some pointers to related documentation that you will need to get started:

- [Set up outgoing email notifications (SMTP, sendmail)](https://www.openproject.org/docs/installation-and-operations/configuration/outbound-emails)
- [Integrate an external authentication provider (LDAP/AD, SAML, OpenID)](https://www.openproject.org/docs/system-admin-guide/authentication)

- [First steps ![Getting started](https://www.openproject.org/assets/images/icons/getting-started-58195ed6.svg)
	Getting started
	Learn about the first steps  
	with OpenProject
	](https://www.openproject.org/docs/getting-started/)
- [All features ![User guide](https://www.openproject.org/assets/images/icons/user-guide-c95038e8.svg)
	User guide
	In-depth guide of all features  
	in OpenProject
	](https://www.openproject.org/docs/user-guide/)
- [![System admin guide](https://www.openproject.org/assets/images/icons/system-admin-guide-229fe831.svg)
	System admin guide
	Learn how to configure your  
	OpenProject
	](https://www.openproject.org/docs/system-admin-guide/)
- [![FAQ](https://www.openproject.org/assets/images/icons/faq-b2e0344e.svg)
	FAQ
	General frequently asked questions
	](https://www.openproject.org/docs/faq/)
- [![Enterprise edition guide](https://www.openproject.org/assets/images/icons/openproject-enterprise-edition-08123e1a.svg)
	Enterprise edition guide
	Manage your OpenProject  
	Enterprise edition
	](https://www.openproject.org/docs/enterprise-guide/)
- [![Installation and operations guide](https://www.openproject.org/assets/images/icons/installation-operations-02983909.svg)
	Installation and operations guide
	Find out how to install  
	and operate OpenProject
	](https://www.openproject.org/docs/installation-and-operations/)
- [![Release Notes](https://www.openproject.org/assets/images/icons/release-notes-7a231f12.svg)
	Release Notes
	See the latest changes  
	in OpenProject
	](https://www.openproject.org/docs/release-notes/)
- [![Development](https://www.openproject.org/assets/images/icons/development-guide-98a6498e.svg)
	Development
	Learn about how to contribute  
	to OpenProject
	](https://www.openproject.org/docs/development/)
- [![API](https://www.openproject.org/assets/images/icons/api-docs-ff3b8f3b.svg)
	API
	Learn about the API  
	powering OpenProject
	](https://www.openproject.org/docs/api/)
- [![BIM Guide](https://www.openproject.org/assets/images/icons/bim-guide-f60380b6.svg)
	BIM Guide
	Additional BIM features for construction teams
	](https://www.openproject.org/docs/bim-guide/)
- [![Use Cases](https://www.openproject.org/assets/images/icons/use-cases-51dbcec7.svg)
	Use Cases
	Detailed step-by-step instructions
	](https://www.openproject.org/docs/use-cases/)
- [![Glossary](https://www.openproject.org/assets/images/icons/glossary-93441a34.svg)
	Glossary
	A dictionary of project management terms
	](https://www.openproject.org/docs/glossary/)
- [![Security & privacy](https://www.openproject.org/assets/images/icons/systemsicherheit-3a0f1af3.svg)
	Security & privacy
	Data protection and information security are of central importance for the OpenProject team.
	](https://www.openproject.org/docs/security-and-privacy/)
- [![Contributions Guide](https://www.openproject.org/assets/images/icons/collaboration-beyond-5fd36e48.svg)
	Contributions Guide
	Learn how to contribute to OpenProject
	](https://www.openproject.org/docs/contributions-guide/)