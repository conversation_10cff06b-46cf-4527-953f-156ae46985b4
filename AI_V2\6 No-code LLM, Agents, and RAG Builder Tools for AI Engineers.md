---
title: 6 No-code <PERSON><PERSON>, Agents, and RAG Builder Tools for AI Engineers
source: https://blog.dailydoseofds.com/p/6-no-code-llm-agents-and-rag-builder
author:
  - "[[<PERSON><PERSON>]]"
published: 2025-07-05
created: 2025-07-05
description: ...with 200k stars combined!
tags:
  - AI/Tools
  - AI/FineTuning
  - AI/RAG
reference:
---
### ...with 200k stars combined!

In today's newsletter:

- Fireplexity: An open-source Perplexity clone
- 6 no-code LLM, Agents, and RAG builder tools.
- \[Video\] A technique to understand TP, TN, FP and FN

### Fireplexity: An open-source Perplexity clone

Fireplexity is a fully open-source Perplexity clone that provides AI-powered answers with cited sources.

![](https://substackcdn.com/image/fetch/$s_!gN0W!)

Key features:

- Powered by real-time web search.
- Provides responses backed by references
- Asks smart follow-ups.

**[GitHub repo →](https://github.com/mendableai/fireplexity)**

*Thanks to <PERSON><PERSON><PERSON><PERSON> for building this and partnering today!*

---

### 6 no-code LLM, Agents, and RAG builder tools

Continuing from open-source…

We scroll through and test several GitHub repos every week to find promising projects that would be helpful for AI engineers.

Here are 6 no-code LLM, Agents, and RAG builder tools we found:

![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/8442dccc-10b7-469c-905c-99da4a9a882e_1224x1008.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:1008,%22width%22:1224,%22resizeWidth%22:null,%22bytes%22:219263,%22alt%22:null,%22title%22:null,%22type%22:%22image/png%22,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:%22https://blog.dailydoseofds.com/i/167426564?img=https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F8442dccc-10b7-469c-905c-99da4a9a882e_1224x1008.png%22,%22isProcessing%22:false,%22align%22:null,%22offset%22:false})

#### RAGFlow

![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/ed6bcc10-a497-45aa-8e7a-45f05787180b_1224x1140.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:1140,%22width%22:1224,%22resizeWidth%22:403,%22bytes%22:159995,%22alt%22:null,%22title%22:null,%22type%22:%22image/png%22,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:%22https://blog.dailydoseofds.com/i/167426564?img=https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fed6bcc10-a497-45aa-8e7a-45f05787180b_1224x1140.png%22,%22isProcessing%22:false,%22align%22:null,%22offset%22:false})

RAGFlow is a RAG engine for deep document understanding!

It lets you build enterprise-grade RAG workflows on complex docs with well-founded citations.

Supports multimodal data understanding, web search, deep research, etc.

100% open-source with 59k+ stars!

**[GitHub repo →](https://github.com/infiniflow/ragflow)**

#### xpander

![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/74786f7b-49cf-4eeb-a251-010e570e4637_1216x1312.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:1312,%22width%22:1216,%22resizeWidth%22:396,%22bytes%22:164151,%22alt%22:null,%22title%22:null,%22type%22:%22image/png%22,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:%22https://blog.dailydoseofds.com/i/167426564?img=https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F74786f7b-49cf-4eeb-a251-010e570e4637_1216x1312.png%22,%22isProcessing%22:false,%22align%22:null,%22offset%22:false})

xpander is a framework-agnostic backend for agents that manages memory, tools, multi-user states, events, guardrails, etc.

While it is not a core no-code tool, you can build, test, and deploy Agents by primarily using the UI.

Compatible with LlamaIndex, CrewAI, etc.

**[GitHub repo →](https://github.com/xpander-ai/xpander.ai)**

#### Transformer Lab

![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/d0b0e6d7-c84f-4888-841f-e465cf3a3281_1216x1340.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:1340,%22width%22:1216,%22resizeWidth%22:351,%22bytes%22:226884,%22alt%22:null,%22title%22:null,%22type%22:%22image/png%22,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:%22https://blog.dailydoseofds.com/i/167426564?img=https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fd0b0e6d7-c84f-4888-841f-e465cf3a3281_1216x1340.png%22,%22isProcessing%22:false,%22align%22:null,%22offset%22:false})

Transformer Lab is an app to experiment with LLMs:

- Train, fine-tune, or chat.
- One-click LLM download (DeepSeek, Gemma, etc.)
- Drag-n-drop UI for RAG.
- Built-in logging, and more.

A 100% open-source and local!

**[GitHub repo →](https://github.com/transformerlab/transformerlab-app)**

#### Llama Factory

![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/14bf36e5-c355-4674-b64d-7140c0dd6fd6_1216x1220.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:1220,%22width%22:1216,%22resizeWidth%22:437,%22bytes%22:118194,%22alt%22:null,%22title%22:null,%22type%22:%22image/png%22,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:%22https://blog.dailydoseofds.com/i/167426564?img=https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F14bf36e5-c355-4674-b64d-7140c0dd6fd6_1216x1220.png%22,%22isProcessing%22:false,%22align%22:null,%22offset%22:false})

LLaMA-Factory lets you train and fine-tune open-source LLMs and VLMs without writing any code.

Supports 100+ models, multimodal fine-tuning, PPO, DPO, experiment tracking, and much more!

100% open-source with 50k stars!

**[GitHub repo →](https://github.com/hiyouga/LLaMA-Factory)**

#### Langflow

![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/3e85216b-6981-4a2f-ae60-2c769a4dbff5_1224x1272.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:1272,%22width%22:1224,%22resizeWidth%22:423,%22bytes%22:173798,%22alt%22:null,%22title%22:null,%22type%22:%22image/png%22,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:%22https://blog.dailydoseofds.com/i/167426564?img=https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F3e85216b-6981-4a2f-ae60-2c769a4dbff5_1224x1272.png%22,%22isProcessing%22:false,%22align%22:null,%22offset%22:false})

Langflow is a drag-and-drop visual tool to build AI agents.

It lets you build and deploy AI-powered agents and workflows. Supports all major LLMs, vector DBs, etc.

100% open-source with 82k+ stars!

**[GitHub repo →](https://github.com/langflow-ai/langflow)**

#### AutoAgent

![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/19cf3b80-f8c0-4d17-9649-9d19f058a8be_1224x1232.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:1232,%22width%22:1224,%22resizeWidth%22:435,%22bytes%22:113763,%22alt%22:null,%22title%22:null,%22type%22:%22image/png%22,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:%22https://blog.dailydoseofds.com/i/167426564?img=https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F19cf3b80-f8c0-4d17-9649-9d19f058a8be_1224x1232.png%22,%22isProcessing%22:false,%22align%22:null,%22offset%22:false})

AutoAgent is a zero-code framework that lets you build and deploy Agents using natural language.

It comes with:

- Universal LLM support
- Native self-managing Vector DB
- Function-calling and ReAct interaction modes.

100% open-source with 5k stars!

**[GitHub repo →](https://github.com/HKUDS/AutoAgent)**

👉 Over to you: What no-code AI builder tools will you add here?

---

### \[Video\] A technique to understand TP, TN, FP and FN

Do you struggle to classify predictions as one of TP, TN, FP, and FN?

![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/98ddef61-b965-4616-afa3-e29f2e2bd980_1820x708.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:566,%22width%22:1456,%22resizeWidth%22:519,%22bytes%22:null,%22alt%22:%22%22,%22title%22:null,%22type%22:null,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:null,%22isProcessing%22:false,%22align%22:null,%22offset%22:false%7D)

If yes, here’s a video that explains a simple technique:

<video src="blob:https://blog.dailydoseofds.com/ca331180-450d-44df-8192-2db79d842679"></video>

Essentially, when labeling any binary classification prediction, ask two questions:

![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/e8ab0847-e269-4feb-b586-f18d0935424b_1940x1540.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:1156,%22width%22:1456,%22resizeWidth%22:534,%22bytes%22:null,%22alt%22:%22%22,%22title%22:null,%22type%22:null,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:null,%22isProcessing%22:false,%22align%22:null,%22offset%22:false%7D)

**Question 1) Did the model get it right?**

- The answer will be either Yes or No.
	- Yes means True.
	- No means False.

**Question 2) What was the predicted class?**

- The answer will be either Positive or Negative.

Next, just combine the above two answers to get the final label.

For instance, say the actual and predicted class were positive.

![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/b4843b4c-0b10-4af0-bbff-2c3264cac239_1940x804.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:603,%22width%22:1456,%22resizeWidth%22:494,%22bytes%22:null,%22alt%22:%22%22,%22title%22:null,%22type%22:null,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:null,%22isProcessing%22:false,%22align%22:null,%22offset%22:false%7D)

Question 1) Did the model get it right?

- Answer: Yes, which means **TRUE**.

Question 2) What was the **predicted** **class**?

- Answer: **POSITIVE**.

The final label: **TRUE POSITIVE**.

👉 Over to you: Do you know any other techniques to label binary classification predictions?

Thanks for reading!

---

### P.S. For those wanting to develop “Industry ML” expertise:

![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/939bede7-b0de-4770-a3e9-34d39488e776_2733x1020.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:543,%22width%22:1456,%22resizeWidth%22:null,%22bytes%22:null,%22alt%22:%22%22,%22title%22:%22%22,%22type%22:null,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:null,%22isProcessing%22:false,%22align%22:null,%22offset%22:false%7D)

At the end of the day, all businesses care about *impact*. That’s it!

- Can you reduce costs?
- Drive revenue?
- Can you scale ML models?
- Predict trends before they happen?

We have discussed several other topics (with implementations) that align with such topics.

Here are some of them:

- Learn how to build Agentic systems in **[a crash course with 14 parts](https://www.dailydoseofds.com/ai-agents-crash-course-part-1-with-implementation/)**.
- Learn how to build real-world RAG apps and evaluate and scale them in **[this crash course](https://www.dailydoseofds.com/a-crash-course-on-building-rag-systems-part-1-with-implementations/)**.

![](https://blog.dailydoseofds.com/p/%7B%22src%22:%22https://substack-post-media.s3.amazonaws.com/public/images/ca6f5be8-3aa0-4754-8087-1929397898e6_697x920.png%22,%22srcNoWatermark%22:null,%22fullscreen%22:null,%22imageSize%22:null,%22height%22:920,%22width%22:697,%22resizeWidth%22:405,%22bytes%22:null,%22alt%22:%22%22,%22title%22:%22%22,%22type%22:null,%22href%22:null,%22belowTheFold%22:true,%22topImage%22:false,%22internalRedirect%22:null,%22isProcessing%22:false,%22align%22:null,%22offset%22:false%7D)

- Learn sophisticated graph architectures and how to train them on graph data.
- So many real-world NLP systems rely on pairwise context scoring. Learn scalable approaches **[here](https://www.dailydoseofds.com/bi-encoders-and-cross-encoders-for-sentence-pair-similarity-scoring-part-1/)**.
- Learn how to run large models on small devices using **[Quantization techniques](https://www.dailydoseofds.com/quantization-optimize-ml-models-to-run-them-on-tiny-hardware/)**.
- Learn how to generate prediction intervals or sets with strong statistical guarantees for increasing trust using **[Conformal Predictions](https://www.dailydoseofds.com/conformal-predictions-build-confidence-in-your-ml-models-predictions/)**.
- Learn how to identify causal relationships and answer business questions using causal inference in **[this crash course](https://www.dailydoseofds.com/a-crash-course-on-causality-part-1/)**.
- Learn how to scale and implement ML model training in this **[practical guide](https://www.dailydoseofds.com/how-to-scale-model-training/)**.
- Learn techniques to reliably **[test new models in production](https://www.dailydoseofds.com/5-must-know-ways-to-test-ml-models-in-production-implementation-included/)**.
- Learn how to build privacy-first ML systems using **[Federated Learning](https://www.dailydoseofds.com/federated-learning-a-critical-step-towards-privacy-preserving-machine-learning/)**.
- Learn 6 techniques with implementation to **[compress ML models](https://www.dailydoseofds.com/model-compression-a-critical-step-towards-efficient-machine-learning/)**.

All these resources will help you cultivate key skills that businesses and companies care about the most.