---
title: "jina-ai/submodular-optimization: Submodular optimization for context engineering: query fan-out, text selection, passage reranking"
source: https://github.com/jina-ai/submodular-optimization/
author:
  - "[[hanxiao]]"
published:
created: 2025-07-15
description: "Submodular optimization for context engineering: query fan-out, text selection, passage reranking - jina-ai/submodular-optimization"
tags:
  - AI/Jina
reference:
---
**[submodular-optimization](https://github.com/jina-ai/submodular-optimization)** Public

Submodular optimization for context engineering: query fan-out, text selection, passage reranking

[jina.ai/news/submodular-optimization-for-diverse-query-generation-in-deepresearch](https://jina.ai/news/submodular-optimization-for-diverse-query-generation-in-deepresearch "https://jina.ai/news/submodular-optimization-for-diverse-query-generation-in-deepresearch")

[Apache-2.0 license](https://github.com/jina-ai/submodular-optimization/blob/main/LICENSE)

[Open in github.dev](https://github.dev/) [Open in a new github.dev tab](https://github.dev/) [Open in codespace](https://github.com/codespaces/new/jina-ai/submodular-optimization?resume=1)

<table><thead><tr><th colspan="2"><span>Name</span></th><th colspan="1"><span>Name</span></th><th><p><span>Last commit message</span></p></th><th colspan="1"><p><span>Last commit date</span></p></th></tr></thead><tbody><tr><td colspan="3"><p><span><a href="https://github.com/jina-ai/submodular-optimization/commit/03b2b67cceba132e177a46cd2b9463025b27d72f">feat: add video frame selection</a></span></p><p><span><a href="https://github.com/jina-ai/submodular-optimization/commit/03b2b67cceba132e177a46cd2b9463025b27d72f">03b2b67</a> ·</span></p><p><a href="https://github.com/jina-ai/submodular-optimization/commits/main/"><span><span><span>5 Commits</span></span></span></a></p></td></tr><tr><td colspan="2"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/.gitignore">.gitignore</a></p></td><td colspan="1"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/.gitignore">.gitignore</a></p></td><td><p><a href="https://github.com/jina-ai/submodular-optimization/commit/817daf0b349564c9166c384e746f9e3f900139e0">feat: blog 2</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/LICENSE">LICENSE</a></p></td><td colspan="1"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/LICENSE">LICENSE</a></p></td><td><p><a href="https://github.com/jina-ai/submodular-optimization/commit/c1687a29ff716c87c66345e8fdac6371ce454147">Initial commit</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/README.md">README.md</a></p></td><td colspan="1"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/README.md">README.md</a></p></td><td><p><a href="https://github.com/jina-ai/submodular-optimization/commit/03b2b67cceba132e177a46cd2b9463025b27d72f">feat: add video frame selection</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/cosine_similarity_analysis.py">cosine_similarity_analysis.py</a></p></td><td colspan="1"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/cosine_similarity_analysis.py">cosine_similarity_analysis.py</a></p></td><td><p><a href="https://github.com/jina-ai/submodular-optimization/commit/9ab2d6cd646d570ec88adf8b752fa428bc0177b1">chore: first commit</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/embed.js">embed.js</a></p></td><td colspan="1"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/embed.js">embed.js</a></p></td><td><p><a href="https://github.com/jina-ai/submodular-optimization/commit/9ab2d6cd646d570ec88adf8b752fa428bc0177b1">chore: first commit</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/example_input.json">example_input.json</a></p></td><td colspan="1"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/example_input.json">example_input.json</a></p></td><td><p><a href="https://github.com/jina-ai/submodular-optimization/commit/e08abdb8bf7c817b640f747e6e74e0f5ae227fb5">chore: first commit</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/index.js">index.js</a></p></td><td colspan="1"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/index.js">index.js</a></p></td><td><p><a href="https://github.com/jina-ai/submodular-optimization/commit/9ab2d6cd646d570ec88adf8b752fa428bc0177b1">chore: first commit</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/output-prompt-v1.txt.json">output-prompt-v1.txt.json</a></p></td><td colspan="1"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/output-prompt-v1.txt.json">output-prompt-v1.txt.json</a></p></td><td><p><a href="https://github.com/jina-ai/submodular-optimization/commit/817daf0b349564c9166c384e746f9e3f900139e0">feat: blog 2</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/output-prompt-v2.txt.json">output-prompt-v2.txt.json</a></p></td><td colspan="1"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/output-prompt-v2.txt.json">output-prompt-v2.txt.json</a></p></td><td><p><a href="https://github.com/jina-ai/submodular-optimization/commit/9ab2d6cd646d570ec88adf8b752fa428bc0177b1">chore: first commit</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/output-prompt-v3.txt.json">output-prompt-v3.txt.json</a></p></td><td colspan="1"><p><a href="https://github.com/jina-ai/submodular-optimization/blob/main/output-prompt-v3.txt.json">output-prompt-v3.txt.json</a></p></td><td><p><a href="https://github.com/jina-ai/submodular-optimization/commit/9ab2d6cd646d570ec88adf8b752fa428bc0177b1">chore: first commit</a></p></td><td></td></tr><tr><td colspan="3"></td></tr></tbody></table>

## Submodular Optimization

Using submodular optimization for context engineering. Examples of text selection, query fan-out, passage reranking are included in this repo.

## Blogs

Highly recommended before diving into the code:

- [Submodular Optimization for Text Selection, Passage Reranking & Context Engineering](https://jina.ai/news/submodular-optimization-for-text-selection-passage-reranking-context-engineering). Examples of text selection and passage reranking w/ submodular optimization.
	- [Colab Notebook: Text Selection](https://github.com/jina-ai/submodular-optimization/blob/main/text-selection.ipynb)
	- [Colab Notebook: Passage Reranking](https://github.com/jina-ai/submodular-optimization/blob/main/passage-reranking.ipynb)
	- [Colab Notebook: Video Frames Selection](https://github.com/jina-ai/submodular-optimization/blob/main/video-frame-rerank.ipynb)
- [Submodular Optimization for Diverse Query Generation in DeepResearch](https://jina.ai/news/submodular-optimization-for-diverse-query-generation-in-deepresearch). Example of query fan-out w/ submodular optimization.

## Setup

```
npm install
export GOOGLE_GENERATIVE_AI_API_KEY=your_api_key_here
export JINA_API_KEY=your_jina_api_key_here
```

## Usage

### Generate Queries

```
npm run generate <prompt-file> "your query" [num_queries_or_range]
npm run embed <output-file>
```
```
node submodular_optimization.js <k>                    # Single k value
node submodular_optimization.js <start>-<end>         # Range of k values
```

## Examples

### Query Generation

```
npm run generate prompt-v1.txt "machine learning" 5
npm run generate prompt-v2.txt "climate change" 2-10
npm run embed output-prompt-v1.txt.json
```

### Submodular Optimization

```
node submodular_optimization.js 5                     # Select 5 optimal queries
node submodular_optimization.js 1-20                  # Select 1-20 queries iteratively
```

## Output Files

### Query Generation

- `output-<prompt-file>.json` - Generated query strings
- `output-<prompt-file>.embeddings.json` - Query embeddings

### Submodular Optimization

- `output-prompt-v1.txt.submodular.embeddings.json` - Optimized query embeddings

## Algorithm

The submodular optimization uses a **lazy greedy algorithm** that:

1. **Maximizes diversity** by selecting queries that cover different aspects of the topic
2. **Maintains relevance** by considering similarity to the original query
3. **Uses cosine similarity** for measuring query relationships
4. **Implements lazy evaluation** for computational efficiency

The objective function balances:

- **Relevance**: How well queries match the original topic (weighted by α=0.3)
- **Coverage**: How well selected queries cover the candidate set
- **Diversity**: How different the selected queries are from each other

## File Structure

```
submodular-optimization/
├── submodular_optimization.js    # Main optimization algorithm
├── prompt-v*.txt                 # Query generation prompts
├── output-prompt-v*.json         # Generated query strings
├── output-prompt-v*.embeddings.json          # Original embeddings
└── output-prompt-v*.submodular.embeddings.json  # Optimized embeddings
```

## Releases

No releases published

## Packages

No packages published  

## Languages

- [Jupyter Notebook 99.7%](https://github.com/jina-ai/submodular-optimization/search?l=jupyter-notebook)
- Other 0.3%