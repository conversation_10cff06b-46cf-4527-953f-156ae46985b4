---
created: 2024-10-20
tags:
  - AI/AWS
source: 
author: 
Reference:
  - https://claude.ai/chat/bfcaaad7-88b1-431b-8a64-7d6c12c7d952
  - https://claude.ai/chat/b41c854b-77ab-4a59-af13-7b5bb03acb63
---

# Stop/Terminate EC2 instance
```
aws ec2 stop-instances --instance-ids i-065aa0e31941c782d --force

aws ec2 terminate-instances --instance-ids i-0b5dee02ff62f297a

# 查看实例详细状态
aws ec2 terminate-instances --instance-ids i-0b5dee02ff62f297a

# 获取系统日志
aws ec2 get-console-output --instance-id i-0b5dee02ff62f297a

```

---
# recover old volumn if terminicate by mistakes
```note
First, verify your EBS volume:

Go to EC2 Dashboard → Volumes
Find your original volume (it should be in "available" state)
Note down the volume ID and availability zone


Launch a new EC2 instance:

Make sure to launch it in the same availability zone as your existing volume
Use similar instance type and configurations as before
For storage, just use a basic root volume for now


After the new instance is running:

Go to the Volumes section
Select your original volume
Click "Actions" → "Attach Volume"
Select your new instance from the dropdown
Specify the device name (e.g., /dev/sdf)

First, stop your newly created instance:

Select the instance
Click "Instance state" → "Stop instance"
Wait until it's fully stopped


Detach the current root volume of your new instance:

Go to the Volumes section
Find the current root volume of your new instance
Select it and click "Actions" → "Detach Volume"
You can delete this temporary root volume if you won't need it


Modify your original volume:

Select your original volume (currently attached as /dev/sdf)
Click "Actions" → "Detach Volume" (if still attached)
Once detached, click "Actions" → "Attach Volume"
Select your stopped instance
IMPORTANT: Change the device name to "/dev/xvda" (for Amazon Linux/Linux) or "/dev/sda1" (for Windows) - this makes it the root volume
Click "Attach"


Start your instance:

Select the instance
Click "Instance state" → "Start instance"



```
---

# mount nfs disk
```notepad
sudo apt-get install nfs-common

sudo mount -t nfs4 -o nfsvers=4.1,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport fs-0c0c2bcd9a0b1d50d.efs.ap-northeast-1.amazonaws.com:/ /nfs
```


---

# setup windows client login with key file
```

# 在EC2服务器上操作
sudo mkdir -p /home/<USER>/.ssh
sudo ssh-keygen -t rsa -f /home/<USER>/.ssh/id_rsa  # 生成密钥对
sudo cp /home/<USER>/.ssh/id_rsa.pub /home/<USER>/.ssh/authorized_keys
sudo chown -R mlusr:mlgrp /home/<USER>/.ssh
sudo chmod 700 /home/<USER>/.ssh
sudo chmod 600 /home/<USER>/.ssh/authorized_keys

# 编辑SSH配置文件
sudo vim /etc/ssh/sshd_config

# 确保以下设置正确
PasswordAuthentication yes  # 临时允许密码认证
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys
PermitRootLogin no

# 重启SSH服务
sudo systemctl restart sshd


# 在MobaXterm中登录后，复制服务器上的私钥内容
sudo cat /home/<USER>/.ssh/id_rsa

# 在Windows本地：
# 1. 创建一个文本文件，粘贴私钥内容
# 2. 保存为 mlusr.pem
# 3. 在MobaXterm中配置这个私钥文件



client create private/pub key with ssh-keygen 
add pub into mgt server authorized_keys

```
 ## some command for operation
```
 <client>
 sudo groupadd opgrp
 sudo useradd -m -g opgrp -s /bin/bash erlin
 sudo su - erlin
 ssh-keygen
 
 <server>
 mkdir .ssh
 chmod 700 .ssh
 cd .ssh
 touch authorized_keys
 chmod 600 authorized_keys
 add pub key to authorized_keys
 
 
 sudo userdel russel
 sudo rm -rf /home/<USER>/
 
 sudo useradd -m -g opgrp -s /bin/bash russell
 sudo su - russell
 mkdir .ssh
 chmod 700 .ssh
 cd .ssh
 touch authorized_keys
 chmod 600 authorized_keys

# config
 Host jumpserver2
     HostName ************
     User ray
     IdentityFile ~/.ssh/id_rsa
     ForwardAgent yes
 
 Host ml01r
     HostName ********
     User ray
     ProxyJump jumpserver2
     IdentityFile ~/.ssh/id_rsa
 
```



---

# Create multi-attached volume

==**<span style="background:#ff4d4f"><span style="background:rgba(240, 107, 5, 0.2)"><font color="#c3d69b">Be careful,  Not All Instance Type Support multi-attached volume</font></span></span>==**
```
在AWS控制台通过GUI创建支持Multi-Attach的EBS卷，需要按照以下步骤：

首先，必须选择 io2 类型的 Provisioned IOPS SSD（我看到您已经选择了io1）。
Multi-Attach 选项不是在创建卷时设置的，而是在卷创建后需要单独启用：

创建卷后，在 EBS 卷列表中选择该卷
点击 "Actions"（操作）按钮
选择 "Modify Volume"（修改卷）
在修改界面中，会看到 "Enable Multi-Attach" 的选项
勾选该选项并保存


首先需要将卷类型改为io2（当前显示的是io1）
创建新卷时，会显示"Enable Multi-Attach"的选项
对于已经创建的卷：

如果是io1类型，需要先升级到io2
如果要启用Multi-Attach，可能需要创建新的io2卷并迁移数据



建议的解决方案：

创建新的io2卷，在创建时启用Multi-Attach
或者使用AWS CLI命令来启用：

bashCopyaws ec2 modify-volume --volume-id vol-0ac318cc7ae049a25 --volume-type io2 --multi-attach-enabled
如果您确实需要Multi-Attach功能，建议：

使用io2类型的卷重新创建
或者考虑使用其他替代方案，如Amazon EFS（如果适用于您的使用场景）

需要我详细说明如何使用io2卷设置Multi-Attach，或者探讨其他可能的解决方案吗？

```
---

# Create new app user



```
sudo groupadd mlgrp
sudo useradd -m -g mlgrp -s /bin/bash mlusr
sudo usermod -aG sudo mlusr

# 创建新的规则文件
sudo visudo -f /etc/sudoers.d/91-mlgrp-users

# 在文件中添加以下内容（注意组名前要加%）
%mlgrp ALL=(ALL) NOPASSWD:ALL


sudo groupadd apgrp
sudo useradd -m -g apgrp -s /bin/bash apusr
sudo usermod -aG sudo apusr
sudo visudo -f /etc/sudoers.d/91-apgrp-users
sudo su - apusr

```

## Setup only sudo to dedicated user
```
sudo visudo -f /etc/sudoers.d/opgrp_to_mlusr
ubuntu@SBIHD-GenAI-dev-LVM-ML01:~$ sudo cat /etc/sudoers.d/opgrp_to_mlusr
%opgrp ALL=(mlusr) NOPASSWD: ALL
sudo -u mlusr -i

sudo visudo -f /etc/sudoers.d/opgrp_to_apusr
ubuntu@SBIHD-GenAI-dev-LVM-ML01:~$ sudo cat /etc/sudoers.d/opgrp_to_apusr
%opgrp ALL=(apusr) NOPASSWD: ALL
sudo -u apusr -i

# 只允许执行某些命令
%opgrp ALL=(mlusr) /usr/bin/python3, /usr/local/bin/jupyter
sudo tail -f /var/log/auth.log

```
[[#setup windows client login with key file]]

---
# Change/Assign Public IP
```
Plan 1:
使用弹性 IP (Elastic IP):
这是推荐的方法，因为弹性 IP 是静态的公网 IP，即使实例重启也不会改变。

步骤如下：

在 EC2 控制台左侧导航栏中点击"弹性 IP"
点击"分配新地址"
点击"分配"
选择新分配的弹性 IP
点击"操作" -> "关联弹性 IP 地址"
在实例选择框中选择 SBIHD-GenAI-dev-LVM-AP02 (i-0ce91e0787f15c189)
点击"关联"
修改实例设置:
如果你不需要固定的公网 IP，可以直接修改实例的网络设置：
停止实例（注意：这会导致实例暂时不可用）
选择实例
点击"操作" -> "联网" -> "管理 IP 地址"
勾选"自动分配公有 IP"选项
重启实例

考虑到这是开发环境的实例，我建议使用第一种方法（弹性 IP），因为：

提供固定的公网 IP 地址
不需要停止实例
更容易管理和维护
可以在需要时轻松地将 IP 重新映射到其他实例

Plan 2:

这种方法通过修改实例的网络接口设置来启用自动分配公网 IP:

选择实例 SBIHD-GenAI-dev-LVM-AP02
首先需要停止实例:

选择 "实例状态" -> "停止实例"
确认停止（注意:这会导致实例暂时不可访问）
等待实例完全停止


修改网络设置:

点击 "操作" -> "网络和安全性" -> "更改源/目标检查"
或者在网络接口设置中找到对应的网络接口
启用 "自动分配公有 IPv4 地址" 选项
保存更改


重新启动实例:

选择 "实例状态" -> "启动实例"
等待实例完全启动
AWS 将自动分配一个公网 IP



这种方法的优缺点:
优点:

设置简单
不需要额外的 AWS 资源

缺点:

需要停止实例,会造成服务中断
每次实例重启可能会获得不同的公网 IP
不适合需要固定 IP 的应用场景

```

---

# Change HostName

```
#!/bin/bash
hostnamectl set-hostname your-custom-hostname
# 对于Amazon Linux 2或RHEL系统
echo "preserve_hostname: true" >> /etc/cloud/cloud.cfg
# 重启系统服务（可选）
sudo systemctl restart systemd-hostnamed

```

# Change Timezone
sudo timedatectl set-timezone Asia/Tokyo

## update ntp server to aws one
```
ubuntu@SBIHD-GenAI-prd-LVM-PMT:~$ cat /etc/systemd/timesyncd.conf 
#  This file is part of systemd.
#
#  systemd is free software; you can redistribute it and/or modify it under the
#  terms of the GNU Lesser General Public License as published by the Free
#  Software Foundation; either version 2.1 of the License, or (at your option)
#  any later version.
#
# Entries in this file show the compile time defaults. Local configuration
# should be created by either modifying this file, or by creating "drop-ins" in
# the timesyncd.conf.d/ subdirectory. The latter is generally recommended.
# Defaults can be restored by simply deleting this file and all drop-ins.
#
# See timesyncd.conf(5) for details.

[Time]
NTP=***************

sudo systemctl restart systemd-timesyncd

ubuntu@SBIHD-GenAI-prd-LVM-PMT:~$ timedatectl
               Local time: 木 2025-07-17 15:38:07 JST
           Universal time: 木 2025-07-17 06:38:07 UTC
                 RTC time: 木 2025-07-17 06:38:06
                Time zone: Asia/Tokyo (JST, +0900)
System clock synchronized: yes
              NTP service: active
          RTC in local TZ: no
          
```
---
# Enable Agent Forward

```
只在信任的服务器上启用 Agent Forwarding
确保服务器A的 /etc/ssh/sshd_config 允许 Agent Forwarding：

AllowAgentForwarding yes

可以通过 echo "$SSH_AUTH_SOCK" 验证 Agent Forwarding 是否正常工作

# 1. 在本地添加密钥
eval $(ssh-agent)
ssh-add ~/.ssh/Key4ML.pem

# 2. 验证密钥已添加
ssh-add -l

# 3. 使用 -A 登录到第一台服务器
ssh -A -i Key4ML.pem ubuntu@************

# 4. 在第一台服务器上，尝试详细模式连接第二台服务器
ssh -v ubuntu@*********

```


## real case

```
cd .ssh
eval $(ssh-agent)
ssh-add id_rsa
ssh-add -l
ssh -A -i id_rsa ray@************
ssh ********
```

  ## another step after enable agentforwarding in Jump server
```
(base) ray@jethome:~/.ssh$ cat config
  Host jumpserver
      HostName ************
      User ubuntu
      IdentityFile ~/.ssh/Key4ML.pem
      ForwardAgent yes
  
  Host ap01
      HostName *********
      User ubuntu
      ProxyJump jumpserver
      IdentityFile ~/.ssh/Key4ML.pem
  
  Host ap02
      HostName *********
      User ubuntu
      ProxyJump jumpserver
      IdentityFile ~/.ssh/Key4ML.pem
  
  Host ml01
      HostName ********
      User ubuntu
      ProxyJump jumpserver
      IdentityFile ~/.ssh/Key4ML.pem
  
  Host ml01
      HostName ********
      User ubuntu
      ProxyJump jumpserver
      IdentityFile ~/.ssh/Key4ML.pem
  
    
```
---
## Access from Cursor

Step 1
```
copy private pem key to C:\Users\<USER>\.ssh
```
![[Pasted image 20241106220526.png]]

Step2: modify config in windows .ssh folder

```
Host jumpserver
    HostName ************
    User ubuntu
    IdentityFile ~/.ssh/Key4ML.pem
    ForwardAgent yes

Host ap01
    HostName *********
    User ubuntu
    ProxyJump jumpserver
    IdentityFile ~/.ssh/Key4ML.pem

Host ap02
    HostName *********
    User ubuntu
    ProxyJump jumpserver
    IdentityFile ~/.ssh/Key4ML.pem

Host ml01
    HostName ********
    User ubuntu
    ProxyJump jumpserver
    IdentityFile ~/.ssh/Key4ML.pem

Host ml02
    HostName ********
    User ubuntu
    ProxyJump jumpserver
    IdentityFile ~/.ssh/Key4ML.pem

Host opt
    HostName **********
    User ubuntu
    ProxyJump jumpserver
    IdentityFile ~/.ssh/Key4ML.pem

Host jumpserver2
    HostName ************
    User ray
    IdentityFile ~/.ssh/id_rsa_jethome
    ForwardAgent yes

Host ml01r
    HostName ********
    User ray
    ProxyJump jumpserver2
    IdentityFile ~/.ssh/id_rsa_jethome

Host ml02r
    HostName ********
    User ray
    ProxyJump jumpserver2
    IdentityFile ~/.ssh/id_rsa_jethome

Host ap01r
    HostName *********
    User ray
    ProxyJump jumpserver2
    IdentityFile ~/.ssh/id_rsa_jethome

Host ap02r
    HostName *********
    User ray
    ProxyJump jumpserver2
    IdentityFile ~/.ssh/id_rsa_jethome

Host optr
    HostName **********
    User ray
    ProxyJump jumpserver2
    IdentityFile ~/.ssh/id_rsa_jethome

```
Step 3: ctrl+, in cursor and 'open as json'
add below . we must use Windows openssh but not git ssh
```
    "remote.SSH.configFile": "C:\\Users\\<USER>\\.ssh\\config",
    "remote.SSH.useLocalServer": true,
    "remote.SSH.showLoginTerminal": true,
    "remote.SSH.enableDynamicForwarding": true,
    "remote.SSH.enableRemoteCommand": true,
    "remote.SSH.LocalServerDownload": true,    
    "remote.SSH.path": "C:\\Windows\\System32\\OpenSSH\\ssh.exe",
```
Step4: ctrl+shift+p  open remote ssh 

---
## Access from MobaXterm

windows -> mgt -> ml01

Basic setting
![[Pasted image 20241106222524.png]]

Advanced setting
![[Pasted image 20241106222543.png]]

Add jump host
![[Pasted image 20241106222612.png]]

---
# Change Timezone to Tokyo

```
sudo apt-get install systemd-timesyncd
sudo timedatectl set-timezone Asia/Tokyo
timedatectl

```
---
# 创建NAT Gateway

```
创建NAT Gateway：
在AWS控制台：
1. 进入VPC服务
2. 左侧导航栏选择 "NAT Gateways"
3. 点击 "Create NAT Gateway"
4. 填写配置：
   - Name: my-system-nat
   - Subnet: 选择一个public subnet
   - Connectivity type: Public
   - Elastic IP allocation: 
     点击 "Allocate Elastic IP"

注意：NAT Gateway必须放在public subnet中

创建或修改路由表(给private subnet用)：
1. 进入 "Route Tables"
2. 点击 "Create route table"
3. 填写信息：
   - Name: my-system-private-rt
   - VPC: 选择您的VPC

4. 添加路由：
   - 选择新建的路由表
   - 点击 "Edit routes"
   - 添加路由：
     * Destination: 0.0.0.0/0
     * Target: 选择刚创建的NAT Gateway
   
5. 关联子网：
   - 选择 "Subnet Associations"标签
   - 点击 "Edit subnet associations"
   - 选择需要访问外网的private subnets

检查Security Group设置：
确保EC2实例的Security Group允许出站流量：
- Type: All traffic
- Destination: 0.0.0.0/0




```
- NAT Gateway是按小时收费的
- 会产生数据传输费用
- 建议设置CloudWatch告警监控费用
- 考虑在不同可用区部署多个NAT Gateway实现高可用

进入EC2控制台，查看那些连不上的实例：

Copy- 确认是否有Public IPv4地址
- 如果显示为"-"，则需要：
  a. 修改子网的"Auto-assign public IPv4 address"设置
  或
  b. 手动分配Elastic IP

如果不想分配公网IP，则需要：

Copy- 创建NAT Gateway
- 修改路由表，添加：
  * Destination: 0.0.0.0/0
  * Target: NAT Gateway


<span style="background:#fdbfff">处于Public子网的server都应该enable autoassign public ip这样才能访问外网，不然就要把这些机器移到private子网</span>
<span style="background:#fdbfff">然后用NAT，但NAT要钱</span>

---
# Install some package
```
sudo apt install net-tools

```

# Create LVM disk
```
ubuntu@SBIHD-GenAI-dev-LVM-AP01:~$ sudo lsblk
NAME         MAJ:MIN RM   SIZE RO TYPE MOUNTPOINTS
loop0          7:0    0  25.2M  1 loop /snap/amazon-ssm-agent/7993
loop1          7:1    0  55.7M  1 loop /snap/core18/2829
loop2          7:2    0  55.4M  1 loop /snap/core18/2846
loop3          7:3    0    64M  1 loop /snap/core20/2379
loop4          7:4    0  63.7M  1 loop /snap/core20/2434
loop5          7:5    0    87M  1 loop /snap/lxd/29351
loop6          7:6    0  38.8M  1 loop /snap/snapd/21759
nvme1n1      259:0    0   500G  0 disk
nvme0n1      259:1    0    50G  0 disk
├─nvme0n1p1  259:3    0  49.9G  0 part /
├─nvme0n1p14 259:4    0     4M  0 part
└─nvme0n1p15 259:5    0   106M  0 part /boot/efi
nvme2n1      259:2    0 372.5G  0 disk
ubuntu@SBIHD-GenAI-dev-LVM-AP01:~$ sudo pvcreate /dev/nvme1n1
  Physical volume "/dev/nvme1n1" successfully created.
ubuntu@SBIHD-GenAI-dev-LVM-AP01:~$ sudo vgcreate datavg /dev/nvme1n1
  Volume group "datavg" successfully created
ubuntu@SBIHD-GenAI-dev-LVM-AP01:~$ sudo pvs
  PV           VG     Fmt  Attr PSize    PFree
  /dev/nvme1n1 datavg lvm2 a--  <500.00g <500.00g
ubuntu@SBIHD-GenAI-dev-LVM-AP01:~$ sudo lvcreate -L 200G -n datalv datavg
  Logical volume "datalv" created.
ubuntu@SBIHD-GenAI-dev-LVM-AP01:~$ sudo lvs
  LV     VG     Attr       LSize   Pool Origin Data%  Meta%  Move Log Cpy%Sync Convert
  datalv datavg -wi-a----- 200.00g
ubuntu@SBIHD-GenAI-dev-LVM-AP01:~$ sudo mkfs.ext4 /dev/datavg/datalv
mke2fs 1.46.5 (30-Dec-2021)
Creating filesystem with ******** 4k blocks and ******** inodes
Filesystem UUID: 122a59d0-fb4f-4e9b-a372-55937db45d10
Superblock backups stored on blocks:
        32768, 98304, 163840, 229376, 294912, 819200, 884736, 1605632, 2654208,
        4096000, 7962624, ********, ********, ********

Allocating group tables: done
Writing inode tables: done
Creating journal (262144 blocks): done
Writing superblocks and filesystem accounting information: done

ubuntu@SBIHD-GenAI-dev-LVM-AP01:~$ sudo ^C
ubuntu@SBIHD-GenAI-dev-LVM-AP01:~$ ^C
ubuntu@SBIHD-GenAI-dev-LVM-AP01:~$ sudo mkdir /databank
ubuntu@SBIHD-GenAI-dev-LVM-AP01:~$ sudo pvs
  PV           VG     Fmt  Attr PSize    PFree
  /dev/nvme1n1 datavg lvm2 a--  <500.00g <300.00g
ubuntu@SBIHD-GenAI-dev-LVM-AP01:~$ df -h
Filesystem       Size  Used Avail Use% Mounted on
/dev/root         49G  2.3G   46G   5% /
tmpfs             16G     0   16G   0% /dev/shm
tmpfs            6.2G  928K  6.2G   1% /run
tmpfs            5.0M     0  5.0M   0% /run/lock
efivarfs         128K  4.4K  119K   4% /sys/firmware/efi/efivars
/dev/nvme0n1p15  105M  6.1M   99M   6% /boot/efi
tmpfs            3.1G  4.0K  3.1G   1% /run/user/1000
ubuntu@SBIHD-GenAI-dev-LVM-AP01:~$ sudo mount /dev/datavg/datalv /databank/
ubuntu@SBIHD-GenAI-dev-LVM-AP01:~$ df -h
Filesystem                 Size  Used Avail Use% Mounted on
/dev/root                   49G  2.3G   46G   5% /
tmpfs                       16G     0   16G   0% /dev/shm
tmpfs                      6.2G  928K  6.2G   1% /run
tmpfs                      5.0M     0  5.0M   0% /run/lock
efivarfs                   128K  4.4K  119K   4% /sys/firmware/efi/efivars
/dev/nvme0n1p15            105M  6.1M   99M   6% /boot/efi
tmpfs                      3.1G  4.0K  3.1G   1% /run/user/1000
/dev/mapper/datavg-datalv  196G   28K  186G   1% /databank

```
update fstab
```
ubuntu@SBIHD-GenAI-dev-LVM-AP01:~$ cat /etc/fstab
LABEL=cloudimg-rootfs   /        ext4   discard,errors=remount-ro       0 1
LABEL=UEFI      /boot/efi       vfat    umask=0077      0 1
/dev/datavg/datalv /databank ext4 defaults 0 2

```

---
# Share Disk Switch

```
ubuntu@SBIHD-GenAI-dev-LVM-ML01:~$ sudo lvchange -an datavg/datalv
ubuntu@SBIHD-GenAI-dev-LVM-ML01:~$ sudo lvs
  LV     VG     Attr       LSize   Pool Origin Data%  Meta%  Move Log Cpy%Sync Convert
  datalv datavg -wi------- 200.00g
ubuntu@SBIHD-GenAI-dev-LVM-ML01:~$ sudo lvchange -ay datavg/datalv
ubuntu@SBIHD-GenAI-dev-LVM-ML01:~$ sudo lvs
  LV     VG     Attr       LSize   Pool Origin Data%  Meta%  Move Log Cpy%Sync Convert
  datalv datavg -wi-a----- 200.00g
ubuntu@SBIHD-GenAI-dev-LVM-ML01:~$ sudo mount /dev/datavg/datalv /databank/

```

---
# Setup AWS Cli and Environment is Jetson

https://claude.ai/chat/49f1d52b-bdd8-45c8-a840-eff1d9e93024

```
# Log into AWS Console
1. Go to AWS Management Console
2. Search for "IAM" in the services search bar
3. Click "Users" in the left sidebar
4. Click "Create user"
5. Enter username: e.g., "cost-explorer-user"
6. Select "Provide user access to the AWS Management Console" (optional)
7. Click "Next"

1. In IAM Console, go to "Policies"
2. Click "Create Policy"
3. Choose the JSON tab
4. Paste this policy:

{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ce:GetCostAndUsage",
                "ce:GetCostForecast",
                "ce:GetTags",
                "ce:GetUsageForecast"
            ],
            "Resource": "*"
        }
    ]
}

1. Click "Next"
2. Name the policy: "CostExplorerReadAccess"
3. Click "Create Policy"
4. Go back to your IAM user
5. Click "Add permissions"
6. Attach the newly created policy
   

1. Select your IAM user
2. Go to "Security credentials" tab
3. Scroll to "Access keys"
4. Click "Create access key"
5. Select "Command Line Interface (CLI)"
6. Click through confirmations
7. IMPORTANT: Download or copy your:
   - Access key ID: ********************
         - Secret access key: cwwev47MV88sGyRft82TRxHqx1G914CIuxBgY9Fp
         - Region: ap-northeast-1
Note: This is the ONLY time you can view the secret key

# For Windows:
# Download and run AWS CLI MSI installer from AWS website

# For MacOS:
brew install awscli

# For Linux:
sudo apt-get update
sudo apt-get install awscli

aws configure

AWS Access Key ID: [Your access key]
AWS Secret Access Key: [Your secret key]
Default region name: [Your region, e.g., us-east-1]
Default output format: json


export AWS_ACCESS_KEY_ID='********************'
export AWS_SECRET_ACCESS_KEY='cwwev47MV88sGyRft82TRxHqx1G914CIuxBgY9Fp'
export AWS_DEFAULT_REGION='ap-northeast-1'

aws sts get-caller-identity

aws ce get-cost-and-usage \
    --time-period Start=$(date -d "30 days ago" +%Y-%m-%d),End=$(date +%Y-%m-%d) \
    --granularity MONTHLY \
    --metrics "UnblendedCost"
    

```
## We use python to access 
```python
import boto3
from datetime import datetime, timedelta
import pandas as pd

def get_daily_costs(days_back=30):
    """
    Retrieve daily AWS costs for the specified number of days
    
    Parameters:
    days_back (int): Number of days to look back (default: 30)
    
    Returns:
    DataFrame with daily costs
    """
    
    # Create AWS Cost Explorer client
    client = boto3.client('ce')
    
    # Calculate time period
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days_back)
    
    # Get cost and usage data
    response = client.get_cost_and_usage(
        TimePeriod={
            'Start': start_date.strftime('%Y-%m-%d'),
            'End': end_date.strftime('%Y-%m-%d')
        },
        Granularity='DAILY',
        Metrics=['UnblendedCost'],
        GroupBy=[
            {'Type': 'DIMENSION', 'Key': 'SERVICE'}
        ]
    )
    
    # Process the response
    daily_costs = []
    for result in response['ResultsByTime']:
        date = result['TimePeriod']['Start']
        
        # Get costs by service
        for group in result['Groups']:
            service = group['Keys'][0]
            cost = float(group['Metrics']['UnblendedCost']['Amount'])
            
            daily_costs.append({
                'Date': date,
                'Service': service,
                'Cost': cost
            })
    
    # Convert to DataFrame
    df = pd.DataFrame(daily_costs)
    
    # Pivot table to show services as columns
    pivot_df = df.pivot_table(
        index='Date',
        columns='Service',
        values='Cost',
        aggfunc='sum'
    ).fillna(0)
    
    # Add total column
    pivot_df['Total'] = pivot_df.sum(axis=1)
    
    return pivot_df

def main():
    try:
        # Make sure AWS credentials are configured
        # Either through AWS CLI or environment variables
        
        # Get costs for last 30 days
        costs_df = get_daily_costs(30)
        
        # Display the results
        print("\nDaily AWS Costs by Service (Last 30 days):")
        print("==========================================")
        
        # Format currency to 2 decimal places
        pd.options.display.float_format = '${:,.2f}'.format
        print(costs_df)
        
        # Print summary statistics
        print("\nSummary Statistics:")
        print("==================")
        print(f"Total Cost: ${costs_df['Total'].sum():,.2f}")
        print(f"Average Daily Cost: ${costs_df['Total'].mean():,.2f}")
        print(f"Highest Daily Cost: ${costs_df['Total'].max():,.2f}")
        print(f"Lowest Daily Cost: ${costs_df['Total'].min():,.2f}")
        
        # Optional: Save to CSV
        costs_df.to_csv('aws_daily_costs.csv')
        print("\nResults saved to 'aws_daily_costs.csv'")
        
    except Exception as e:
        print(f"Error: {str(e)}")
        print("\nPlease make sure:")
        print("1. You have AWS credentials configured")
        print("2. Required permissions are set (Cost Explorer access)")
        print("3. boto3 and pandas libraries are installed")

if __name__ == "__main__":
    main()
```

---

# Install pytorch and cuda
```
sudo apt install ubuntu-drivers-common
sudo apt install alsa-utils
sudo ubuntu-drivers install
sudo ubuntu-drivers devices
sudo apt install nvidia-driver-535
sudo reboot now
wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2204/x86_64/cuda-keyring_1.1-1_all.deb
sudo apt install -y ./cuda-keyring_1.1-1_all.deb
sudo apt update
sudo apt upgrade
sudo apt --fix-broken install
sudo apt -y install cuda-toolkit-12-3
sudo apt install -y ubuntu-drivers-common
sudo ubuntu-drivers install
reboot
sudo nvidia-smi
sudo ubuntu-drivers autoinstall
sudo apt-get -y install cudnn

\#environment path
export PATH="/usr/local/cuda/bin:$PATH"
export LD_LIBRARY_PATH="/usr/local/cuda/lib64:$LD_LIBRARY_PATH"

sudo groupadd opgrp
sudo useradd -m -g opgrp -s /bin/bash ray
sudo useradd -m -g opgrp -s /bin/bash erlin
sudo useradd -m -g opgrp -s /bin/bash russel

<<mgmt>>
ssh-keygen
cat id_rsa.pub

<<ml01>>
mkdir .ssh
chmod 700 .ssh
cd .ssh
touch authorized_keys
chmod 600 authorized_keys
#add pub key
vi authorized_keys

# install python
conda create -n mom python=3.10
pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121


```

---
# Monitoring Setup

## VCS Extension - aws toolkit 
``` policy
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "costexplorer",
            "Effect": "Allow",
            "Action": [
                "ce:GetCostAndUsage",
                "ce:GetDimensionValues",
                "sts:GetCallerIdentity"
            ],
            "Resource": "*"
        },
        {
            "Sid": "vpc",
            "Effect": "Allow",
            "Action": [
                "ec2:DescribeVpcs",
                "ec2:DescribeSubnets",
                "ec2:DescribeRouteTables",
                "ec2:DescribeInternetGateways",
                "ec2:DescribeNatGateways",
                "ec2:DescribeVpcPeeringConnections"
            ],
            "Resource": "*"
        },
        {
            "Sid": "s3",
            "Effect": "Allow",
            "Action": [
                "s3:ListAllMyBuckets",
                "s3:GetBucketLocation"
            ],
            "Resource": "*"
        },
        {
            "Sid": "ec2",
            "Effect": "Allow",
            "Action": [
                "ec2:DescribeInstances",
                "ec2:DescribeAddresses",
                "autoscaling:DescribeAutoScalingGroups"
            ],
            "Resource": "*"
        },
        {
            "Sid": "lambda",
            "Effect": "Allow",
            "Action": [
                "lambda:ListFunctions",
                "lambda:GetFunction",
                "lambda:ListLayers",
                "serverlessrepo:ListApplications"
            ],
            "Resource": "*"
        },
        {
            "Sid": "ecs",
            "Effect": "Allow",
            "Action": [
                "ecs:ListClusters",
                "ecs:ListServices",
                "ecs:ListTasks"
            ],
            "Resource": "*"
        },
        {
            "Sid": "ecr",
            "Effect": "Allow",
            "Action": [
                "ecr:DescribeRepositories",
                "ecr:DescribeImages"
            ],
            "Resource": "*"
        },
        {
            "Sid": "rds",
            "Effect": "Allow",
            "Action": [
                "rds:DescribeDBInstances",
                "rds:DescribeDBClusters",
                "rds:DescribeDBClusterSnapshots",
                "rds:DescribeDBClusterAutomatedBackups",
                "rds:DescribeDBSnapshots"
            ],
            "Resource": "*"
        },
        {
            "Sid": "dynamodb",
            "Effect": "Allow",
            "Action": [
                "dynamodb:ListTables",
                "dynamodb:DescribeTable",
                "dynamodb:ListBackups"
            ],
            "Resource": "*"
        },
        {
            "Sid": "redshift",
            "Effect": "Allow",
            "Action": [
                "redshift:DescribeClusters",
                "redshift:DescribeClusterSnapshots",
                "redshift-serverless:ListWorkgroups"
            ],
            "Resource": "*"
        },
        {
            "Sid": "iam",
            "Effect": "Allow",
            "Action": [
                "iam:ListUsers",
                "iam:GetUser",
                "iam:ListGroupsForUser",
                "iam:ListAttachedUserPolicies",
                "iam:ListRoles",
                "cloudtrail:LookupEvents"
            ],
            "Resource": "*"
        },
        {
            "Sid": "eventbridge",
            "Effect": "Allow",
            "Action": [
                "events:ListRules",
                "events:ListEventBuses",
                "scheduler:ListSchedules",
                "pipes:ListPipes"
            ],
            "Resource": "*"
        },
        {
            "Sid": "DescribeRegions",
            "Effect": "Allow",
            "Action": [
                "ec2:DescribeRegions"
            ],
            "Resource": "*"
        }
    ]
}
```


---
## Alarm Setup
Below include all 
https://claude.ai/chat/7bb97d4a-2161-4b29-bf54-4b151c106bdf

### Flow
CloudWatch => Alarm -> SNS -> SQS -> Lambda(SES identies + IAM role) -> SES -> SMTP -> Email

## Destination Email
```
<EMAIL>

<EMAIL>
<EMAIL>/checkitSBI#123
```

### 设置 SES (Simple Email Service)

Need to setup email sender and receiver both side
```
访问 AWS SES:

登录 AWS 控制台
在搜索栏中输入 "SES" 或找到 "Simple Email Service"
确保您在正确的区域（在您的案例中是 ap-northeast-1）


验证电子邮件身份:

在 SES 控制台左侧菜单中，找到 "Configuration" 下的 "Verified identities"
点击 "Create identity" 按钮
选择身份类型：

"Email address" (适合测试和简单使用)
"Domain" (适合生产环境和企业使用)




如果选择验证电子邮件地址:

选择 "Email address"
输入您要用作发件人的电子邮件地址
点击 "Create identity"
AWS 会向该邮箱发送验证邮件
在邮箱中找到 AWS 发送的验证邮件，点击验证链接
返回 SES 控制台，确认状态变为 "Verified"
```
### Create SQS

#### Access Policy 
```
{
  "Version": "2012-10-17",
  "Id": "__default_policy_ID",
  "Statement": [
    {
      "Sid": "__owner_statement",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::060795926728:root"
      },
      "Action": "SQS:*",
      "Resource": "arn:aws:sqs:ap-northeast-1:060795926728:Mom-SQS"
    },
    {
      "Sid": "topic-subscription-arn:aws:sns:ap-northeast-1:060795926728:MomInfraMonitoringAlert",
      "Effect": "Allow",
      "Principal": {
        "AWS": "*"
      },
      "Action": "SQS:SendMessage",
      "Resource": "arn:aws:sqs:ap-northeast-1:060795926728:Mom-SQS",
      "Condition": {
        "ArnLike": {
          "aws:SourceArn": "arn:aws:sns:ap-northeast-1:060795926728:MomInfraMonitoringAlert"
        }
      }
    },
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "sns.amazonaws.com"
      },
      "Action": "sqs:SendMessage",
      "Resource": "arn:aws:sqs:ap-northeast-1:060795926728:Mom-SQS",
      "Condition": {
        "ArnEquals": {
          "aws:SourceArn": "arn:aws:sns:ap-northeast-1:060795926728:MomInfraMonitoringAlert.fifo"
        }
      }
    }
  ]
}
```

### Create SNS
SNS -> Topics -> FIFO

### Create Subcription
Select SNS created and 'Amazon SQS' and Created SQS
### 创建处理 SQS 消息并发送邮件的 Lambda 函数
```
访问 Lambda 控制台:

登录 AWS 控制台
在搜索栏中输入 "Lambda" 或从服务列表中选择 "Lambda"
确保您在正确的区域（ap-northeast-1）


创建新的 Lambda 函数:

点击 "Create function"（创建函数）
选择 "Author from scratch"（从头开始创作）
填写基本信息：

Function name（函数名）: MonitoringAlertEmailHandler
Runtime（运行时）: Node.js 20.x
Architecture（架构）: x86_64
Permissions（权限）: Create a new role with basic Lambda permissions（创建具有基本 Lambda 权限的新角色）


点击 "Create function"（创建函数）


配置代码:

在代码编辑器中，复制粘贴以下代码：
```

```node
import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';

// Configuration
const REGION = 'ap-northeast-1';
const FROM_EMAIL = process.env.FROM_EMAIL;
const TO_EMAIL = process.env.TO_EMAIL;

const sesClient = new SESClient({ region: REGION });

export const handler = async (event) => {
    console.log('Received event:', JSON.stringify(event, null, 2));
    
    try {
        for (const record of event.Records) {
            // Try to parse JSON, if fails, use the raw message
            let messageContent;
            try {
                messageContent = JSON.parse(record.body);
            } catch (e) {
                messageContent = record.body;
            }
            
            console.log('Processing message:', 
                typeof messageContent === 'object' 
                    ? JSON.stringify(messageContent, null, 2) 
                    : messageContent
            );
            
            // Build email subject based on message type
            let subject = 'Infrastructure Alert Notification';
            if (record.attributes && record.attributes.MessageGroupId) {
                subject = `Infrastructure Alert - ${record.attributes.MessageGroupId}`;
            }
            
            // Build message body
            let messageBody = typeof messageContent === 'object' 
                ? JSON.stringify(messageContent, null, 2)
                : messageContent;

            // Add additional context if available
            if (record.messageAttributes) {
                messageBody += '\n\nMessage Attributes:\n' + 
                    JSON.stringify(record.messageAttributes, null, 2);
            }

            // Build email parameters
            const emailParams = {
                Source: FROM_EMAIL,
                Destination: {
                    ToAddresses: [TO_EMAIL]
                },
                Message: {
                    Subject: {
                        Data: subject,
                        Charset: 'UTF-8'
                    },
                    Body: {
                        Text: {
                            Data: `
Alert Details:
-------------------
Time: ${new Date().toISOString()}
Message ID: ${record.messageId}
Alert Content: 
${messageBody}
                            `,
                            Charset: 'UTF-8'
                        }
                    }
                }
            };

            // Send email
            console.log('Sending email...');
            const command = new SendEmailCommand(emailParams);
            const response = await sesClient.send(command);
            console.log('Email sent successfully:', response.MessageId);
        }
        
        return {
            statusCode: 200,
            body: JSON.stringify('Alerts processed and emails sent successfully')
        };
    } catch (error) {
        console.error('Error processing message:', error);
        throw error;
    }
};
```
```
配置函数设置:

在 "Configuration" 标签页中：

点击 "General configuration"
点击 "Edit"
设置 Memory（内存）: 128 MB（足够处理简单的邮件发送）
设置 Timeout（超时）: 30 秒
点击 "Save"




安装依赖:

由于我们使用了 AWS SDK v3，需要添加层（Layer）或在本地打包依赖
方法一：使用 Lambda Layer

在 Lambda 控制台点击 "Layers"
点击 "Add a layer"
选择 "AWS layers"
搜索并添加 AWS-Parameters-and-Secrets-Lambda-Extension

更新环境变量:

在 "Configuration" -> "Environment variables" 中：
点击 "Edit"
添加以下变量：

FROM_EMAIL: 您在 SES 验证过的发件人邮箱
TO_EMAIL: 接收告警的邮箱地址


点击 "Save"


```
Send Test Message
测试函数:

点击 "Test" 标签
点击 "Create new test event"
选择 "SQS template"
使用以下测试数据：

```
{
  "Records": [
    {
      "messageId": "19dd0b57-b21e-4ac1-bd88-01bbb068cb78",
      "receiptHandle": "MessageReceiptHandle",
      "body": "Hello from SQS!",
      "attributes": {
        "ApproximateReceiveCount": "1",
        "SentTimestamp": "1523232000000",
        "SenderId": "123456789012",
        "ApproximateFirstReceiveTimestamp": "1523232000001"
      },
      "messageAttributes": {},
      "md5OfBody": "{{{md5_of_body}}}",
      "eventSource": "aws:sqs",
      "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:MyQueue",
      "awsRegion": "us-east-1"
    }
  ]
}
```

### 配置 Lambda 的 IAM 权限（允许访问 SQS 和 SES）
   访问 Lambda 函数的 IAM 角色:
   
   在 Lambda 函数页面，点击 "Configuration" 标签
   选择左侧的 "Permissions"
   点击 "Role name" 下方的链接（这会带您到 IAM 控制台）
      AWSLambdaBasicExecutionRole-177f4941-39df-412f-b653-c79617d5a29d
   添加所需权限:
   
   在 IAM 角色页面，点击 "Add permissions" 按钮
   选择 "Create inline policy"
   选择 JSON 标签页
   粘贴以下策略：
   ```
   {
       "Version": "2012-10-17",
       "Statement": [
           {
               "Effect": "Allow",
               "Action": [
                   "ses:SendEmail",
                   "ses:SendRawEmail"
               ],
               "Resource": "*"
           },
           {
               "Effect": "Allow",
               "Action": [
                   "sqs:ReceiveMessage",
                   "sqs:DeleteMessage",
                   "sqs:GetQueueAttributes"
               ],
               "Resource": "arn:aws:sqs:ap-northeast-1:060795926728:Mom-SQS"
           },
           {
               "Effect": "Allow",
               "Action": [
                   "logs:CreateLogGroup",
                   "logs:CreateLogStream",
                   "logs:PutLogEvents"
               ],
               "Resource": "arn:aws:logs:ap-northeast-1:060795926728:log-group:/aws/lambda/MonitoringAlertEmailHandler:*"
           }
       ]
   }
```
   Review 和命名策略:
   
   点击 "Review policy"（检查策略）
   命名策略：MonitoringAlertEmailHandler-Policy
   在 Description（描述）中可以添加：Allow Lambda to access SQS, SES and CloudWatch Logs
   点击 "Create policy"（创建策略）
   
   
   验证权限:
   
   返回到 IAM 角色页面
   在 "Permissions policies" 下，您应该能看到：
   
   新创建的内联策略 MonitoringAlertEmailHandler-Policy
   AWS 默认的 Lambda 执行角色策略 AWSLambdaBasicExecutionRole
First, let's check and create the IAM role:

Go to IAM Console
Click "Roles" -> "Create role"

EC2WatchAgentServerRole

Choose "AWS service" and "EC2"
Add these policies:

CloudWatchAgentServerPolicy
AmazonSSMManagedInstanceCore (for Systems Manager)




Attach the role to your EC2 instances:

Go to EC2 Console
Select each instance
Actions -> Security -> Modify IAM role
Select the role we just created
### Setup SQS Trigger

```
返回到 Lambda 函数页面:

在 Function overview 区域，点击 "Add trigger" 按钮


配置触发器:

Trigger configuration:

选择 "SQS" 作为触发器类型
在 SQS queue 下拉菜单中选择 "Mom-SQS"
Batch size（批处理大小）: 建议从小的数值开始，如 1 或 5（最大可设置为 10）
勾选 "Activate trigger"（激活触发器）




高级设置（可选）:

Batch window（批处理窗口）: 默认为 0，表示立即处理
如果需要，可以设置为最大 300 秒（5分钟）
Filter criteria（筛选标准）: 如果需要只处理特定类型的消息，可以添加筛选条件
DLQ - Dead Letter Queue（死信队列）: 可以配置处理失败的消息转发到另一个队列
Scaling controls（扩展控制）: 可以设置并发执行限制


创建触发器:

检查所有配置是否正确
点击 "Add" 按钮创建触发器



现在，整个流程已经配置完成：

监控告警 -> Mom-SQS 队列
SQS 触发 -> Lambda 函数
Lambda 函数 -> 通过 SES 发送邮件

要测试整个流程，我们可以：

在 SQS 控制台发送测试消息
观察 Lambda 的 CloudWatch Logs
检查目标邮箱是否收到邮件


```

## Create Alarm
```
Create CloudWatch Alarm:

Go to CloudWatch Console
Click "Alarms" in the left sidebar
Click "Create alarm"
Click "Select metric"
Choose the service and metric you want to monitor
(e.g., EC2 > Per-Instance Metrics > CPUUtilization)
Select the specific resource
Click "Select metric"


Configure Alarm Conditions:

Define your threshold conditions:

Choose statistic (Average, Maximum, etc.)
Set period (e.g., 5 minutes)
Set threshold type (Static or Anomaly detection)
Set threshold value (e.g., CPU > 80%)


Click "Next"


Configure Notification:

Under "Notification", select:

Select "In alarm" state
Choose the SNS topic you created earlier


Add a custom message (optional)
Click "Next"


Add Name and Description:

Enter alarm name (e.g., "High-CPU-Alert")
Enter description (optional)
Click "Next"


Preview and Create:

Review your settings
Click "Create alarm"



Additional Tips:

Test the alarm by temporarily lowering the threshold
You can add multiple phone numbers by creating additional subscriptions
Consider the AWS SMS costs in your region
SMS might not be available in all regions

Let's use the first way with "Statistics". I'll guide you step by step:

1. When you see all the instance metrics listed:
   - Make sure all 4 instance metrics are selected
   - Click "Add math" dropdown
   - Navigate to "Statistics"
   - Select "AVG" (this will create an average across all instances)

2. After adding the AVG statistic:
   - You should see a new line with a math expression
   - This will be a single metric representing the average CPU across all instances
   - Select only this new averaged metric line (uncheck the individual instance metrics)
   - Now the "Select metric" button should become active

3. The resulting metric will represent the average CPU utilization across all your instances:
   - SBIIHD-GenAI-dev-LVM-MGT
   - SBIIHD-GenAI-dev-LVM-AP01
   - SBIIHD-GenAI-dev-LVM-OPT
   - SBIIHD-GenAI-dev-LVM-ML01

Would you like me to continue guiding you through this process? Are you able to see the AVG metric after adding it?
```
## Server Agent Install

#### Role 
Verify the policies attached to this role:

Click on EC2WatchAgentServerRole
Make sure it has these policies:

CloudWatchAgentServerPolicy
AmazonSSMManagedInstanceCore (optional but recommended)




Attach this role to your EC2 instances:

Go to EC2 Console
Select your instance (SBIHD-GenAI-dev-LVM-ML01)
Click Actions → Security → Modify IAM role
Select EC2WatchAgentServerRole
Click "Update IAM role

```
# For Ubuntu amd64 architecture
wget https://s3.amazonaws.com/amazoncloudwatch-agent/ubuntu/amd64/latest/amazon-cloudwatch-agent.deb
# Install the package using dpkg
sudo dpkg -i -E ./amazon-cloudwatch-agent.deb

# If you see any dependency errors, run
sudo apt-get install -f

sudo mkdir -p /opt/aws/amazon-cloudwatch-agent/etc
sudo nano /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json

```
#### agent json 
```json
{
    "agent": {
        "metrics_collection_interval": 60,
        "run_as_user": "root"
    },
    "metrics": {
        "namespace": "GenAI_System_Metrics",
        "metrics_collected": {
            "swap": {
                "measurement": [
                    "swap_used_percent",
                    "swap_used",
                    "swap_free"
                ],
                "metrics_collection_interval": 60
            },
            "mem": {
                "measurement": [
                    "mem_used_percent",
                    "mem_available",
                    "mem_available_percent",
                    "mem_used"
                ],
                "metrics_collection_interval": 60
            },
            "disk": {
                "measurement": [
                    "disk_used_percent"
                ],
                "metrics_collection_interval": 60
            }
        },
        "append_dimensions": {
            "InstanceId": "${aws:InstanceId}",
            "InstanceType": "${aws:InstanceType}"
        }
    }
}

```
#### restart agent
```
# Start the agent
sudo systemctl start amazon-cloudwatch-agent

# Enable on boot
sudo systemctl enable amazon-cloudwatch-agent

# Check status
sudo systemctl status amazon-cloudwatch-agent

# Check logs
sudo tail -f /opt/aws/amazon-cloudwatch-agent/logs/amazon-cloudwatch-agent.log
```

## Troubleshooting

### CloudWatch Log

Cloudwatch -> Logs -> Log groups
![[Pasted image 20241029220126.png]]
### enable SNS Delivery status logging
SNS -> Topics -> Delievery status logging.   SNSSUccessFeedback/SNSFailureFeedback

### Lambda Function log
Lambda -> Monitor -> View CloudWatch Logs

## GPU Monitoring
#### json file setup
```
{
    "agent": {
        "metrics_collection_interval": 60,
        "run_as_user": "root"
    },
    "metrics": {
        "namespace": "GenAI_System_Metrics",
        "metrics_collected": {
            "swap": {
                "measurement": [
                    "swap_used_percent",
                    "swap_used",
                    "swap_free"
                ],
                "metrics_collection_interval": 60
            },
            "mem": {
                "measurement": [
                    "mem_used_percent",
                    "mem_available",
                    "mem_available_percent",
                    "mem_used"
                ],
                "metrics_collection_interval": 60
            },
            "disk": {
                "measurement": [
                    "disk_used_percent"
                ],
                "metrics_collection_interval": 60
            },
            "nvidia_gpu": {
                "measurement": [
                    "utilization_gpu",
                    "temperature_gpu",
                    "power_draw",
                    "memory_used",
                    "memory_total",
                    "memory_free"
                ],
                "metrics_collection_interval": 60,
                "devices": "*"
            }
        },
        "append_dimensions": {
            "InstanceId": "${aws:InstanceId}",
            "InstanceType": "${aws:InstanceType}"
        }
    }
}
```
#### ~~install nvidia agent~~
   in the latest aws agent , navidia gpu monioring already there
```
# Download the NVIDIA GPU plugin
wget https://s3.amazonaws.com/amazoncloudwatch-agent/nvidia/linux/amd64/latest/nvidia-cloudwatch-plugin.deb

# Install the plugin
sudo dpkg -i nvidia-cloudwatch-plugin.deb

# If there are any dependency issues
sudo apt-get install -f


```

#### restart
```
sudo systemctl restart amazon-cloudwatch-agent
```


# Firewall/Network Access Control

https://claude.ai/chat/1ed9e926-c54d-45cd-a936-64954f9921ff
 need to access from public network link
```
8243 - 8246
80
443
22
```

![[Pasted image **************.png]]![[Pasted image **************.png]]



# Migration to AWS

idea is tar miniconda3 and all pg and project file to remote and create symlink 

## some python libaray issue after migration
```
(mom) mlusr@SBIHD-GenAI-dev-LVM-ML01:/opt/app/mom$ python modeltest.py
Traceback (most recent call last):
  File "/databank/app/mom/modeltest.py", line 33, in <module>
    import psycopg2
  File "/home/<USER>/miniconda3/envs/mom/lib/python3.10/site-packages/psycopg2/__init__.py", line 51, in <module>
    from psycopg2._psycopg import (                     # noqa
ImportError: libpq.so.5: cannot open shared object file: No such file or directory

sudo apt-get install libpq5 libpq-dev
```

## database migration
```bash

sudo systemctl stop mom_ai.service
sudo systemctl stop mom_emb.service
sudo systemctl stop pg16.service
tar zcvf postgresql_backup_$(date +%Y%m%d_%H%M%S).tar.gz --dereference bin lib pgvector data conf

sudo systemctl start pg16.service
sudo systemctl start mom_emb.service
sudo systemctl start mom_ai.service

sudo systemctl status pg16.service
sudo systemctl status mom_emb.service
sudo systemctl status mom_ai.service

cd /usr/share/postgresql/16
backup above all in above folder

scp -P 60001 -rp Key4ML.pem azusr@127.0.0.1:/tmp
#setup config file and make sure config  file and keyfile only owner can access
Host jumpserver
    HostName ************
    User ubuntu
    IdentityFile ~/.ssh/Key4ML.pem
    ForwardAgent yes

Host ml01
    HostName ********
    User ubuntu
    ProxyJump jumpserver
    IdentityFile ~/.ssh/Key4ML.pem

scp -rp xxx ml01:/tmp
cd /databank/database/16
mkdir share
tar zxvf /databank/install/migration/pgshare.tar.gz
cd /usr/share
sudo mkdir postgresql
cd postgresql/
sudo ln -s /databank/database/16/share 16
ubuntu@SBIHD-GenAI-dev-LVM-ML01:/usr/share/postgresql$ ls
16
ubuntu@SBIHD-GenAI-dev-LVM-ML01:/usr/share/postgresql$ ls -lrt
total 0
lrwxrwxrwx 1 root root 27 Nov  6 16:42 16 -> /databank/database/16/share


postgres@SBIHD-GenAI-dev-LVM-ML01:/opt/postgresql$ pg_ctl start -D /opt/postgresql/data
waiting for server to start....2024-11-06 16:43:52.580 JST [41731] FATAL:  could not load server certificate file "/etc/ssl/certs/ssl-cert-snakeoil.pem": No such file or directory
2024-11-06 16:43:52.581 JST [41731] LOG:  database system is shut down
 stopped waiting
pg_ctl: could not start server
Examine the log output.
<<solution>>
sudo apt-get install ssl-cert
# confirm postgresql.conf setup
# OR Option 2: Point to correct certificate locations
ssl = on
ssl_cert_file = '/etc/ssl/certs/ssl-cert-snakeoil.pem'
ssl_key_file = '/etc/ssl/private/ssl-cert-snakeoil.key'
# confirm permission
sudo ls -l /etc/ssl/certs/ssl-cert-snakeoil.pem
sudo ls -l /etc/ssl/private/ssl-cert-snakeoil.key
sudo chown postgres:postgres /etc/ssl/certs/ssl-cert-snakeoil.pem
sudo chown postgres:postgres /etc/ssl/private/ssl-cert-snakeoil.key
sudo chmod 600 /etc/ssl/private/ssl-cert-snakeoil.key
sudo ls -l /etc/ssl/certs/ssl-cert-snakeoil.pem
sudo ls -l /etc/ssl/private/ssl-cert-snakeoil.key

postgres@SBIHD-GenAI-dev-LVM-ML01:~$ ls -lrt /etc/ssl/private/ssl-cert-snakeoil.key
ls: cannot access '/etc/ssl/private/ssl-cert-snakeoil.key': Permission denied

#let us move key to some other place
sudo mv /etc/ssl/private/ssl-cert-snakeoil.key /databank/database/16/ssl

2024-11-06 16:56:32.494 JST [42091] LOG:  listening on IPv4 address "0.0.0.0", port 5432
2024-11-06 16:56:32.495 JST [42091] FATAL:  could not create lock file "/var/run/postgresql/.s.PGSQL.5432.lock": No such file or directory
2024-11-06 16:56:32.502 JST [42091] LOG:  database system is shut down

mkdir -p /opt/postgresql/run
chown postgres:postgres /opt/postgresql/run
chmod 2775 /opt/postgresql/run
Then update postgresql.conf:
unix_socket_directories = '/opt/postgresql/run'
cd /var/run
sudo ln -s /opt/postgresql/run postgresql

#for test
pg_ctl start -D /opt/postgresql/data
pg_ctl stop -m f



```

## Mom_ai migration issue
```

Nov 06 18:13:40 SBIHD-GenAI-dev-LVM-ML01 python[46327]: /opt/app/miniconda3/envs/mom/lib/python3.10/site-packages/pydub             /utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
Nov 06 18:13:40 SBIHD-GenAI-dev-LVM-ML01 python[46327]:   warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg,              but may not work", RuntimeWarning)
Nov 06 18:13:47 SBIHD-GenAI-dev-LVM-ML01 python[46327]: Special tokens have been added in the vocabulary, make sure the              associated word embeddings are fine-tuned or trained.
Nov 06 18:14:13 SBIHD-GenAI-dev-LVM-ML01 python[46327]: Special tokens have been added in the vocabulary, make sure the associated wtuned or trained.
Nov 06 18:14:13 SBIHD-GenAI-dev-LVM-ML01 python[46327]: Special tokens have been added in the vocabulary, make sure the associated wtuned or trained.
Nov 06 18:14:14 SBIHD-GenAI-dev-LVM-ML01 python[46327]: Traceback (most recent call last):
Nov 06 18:14:14 SBIHD-GenAI-dev-LVM-ML01 python[46327]:   File "bits_momx_profanity_noquan.py", line 1426, in <module>
Nov 06 18:14:14 SBIHD-GenAI-dev-LVM-ML01 python[46327]:   File "bits_momx_profanity_noquan.py", line 1359, in main
Nov 06 18:14:14 SBIHD-GenAI-dev-LVM-ML01 python[46327]:   File "bits_momx_profanity_noquan.py", line 1326, in update_in_progress_to_
Nov 06 18:14:14 SBIHD-GenAI-dev-LVM-ML01 python[46327]:   File "bits_momx_profanity_noquan.py", line 597, in scan_convert_status_mat
Nov 06 18:14:14 SBIHD-GenAI-dev-LVM-ML01 python[46327]:   File "/opt/app/miniconda3/envs/mom/lib/python3.10/site-packages/psycopg2/_n connect
Nov 06 18:14:14 SBIHD-GenAI-dev-LVM-ML01 python[46327]:     conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
Nov 06 18:14:14 SBIHD-GenAI-dev-LVM-ML01 python[46327]: psycopg2.OperationalError: connection to server at "********", port 5432 faionf entry for host "********", user "momusr", database "momdb", SSL encryption
Nov 06 18:14:14 SBIHD-GenAI-dev-LVM-ML01 python[46327]: connection to server at "********", port 5432 failed: FATAL:  no pg_hba.conf7", user "momusr", database "momdb", no encryption


```
Soltuon
```
sudo apt-get install ffmpeg

# edit pg_hba.conf
host    all             all             ********/24             md5

```

## gpu pool implementation
```python
import torch
import numpy as np
import pandas as pd

from spectralcluster import Deflicker
from spectralcluster import MultiStageClusterer
from spectralcluster import SpectralClusterer
from whisper import load_model
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn import metrics
import prince
from transformers import AutoModelForSpeechSeq2Seq, AutoProcessor, pipeline
from transformers import WhisperTokenizer, WhisperTokenizerFast, AddedToken
from transformers import WhisperProcessor, WhisperForConditionalGeneration, WhisperTokenizer
from transformers import Wav2Vec2ForSequenceClassification, AutoFeatureExtractor
from typing import Optional, Collection
from typing import List, Dict, Union
import torchaudio.transforms as T
from speechbrain.inference.speaker import EncoderClassifier
from pyannote.audio import Audio
from pyannote.core import Segment
from pydub import AudioSegment
import soundfile as sf
from dotenv import load_dotenv
import tempfile
import json
import gc
import os
import re
import unicodedata
from bits_logger import Logger
import psycopg2
import logging
from functools import wraps
import subprocess
import xml.etree.ElementTree as ET

load_dotenv()

# Configure debug logger
debug_logger = logging.getLogger('gpu_pool')
debug_logger.setLevel(logging.DEBUG)

# Console handler with custom formatter
ch = logging.StreamHandler()
formatter = logging.Formatter('🔍 GPU_POOL | %(asctime)s | %(message)s', 
                            datefmt='%Y-%m-%d %H:%M:%S')
ch.setFormatter(formatter)
debug_logger.addHandler(ch)


# Debug flag - set to False to disable all debug output
DEBUG_GPU_POOL = True
def debug_log(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        if not DEBUG_GPU_POOL:
            return func(*args, **kwargs)
            
        debug_logger.debug(f"Calling {func.__name__} with args: {args}, kwargs: {kwargs}")
        result = func(*args, **kwargs)
        debug_logger.debug(f"{func.__name__} returned: {result}")
        return result
    return wrapper

torch_dtype = torch.float16 if torch.cuda.is_available() else torch.float32
audio_model_version = int(os.getenv("AUDIO_MODEL_VERSION"))
worker_count = int(os.getenv("WORKER_COUNT"))
failback_threshold = float(os.getenv("POSITIVE_THRESHOLD"))
max_speakers = int(os.getenv("MAX_SPEAKERS"))
less_memory = 0

class GPUPoolManager:
    def __init__(self):
        self.available_gpus = {}
        self._init_gpu_pool()

    def _get_gpu_memory_info_from_nvidia_smi(self):
        try:
            # 使用 XML 格式获取更准确的数据
            result = subprocess.check_output(
                ['nvidia-smi', '-q', '-x'], 
                encoding='utf-8'
            )
            root = ET.fromstring(result)
            
            memory_info = []
            for gpu in root.findall(".//gpu"):
                index = int(gpu.find('minor_number').text)
                mem_info = gpu.find('.//fb_memory_usage')
                total = int(mem_info.find('total').text.split()[0])  # MiB
                used = int(mem_info.find('used').text.split()[0])    # MiB
                free = int(mem_info.find('free').text.split()[0])    # MiB
                
                memory_info.append({
                    'index': index,
                    'total': total * 1024 * 1024,  # Convert to bytes
                    'used': used * 1024 * 1024,
                    'free': free * 1024 * 1024
                })
            return memory_info
        except Exception as e:
            debug_logger.error(f"Error getting GPU info from nvidia-smi: {e}")
            return None
			
    @debug_log
    def _init_gpu_pool(self):
        if not torch.cuda.is_available():
            debug_logger.debug("No CUDA devices available")
            return
        
        memory_info = self._get_gpu_memory_info_from_nvidia_smi()
        if not memory_info:
            debug_logger.warning("Failed to get GPU memory info, falling back to torch.cuda")
            return
        
        for gpu_info in memory_info:
            i = gpu_info['index']
            props = torch.cuda.get_device_properties(i)
            
            # Consider a GPU available if it has more than 2GB free memory
            self.available_gpus[i] = gpu_info['free'] > 2e9
            
            if DEBUG_GPU_POOL:
                status = "AVAILABLE" if self.available_gpus[i] else "IN USE"
                debug_logger.debug(
                    f"Initialized GPU {i}: {props.name} | "
                    f"Total: {gpu_info['total']/1e9:.2f}GB | "
                    f"Used: {gpu_info['used']/1e9:.2f}GB | "
                    f"Free: {gpu_info['free']/1e9:.2f}GB | "
                    f"Status: {status}"
                )
				
    @debug_log
    def _get_gpu_memory_info(self):
        memory_info = self._get_gpu_memory_info_from_nvidia_smi()
        if not memory_info:
            return []
            
        result = []
        for gpu_info in memory_info:
            i = gpu_info['index']
            if self.available_gpus.get(i, False):
                result.append((i, gpu_info['free']))
                if DEBUG_GPU_POOL:
                    debug_logger.debug(
                        f"GPU {i} - Free: {gpu_info['free']/1e9:.2f}GB"
                    )
        return result

    @debug_log
    def get_optimal_gpu(self):
        if not self.available_gpus:
            debug_logger.debug("No GPUs available in pool")
            return None
            
        memory_info = self._get_gpu_memory_info()
        memory_info.sort(key=lambda x: x[1], reverse=True)
        optimal_gpu = memory_info[0][0]
        if DEBUG_GPU_POOL:
            debug_logger.debug(f"Selected GPU {optimal_gpu} as optimal choice")
        return optimal_gpu

# Global GPU pool manager instance
gpu_pool = GPUPoolManager()

@debug_log
def get_device(cuda_id=None):
    """
    Get the device to use for PyTorch operations.
    
    Args:
        cuda_id (int, optional): Specific CUDA device ID to use. 
                               If None, automatically selects GPU with most free memory.
    
    Returns:
        str: Device specification ('cuda:n' or 'cpu')
    """
    if not torch.cuda.is_available():
        debug_logger.debug("CUDA not available, using CPU")
        return "cpu"
        
    if cuda_id is not None:
        if cuda_id >= torch.cuda.device_count():
            msg = f"CUDA device {cuda_id} not available. Max device id is {torch.cuda.device_count()-1}"
            debug_logger.error(msg)
            raise ValueError(msg)
        debug_logger.debug(f"Using specified CUDA device: {cuda_id}")
        return f"cuda:{cuda_id}"
    
    optimal_gpu = gpu_pool.get_optimal_gpu()
    device = f"cuda:{optimal_gpu}" if optimal_gpu is not None else "cpu"
    debug_logger.debug(f"Selected device: {device}")
    return device
	
def setup_model_v2(sample_rate=22050):

    global audio_model,emb_model,emb_audio, model_temperature, audio_processor, audio_pipeline, audio_embedding_column, model_failback
    global lang_processor, lang_model, lang_tokenizer
    model_temperature = float(os.getenv("MODEL_TEMPERATURE"))
    audio_embedding_column = "segments" if audio_model_version == 1 else "chunks"
    emb_model = EncoderClassifier.from_hparams(source="speechbrain/spkrec-ecapa-voxceleb", savedir="/opt/app/mom/model", huggingface_cache_dir="/opt/app/mom/model", run_opts={"device":get_device()})
    emb_audio = Audio(sample_rate=sample_rate, mono="downmix")

    model_id = "/opt/app/mom/model/whisper-large-v2"
    audio_model = AutoModelForSpeechSeq2Seq.from_pretrained(model_id, torch_dtype=torch_dtype).half()
    audio_model.to(get_device())
    audio_processor = AutoProcessor.from_pretrained(model_id)
    audio_pipeline = pipeline(
        "automatic-speech-recognition",
        model=audio_model,
        tokenizer=audio_processor.tokenizer,
        feature_extractor=audio_processor.feature_extractor,
        # max_new_tokens=128,
        chunk_length_s=30,
        batch_size=worker_count,
        return_timestamps=True,
        torch_dtype=torch_dtype,
        device=get_device(),
        model_kwargs={"attn_implementation": "flash_attention_2"}
    )

    model_failback = load_model("large", device=get_device(), download_root="/opt/app/mom/model",).half().float()

    if less_memory == 0:
        lang_processor = WhisperProcessor.from_pretrained("/opt/app/mom/model/whisper-small")
        lang_model = WhisperForConditionalGeneration.from_pretrained("/opt/app/mom/model/whisper-small")
        lang_tokenizer = WhisperTokenizer.from_pretrained("/opt/app/mom/model/whisper-small")
        # 检查 CUDA 是否可用并将模型移动到 GPU
        if torch.cuda.is_available():
            device = get_device()
            lang_model.to(device)  # 将模型移动到 GPU
        else:
            device = torch.device("cpu")
            Logger.log("Model Setup", "CUDA is not available. Running language detect model on CPU instead.")
    else:
        lang_processor = WhisperProcessor.from_pretrained("/opt/app/mom/model/whisper-small")
        lang_model = WhisperForConditionalGeneration.from_pretrained("/opt/app/mom/model/whisper-small")
        lang_tokenizer = WhisperTokenizer.from_pretrained("/opt/app/mom/model/whisper-small")

setup_model_v2()

```

# Protect bastion host 

## check ssh login failed/succ
```shell
sudo journalctl | grep "Invalid user" | awk '{print "Time:", $1, $2, $3, "User:", $8, "IP:", $10}'
sudo journalctl | grep "Invalid user" | awk '{print "Time:", $1, $2, $3, "User:", $8, "IP:", $10}'|awk '{print $8}' | sort -u
sudo journalctl | grep "session opened"

sudo tail -f /var/log/auth.log 
sudo tail -f /var/log/auth.log | grep "Invalid user" | awk '{print "Time:", $1, $2, $3, "User:", $8, "IP:", $10}'
```
## Setup Fail2ban
```
# Install and configure Fail2ban
sudo apt-get install fail2ban
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local

# Confirm sshd configure# Edit SSH config
sudo nano /etc/ssh/sshd_config

# Add/modify these lines
PermitRootLogin no
MaxAuthTries 3
PasswordAuthentication no  # Only if using key-based authentication
AllowUsers your_username   # Whitelist specific users


# Config fail2ban setings
[sshd]
port    = ssh
logpath = %(sshd_log)s
backend = %(sshd_backend)s
enabled = true
filter = sshd
maxretry = 1
bantime = 3600  # Ban for 1 hour
findtime = 60  # Within 10 minutes

sudo systemctl restart fail2ban

# Check status
# Check if service is running
sudo systemctl status fail2ban

# Verify your jail settings
sudo fail2ban-client get sshd bantime
sudo fail2ban-client get sshd findtime
sudo fail2ban-client get sshd maxretry
sudo fail2ban-client status sshd

sudo tail -f /var/log/fail2ban.log

<<remove banned ip from the list>>
sudo fail2ban-client set sshd unbanip IP_ADDRESS
sudo fail2ban-client set sshd unbanip --all

<<Setup ignore ip list>>
[DEFAULT]
bantime.increment = true
ignoreip = 127.0.0.1/8 ::1 ********/24 **************/32

sudo fail2ban-client get sshd ignoreip
sudo fail2ban-client get sshd bantime.increment

```
## block nobody
```
<<block nobody>>
sudo journalctl --since today|grep sshd|grep nobody
Nov 07 10:20:02 SBIHD-GenAI-dev-LVM-MGT sshd[56621]: Connection closed by authenticating user nobody *************** port 18773 [preauth]
Nov 07 10:57:31 SBIHD-GenAI-dev-LVM-MGT sshd[56793]: Connection closed by authenticating user nobody *************** port 39736 [preauth]
Nov 07 12:03:33 SBIHD-GenAI-dev-LVM-MGT sshd[57196]: Connection closed by authenticating user nobody ************** port 38069 [preauth]
Nov 07 12:13:01 SBIHD-GenAI-dev-LVM-MGT sshd[57237]: Connection closed by authenticating user nobody ************** port 52850 [preauth]
Nov 07 15:03:01 SBIHD-GenAI-dev-LVM-MGT sshd[60322]: Connection closed by authenticating user nobody ************** port 40033 [preauth]
Nov 07 15:41:06 SBIHD-GenAI-dev-LVM-MGT sshd[61465]: Connection closed by authenticating user nobody ************** port 41279 [preauth]
Nov 07 15:55:27 SBIHD-GenAI-dev-LVM-MGT sshd[61608]: Connection closed by authenticating user nobody ************* port 34835 [preauth]

sudo nano /etc/ssh/sshd_config
# Add or modify
DenyUsers nobody

```

 ## ufw setup  optional

```
<<Option>>
# Below is option as we already have control in aws side
sudo ufw allow ssh
sudo ufw enable
sudo ufw default deny incoming
sudo ufw allow from trusted_ip to any port 22  # Allow specific IPs
sudo ufw status
```

## check ip details
```
<<check ip details>>
whois *************

```
## create tunnel in mgt jumpserver
```
cd .ssh
eval $(ssh-agent)
ssh-add id_rsa
ssh-add -l
ssh -A -i id_rsa ray@************
-- then create tunnel
ssh -R 0.0.0.0:5888:********:5432 127.0.0.1

```
## knocking access

#### Configure knockd
```
ubuntu@SBIHD-GenAI-dev-LVM-MGT:~$ cat /etc/knockd.conf
[options]
        UseSyslog
        LogFile = /var/log/knockd.log
        interface = eth0    # Add this line with your network interface

[openSSH]
        sequence    = 7000,8001,9002,9900,7002
        seq_timeout = 10
        tcpflags    = syn
        start_command = /sbin/iptables -I INPUT 1 -s %IP% -p tcp --dport 22 -j ACCEPT
        cmd_timeout   = 30
        stop_command  = /sbin/iptables -D INPUT -s %IP% -p tcp --dport 22 -j ACCEPT

[closeSSH]
        sequence    = 7000,8001,9900,7002,9002
        seq_timeout = 10
        tcpflags    = syn
        start_command = /sbin/iptables -D INPUT -s %IP% -p tcp --dport 22 -j ACCEPT

ubuntu@SBIHD-GenAI-dev-LVM-MGT:~$ cat /etc/default/knockd
# control if we start knockd at init or not
# 1 = start
# anything else = don't start
# PLEASE EDIT /etc/knockd.conf BEFORE ENABLING
START_KNOCKD=1

# command line options
KNOCKD_OPTS="-i eth0"

```

#### Emergency access commands !!! <font color="#ffff00">Make sure you keep one session open before anything wrong</font>
```
sudo iptables -F  # Flush all rules
iptables-restore < ~/iptables_backup_$(date +%Y%m%d)
sudo systemctl stop knockd   # Stop knockd if needed
```

#### Steps
```
# 1. FIRST run this to protect existing connections !!!!!!!!!
sudo iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# 2. THEN run this to block new SSH connections
sudo iptables -A INPUT -p tcp --dport 22 -j DROP

# 3. add port access# Allow the knock ports and if in aws, you need to setup network as well
sudo iptables -A INPUT -p tcp --dport 7000 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 8001 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 9002 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 9900 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 7002 -j ACCEPT

```
  <span style="background:#40a9ff">AWS/Oracle You should check if knockport and ssh port is opened or not.;</span>
#### debug
```
sudo tail -f /var/log/knockd.log /var/log/syslog

# Use tcpdump to see if knock packets are arriving
sudo tcpdump -i enp0s6 -nn 'tcp[tcpflags] & tcp-syn != 0' and 'port 7000 or port 8001 or port 9002 or port 9900 or port 7002'

Keep tcpdump running and try your knock sequence from the other server. 


start one port 
nc -l -p xxx
```

#### access from client
```
# Debian/Ubuntu
sudo apt-get install knockd

# RHEL/CentOS
sudo yum install knock

# Fedora
sudo dnf install knock

macOS:
brew install knock
sudo port install knock

windows
open wsl
#run
bash


telnet server.example.com 7000
telnet server.example.com 8000
telnet server.example.com 9000
run it with admin role in command prompt
dism /online /Enable-Feature /FeatureName:TelnetClient
knock.bat
@echo off
setlocal EnableDelayExpansion

set SERVER=************
set DELAY=1

echo Starting knock sequence for %SERVER%...

for %%p in (7000 8001 9002 9900 7002) do (
    echo Knocking port %%p...
    start /b telnet %SERVER% %%p
    timeout /t %DELAY% /nobreak > nul
)

echo Knock sequence completed.

```
  
#### login way
```
# will be close after 20sec
knock -v -d 1000 ************ 7000 8001 9002 9900 7002
ssh jumpserver
# will forcefully close 
knock -v -d 1000 ************ 7000 8001 9900 7002 9002
```

#### leave last door open
```
# after confirmation several days still we can restart server to disable knockd
sudo systemctl enable knockd

```
#### avoid restore by cloud after restart vm
```
# Install iptables-persistent
sudo apt install iptables-persistent

# Copy your backup to the standard location
sudo cp ./iptable_backup.20250528_aftersetup /etc/iptables/rules.v4

# Or save current rules if they're already loaded
sudo iptables-save > /etc/iptables/rules.v4

# The rules will now automatically restore on boot
```

# DNS create and SSL certification
## DNS creation
route 53 -> create domain -> create host zoned -> add G-IP
dig @******* sbigenai.com ANY

## SSL certification
https://ap-northeast-1.console.aws.amazon.com/acm/home?region=ap-northeast-1#/welcome

ACM -> Request a cerfication -> Request public cert -> enter domain sbigenai.com  
(should be exact same as your domain.com) -> DNS validaton -> Create records in Route 53
you can find even no need to 'create records in route 53' cname record automatically added

![[Pasted image 20241112173402.png]]

## Setup ssl NLB loadbalance for internal service

1. create certificate from *.sbigenai.com and sbigenai.com (before that, we need to create domain in route 53
   and binding elastic public ip to some damain)

2. Create mom-lb-sg sg 
make sure 443 0.0.0.0/0
  also we are using TLS passthrough so you have to make sure
  sg  443 0.0.0.0/0 for application web server as well

3. add xxx.sbigenai.com to dns (type record A)

4. create target group
TLS:8243 and health check using https path '/'


5. create load balance (NLB but not ALB because alb need 2 diff zone)

![[Pasted image 20241112224342.png]]

1. if we need sbigenai.com and www.sbigenai.com access
  then we need to add both certificates to listener
  and change sbigenai.com as default certficates
  ![[Pasted image 20241112234723.png]]
  
  After all done. to verify
  curl -v https://www.sbigenai.com/
  
  ---
  #  SMTP SES Service Setup
  https://claude.ai/chat/de4b200f-6224-43d5-b235-3ec90d5475da
  
  ## endpoinst etc information
  
```
SMTP endpoint
email-smtp.ap-northeast-1.amazonaws.com
STARTTLS Port
25, 587 or 2587

```
  ## credentials
```
SMTP credentials
IAM user name:   ses-smtp-sbigenai
SMTP user name:  AKIAQ4J5YHDENN4JYW74
SMTP password:BLcTGKOkFMbmX6foMo/hUUc9PXJ1/hS+dticwvdhjlOD        
```
 [[🦜🦜🦜MeetingMom Tool]]
 pls refer to smtp server in above link
:```
sudo nano /etc/postfix/main.cf
sender_canonical_maps = static:<EMAIL>
smtp_header_checks = regexp:/etc/postfix/smtp_header_checks
sudo nano /etc/postfix/smtp_header_checks
/^From:.*/ REPLACE From: SBI GenAI Mom System <<EMAIL>>

sudo postmap -r /etc/postfix/smtp_header_checks
sudo chmod 644 /etc/postfix/smtp_header_checks
sudo chmod 644 /etc/postfix/smtp_header_checks.db

sudo systemctl restart postfix
echo "This is a test email" | mail -s "Test Subject" <EMAIL>
```
## open 25 SMTP port in OP security group

## For future MAIL server. MX related setup

```
sbigenai.com MX 10 mail.sbigenai.com
mail.sbigenai.com MX 10 feedback-smtp.ap-northeast-1.amazonses.com
mail.sbigenai.com A ************

sbigenai.com → MX → mail.sbigenai.com → MX → feedback-smtp... → (最终解析到IP) → 投递邮件。
A记录的************不被使用。

总结
为 mail.sbigenai.com 添加A记录在当前配置下对邮件投递几乎没有影响，因为邮件服务器会优先使用MX记录指定的目标（SES）进行邮件投递。A记录仅在无MX记录情况下可能被用来作为邮件递送目标，或在非邮件应用场景下为mail.sbigenai.com提供IP解析。

```
DNS规范要求，当一个域名存在CNAME记录时，该域名不能再有其它类型的记录共同存在。所以上面是唯一办法
sbigenai.com MX 10 mail.sbigenai.com
mail.sbigenai.com MX 10 feedback-smtp.ap-northeast-1.amazonses.com
~~mail.sbigenai.com CNAME opt.sbigenai.com~~
mail.sbigenai.com A ************ 
opt.sbigenai.com A ************

```
这是关于 DNS 记录配置的邮件,主要涉及 MX(邮件交换)和 SPF(发件人框架策略)记录的设置。让我来详细解释:

邮件开头说明他们收到了 MX 记录信息,但会将其调整用于 "sbigroup.ai" 域名:
原始记录是针对 "sbigenai.com" 的:

MX 记录: 优先级为 10,指向 mail.sbigenai.com
SPF 记录: 配置为使用 Amazon SES (Simple Email Service)


他们计划为 "sbigroup.ai" 设置以下新记录:

A 记录: 将 mail.sbigroup.ai 指向 IP 地址 ************** (标注为 AWS 服务器 G-IP)
MX 记录: 优先级为 10,指向 mail.sbigroup.ai
SPF 记录: 与之前相同的配置,允许 Amazon SES 发送邮件



这看起来是一个邮件服务器设置的迁移或复制操作,从 sbigenai.com 迁移到 sbigroup.ai,同时保持使用 Amazon SES 作为邮件发送基础设施。这些配置将允许新域名(sbigroup.ai)正常收发邮件,并通过 SPF 记录维护适当的身份验证。

A 记录: 定义域名对应的 IP 地址
MX 记录: 定义处理该域名邮件的服务器
SPF 记录: 定义哪些邮件服务器被授权发送该域名的邮件,这里特别授权了 Amazon SES 服务

这样的配置可以确保:

邮件能够正确发送和接收
防止邮件被标记为垃圾邮件
提供适当的安全性和身份验证

您需要我详细解释其中的某个具体部分吗?
```

# Use Jumpserver to forward TCP access to UAT severs
We can use LB as well but below is to do route in server but not in aws
```
Jump server TCP package forwrd
# Confirmation method
sudo apt-get install socat

socat -v tcp-l:8243,reuseaddr,fork exec:'/bin/cat'
socat -v tcp-l:8245,reuseaddr,fork exec:'/bin/cat'
socat -v tcp-l:8246,reuseaddr,fork exec:'/bin/cat'

curl -v http://*********:8243
curl -v http://*********:8245
curl -v http://*********:8246

# Enable IP Forwarding in mgmt server
cat /proc/sys/net/ipv4/ip_forward
sudo sysctl -w net.ipv4.ip_forward=1
vi /etc/sysctl.conf
net.ipv4.ip_forward = 1
sudo sysctl -p

# Add iptables Rules for Port Forwarding
-- take backup
sudo iptables-save > /home/<USER>/20241213_before

sudo iptables -t nat -A PREROUTING -p tcp --dport 8243 -j DNAT --to-destination *********:8243
sudo iptables -A FORWARD -p tcp -d ********* --dport 8243 -j ACCEPT
sudo iptables -t nat -A PREROUTING -p tcp --dport 8245 -j DNAT --to-destination *********:8245
sudo iptables -A FORWARD -p tcp -d ********* --dport 8245 -j ACCEPT
sudo iptables -t nat -A PREROUTING -p tcp --dport 8246 -j DNAT --to-destination *********:8246
sudo iptables -A FORWARD -p tcp -d ********* --dport 8246 -j ACCEPT
sudo iptables -t nat -A POSTROUTING -j MASQUERADE

# Test from remote site
curl -v http://************:8243
curl -v http://************:8245
curl -v http://************:8246

# Make iptable change permanently
sudo apt-get install iptables-persistent
sudo iptables-save > /etc/iptables/rules

sudo vi /etc/systemd/system/iptables-restore.service

[Unit]
Description=Restore iptables rules
After=network.target

[Service]
Type=oneshot
ExecStart=/usr/sbin/iptables-restore /etc/iptables/rules
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target

sudo systemctl daemon-reload
sudo systemctl restart iptables-restore.service
sudo systemctl status iptables-restore.service


# To fix iptables -L
sudo: unable to resolve host SBIHD-GenAI-dev-LVM-MGT: Temporary failure in name resolution
Add below to /etc/hosts
*********0 SBIHD-GenAI-dev-LVM-MGT
********* SBIHD-GenAI-uat-LVM-AP01

# To rollback change
sudo iptables-restore /home/<USER>/20241213_before


```

# Firewall setup Trick
To enable UAT port access from PROD And BiDirection way
, you can JUST enable 150 rule and make it allow. It will enable both direction access


![[Pasted image 20241217150215.png]]