---
title: "dusty-nv/jetson-containers: Machine Learning Containers for NVIDIA Jetson and JetPack-L4T"
source: https://github.com/dusty-nv/jetson-containers
author:
  - "[[johnny<PERSON><PERSON><PERSON>]]"
published:
created: 2025-07-06
description: Machine Learning Containers for NVIDIA Jetson and JetPack-L4T - dusty-nv/jetson-containers
tags:
  - AI/Jetson
reference:
  - https://www.dongaigc.com/a/jetson-containers-intelligent-solution
  - https://cloud.tencent.com/developer/article/2354032
---
**[jetson-containers](https://github.com/dusty-nv/jetson-containers)** Public

Machine Learning Containers for NVIDIA Jetson and JetPack-L4T

[MIT license](https://github.com/dusty-nv/jetson-containers/blob/master/LICENSE.md)

[Open in github.dev](https://github.dev/) [Open in a new github.dev tab](https://github.dev/) [Open in codespace](https://github.com/codespaces/new/dusty-nv/jetson-containers?resume=1)

<table><thead><tr><th colspan="2"><span>Name</span></th><th colspan="1"><span>Name</span></th><th><p><span>Last commit message</span></p></th><th colspan="1"><p><span>Last commit date</span></p></th></tr></thead><tbody><tr><td colspan="3"><p><span><a href="https://github.com/dusty-nv/jetson-containers/commit/1ce72525caf97ecfcd59bc71ff53495b3b210935">Merge remote-tracking branch 'origin/dev' into dev</a></span></p><p><span><a href="https://github.com/dusty-nv/jetson-containers/commit/1ce72525caf97ecfcd59bc71ff53495b3b210935">1ce7252</a> ·</span></p><p><a href="https://github.com/dusty-nv/jetson-containers/commits/master/"><span><span><span>4,412 Commits</span></span></span></a></p></td></tr><tr><td colspan="2"><p><a href="https://github.com/dusty-nv/jetson-containers/tree/master/.github">.github</a></p></td><td colspan="1"><p><a href="https://github.com/dusty-nv/jetson-containers/tree/master/.github">.github</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/dusty-nv/jetson-containers/tree/master/data">data</a></p></td><td colspan="1"><p><a href="https://github.com/dusty-nv/jetson-containers/tree/master/data">data</a></p></td><td><p><a href="https://github.com/dusty-nv/jetson-containers/commit/e87f5969fa7453ba8348e6ad12796785f697681b">fix: update CUDA version to 12.9 in configuration files and documenta…</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/dusty-nv/jetson-containers/tree/master/deprecated">deprecated</a></p></td><td colspan="1"><p><a href="https://github.com/dusty-nv/jetson-containers/tree/master/deprecated">deprecated</a></p></td><td><p><a href="https://github.com/dusty-nv/jetson-containers/commit/16039ad9ac52a39cc29771bd4ebb03301fbb2188">fix: update Dockerfile dependencies and README for Jetpack version su…</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/dusty-nv/jetson-containers/tree/master/docs">docs</a></p></td><td colspan="1"><p><a href="https://github.com/dusty-nv/jetson-containers/tree/master/docs">docs</a></p></td><td><p><a href="https://github.com/dusty-nv/jetson-containers/commit/401cb4fcf94438c74afcf6339a104cfb3fbc37b0">Update build.md</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/dusty-nv/jetson-containers/tree/master/jetson_containers">jetson_containers</a></p></td><td colspan="1"><p><a href="https://github.com/dusty-nv/jetson-containers/tree/master/jetson_containers">jetson_containers</a></p></td><td><p><a href="https://github.com/dusty-nv/jetson-containers/commit/17927012be2cdfe31fdd1f31ba0a812d496067a6">exclude <code>-builder</code> packages from <code>Dependants</code> during docs generation</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/dusty-nv/jetson-containers/tree/master/packages">packages</a></p></td><td colspan="1"><p><a href="https://github.com/dusty-nv/jetson-containers/tree/master/packages">packages</a></p></td><td><p><a href="https://github.com/dusty-nv/jetson-containers/commit/1ce72525caf97ecfcd59bc71ff53495b3b210935">Merge remote-tracking branch 'origin/dev' into dev</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/dusty-nv/jetson-containers/tree/master/scripts">scripts</a></p></td><td colspan="1"><p><a href="https://github.com/dusty-nv/jetson-containers/tree/master/scripts">scripts</a></p></td><td><p><a href="https://github.com/dusty-nv/jetson-containers/commit/5645241e8243d4de4062b74c42bd9b760f6c091c">Refactor Jupyter notebooks for improved clarity and structure</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/dusty-nv/jetson-containers/blob/master/.dockerignore">.dockerignore</a></p></td><td colspan="1"><p><a href="https://github.com/dusty-nv/jetson-containers/blob/master/.dockerignore">.dockerignore</a></p></td><td><p><a href="https://github.com/dusty-nv/jetson-containers/commit/757e0251b8aca0216b9fa8edf58e54a16b286f9f">updated.dockerignore</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/dusty-nv/jetson-containers/blob/master/.editorconfig">.editorconfig</a></p></td><td colspan="1"><p><a href="https://github.com/dusty-nv/jetson-containers/blob/master/.editorconfig">.editorconfig</a></p></td><td><p><a href="https://github.com/dusty-nv/jetson-containers/commit/b256ace74f131a42ddaa6f567c5b496080e44c6e">Add code style tools with opt-in formatting system (</a><a href="https://github.com/dusty-nv/jetson-containers/pull/1039">#1039</a><a href="https://github.com/dusty-nv/jetson-containers/commit/b256ace74f131a42ddaa6f567c5b496080e44c6e">)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/dusty-nv/jetson-containers/blob/master/.env">.env</a></p></td><td colspan="1"><p><a href="https://github.com/dusty-nv/jetson-containers/blob/master/.env">.env</a></p></td><td><p><a href="https://github.com/dusty-nv/jetson-containers/commit/e87f5969fa7453ba8348e6ad12796785f697681b">fix: update CUDA version to 12.9 in configuration files and documenta…</a></p></td><td></td></tr><tr><td colspan="3"></td></tr></tbody></table>

[![a header for a software project about building containers for AI and machine learning](https://raw.githubusercontent.com/dusty-nv/jetson-containers/docs/docs/images/header_blueprint_rainbow.jpg)](https://www.jetson-ai-lab.com/)

Modular container build system that provides the latest [**AI/ML packages**](https://pypi.jetson-ai-lab.dev/) for [NVIDIA Jetson](https://jetson-ai-lab.com/) 🚀🤖

## Code Style

The project uses automated code formatting tools to maintain consistent code style. See [Code Style Guide](https://github.com/dusty-nv/jetson-containers/blob/master/docs/code-style.md) for details on:

- Setting up formatting tools
- Adding your package to formatting checks
- Troubleshooting common issues

|  |  |
| --- | --- |
| **ML** | [`pytorch`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/pytorch) [`tensorflow`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/tensorflow) [`jax`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/jax) [`onnxruntime`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/onnxruntime) [`deepstream`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/cv/deepstream) [`holoscan`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/cv/holoscan) [`CTranslate2`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/ctranslate2) [`JupyterLab`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/jupyterlab) |
| **LLM** | [`SGLang`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/llm/sglang) [`vLLM`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/llm/vllm) [`MLC`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/llm/mlc) [`AWQ`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/llm/awq) [`transformers`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/llm/transformers) [`text-generation-webui`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/llm/text-generation-webui) [`ollama`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/llm/ollama) [`llama.cpp`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/llm/llama_cpp) [`llama-factory`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/llm/llama-factory) [`exllama`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/llm/exllama) [`AutoGPTQ`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/llm/auto_gptq) [`FlashAttention`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/attention/flash-attention) [`DeepSpeed`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/llm/deepspeed) [`bitsandbytes`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/llm/bitsandbytes) [`xformers`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/llm/xformers) |
| **VLM** | [`llava`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/vlm/llava) [`llama-vision`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/vlm/llama-vision) [`VILA`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/vlm/vila) [`LITA`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/vlm/lita) [`NanoLLM`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/llm/nano_llm) [`ShapeLLM`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/vlm/shape-llm) [`Prismatic`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/vlm/prismatic) [`xtuner`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/vlm/xtuner) [`gemma_vlm`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/vlm/gemma_vlm) |
| **VIT** | [`NanoOWL`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/vit/nanoowl) [`NanoSAM`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/vit/nanosam) [`Segment Anything (SAM)`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/vit/sam) [`Track Anything (TAM)`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/vit/tam) [`clip_trt`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/vit/clip_trt) |
| **RAG** | [`llama-index`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/rag/llama-index) [`langchain`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/rag/langchain) [`jetson-copilot`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/rag/jetson-copilot) [`NanoDB`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/vectordb/nanodb) [`FAISS`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/vectordb/faiss) [`RAFT`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/rapids/raft) |
| **L4T** | [`l4t-pytorch`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/l4t/l4t-pytorch) [`l4t-tensorflow`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/l4t/l4t-tensorflow) [`l4t-ml`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/l4t/l4t-ml) [`l4t-diffusion`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/l4t/l4t-diffusion) [`l4t-text-generation`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/l4t/l4t-text-generation) |
| **CUDA** | [`cupy`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/numeric/cupy) [`cuda-python`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/cuda/cuda-python) [`pycuda`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/cuda/pycuda) [`cv-cuda`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/cv/cv-cuda) [`opencv:cuda`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/cv/opencv) [`numba`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/numeric/numba) |
| **Robotics** | [`ROS`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/robots/ros) [`LeRobot`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/robots/lerobot) [`OpenVLA`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/vla/openvla) [`3D Diffusion Policy`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/diffusion/3d_diffusion_policy) [`Crossformer`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/diffusion/crossformer) [`MimicGen`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/sim/mimicgen) [`OpenDroneMap`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/robots/opendronemap) [`ZED`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/hardware/zed) [`openpi`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/robots/openpi) |
| **Simulation** | [`Isaac Sim`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/sim/isaac-sim) [`Genesis`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/sim/genesis) [`Habitat Sim`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/sim/habitat-sim) [`MimicGen`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/sim/mimicgen) [`MuJoCo`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/sim/mujoco) [`PhysX`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/sim/physx) [`Protomotions`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/sim/protomotions) [`RoboGen`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/sim/robogen) [`RoboMimic`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/sim/robomimic) [`RoboSuite`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/sim/robosuite) [`Sapien`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/sim/sapien) |
| **Graphics** | [`3D Diffusion Policy`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/diffusion/3d_diffusion_policy) [`AI Toolkit`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/diffusion/ai-toolkit) [`ComfyUI`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/diffusion/comfyui) [`Cosmos`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/diffusion/cosmos) [`Diffusers`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/diffusion/diffusers) [`Diffusion Policy`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/diffusion/diffusion_policy) [`FramePack`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/diffusion/framepack) [`Small Stable Diffusion`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/diffusion/small-stable-diffusion) [`Stable Diffusion`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/diffusion/stable-diffusion) [`Stable Diffusion WebUI`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/diffusion/stable-diffusion-webui) [`SD.Next`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/diffusion/sdnext) [`nerfstudio`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/nerf/nerfstudio) [`meshlab`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/nerf/meshlab) [`gsplat`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/nerf/gsplat) |
| **Mamba** | [`mamba`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/mamba) [`mambavision`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/mamba/mambavision) [`cobra`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/mamba/cobra) [`dimba`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/mamba/dimba) [`videomambasuite`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/mamba/videomambasuite) |
| **KANs** | [`pykan`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/kans/pykan) [`kat`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/kans/kat) |
| **xLTSM** | [`xltsm`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/xltsm/xltsm) [`mlstm_kernels`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/ml/xltsm/mlstm_kernels) |
| **Speech** | [`whisper`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/speech/whisper) [`whisper_trt`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/speech/whisper_trt) [`piper`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/speech/piper-tts) [`riva`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/speech/riva-client) [`audiocraft`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/speech/audiocraft) [`voicecraft`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/speech/voicecraft) [`xtts`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/speech/xtts) |
| **Home/IoT** | [`homeassistant-core`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/smart-home/homeassistant-core) [`wyoming-whisper`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/smart-home/wyoming/wyoming-whisper) [`wyoming-openwakeword`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/smart-home/wyoming/openwakeword) [`wyoming-piper`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/smart-home/wyoming/piper) |
| **3DPrintObjects** | [`PartPacker`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/objects/partpacker) [`Sparc3D`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/objects/sparc3d) |

See the [**`packages`**](https://github.com/dusty-nv/jetson-containers/blob/master/packages) directory for the full list, including pre-built container images for JetPack/L4T.

Using the included tools, you can easily combine packages together for building your own containers. Want to run ROS2 with PyTorch and Transformers? No problem - just do the [system setup](https://github.com/dusty-nv/jetson-containers/blob/master/docs/setup.md), and build it on your Jetson:

```
$ jetson-containers build --name=my_container pytorch transformers ros:humble-desktop
```

There are shortcuts for running containers too - this will pull or build a [`l4t-pytorch`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/l4t/l4t-pytorch) image that's compatible:

```
$ jetson-containers run $(autotag l4t-pytorch)
```

> <sup><a href="https://github.com/dusty-nv/jetson-containers/blob/master/docs/run.md"><code>jetson-containers run</code></a> launches <a href="https://docs.docker.com/engine/reference/commandline/run/"><code>docker run</code></a> with some added defaults (like <code>--runtime nvidia</code>, mounted <code>/data</code> cache and devices)</sup>  
> <sup><a href="https://github.com/dusty-nv/jetson-containers/blob/master/docs/run.md#autotag"><code>autotag</code></a> finds a container image that's compatible with your version of JetPack/L4T - either locally, pulled from a registry, or by building it.</sup>

If you look at any package's readme (like [`l4t-pytorch`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/l4t/l4t-pytorch)), it will have detailed instructions for running it.

You can rebuild the container stack for different versions of CUDA by setting the `CUDA_VERSION` variable:

```
CUDA_VERSION=12.6 jetson-containers build transformers
```

It will then go off and either pull or build all the dependencies needed, including PyTorch and other packages that would be time-consuming to compile. There is a [Pip server](https://github.com/dusty-nv/jetson-containers/blob/master/docs/build.md#pip-server) that caches the wheels to accelerate builds. You can also request specific versions of cuDNN, TensorRT, Python, and PyTorch with similar environment variables like [here](https://github.com/dusty-nv/jetson-containers/blob/master/docs/build.md#changing-versions).

## Documentation

[![](https://camo.githubusercontent.com/1e7863d6b72f039759c2f68ca10981e3ec65bfc8f8a12e2fde9c86f5b6398698/68747470733a2f2f6e76696469612d61692d696f742e6769746875622e696f2f6a6574736f6e2d67656e657261746976652d61692d706c617967726f756e642f696d616765732f4a4f4e5f47656e2d41492d70616e656c732e706e67)](https://www.jetson-ai-lab.com/)

- [Package List](https://github.com/dusty-nv/jetson-containers/blob/master/packages)
- [Package Definitions](https://github.com/dusty-nv/jetson-containers/blob/master/docs/packages.md)
- [System Setup](https://github.com/dusty-nv/jetson-containers/blob/master/docs/setup.md)
- [Building Containers](https://github.com/dusty-nv/jetson-containers/blob/master/docs/build.md)
- [Running Containers](https://github.com/dusty-nv/jetson-containers/blob/master/docs/run.md)

Check out the tutorials at the [**Jetson Generative AI Lab**](https://www.jetson-ai-lab.com/)!

## Getting Started

Refer to the [System Setup](https://github.com/dusty-nv/jetson-containers/blob/master/docs/setup.md) page for tips about setting up your Docker daemon and memory/storage tuning.

```
# install the container tools
git clone https://github.com/dusty-nv/jetson-containers
bash jetson-containers/install.sh

# automatically pull & run any container
jetson-containers run $(autotag l4t-pytorch)
```

Or you can manually run a [container image](https://hub.docker.com/r/dustynv) of your choice without using the helper scripts above:

```
sudo docker run --runtime nvidia -it --rm --network=host dustynv/l4t-pytorch:r36.2.0
```

Looking for the old jetson-containers? See the [`legacy`](https://github.com/dusty-nv/jetson-containers/tree/legacy) branch.

## Gallery

> [Multimodal Voice Chat with LLaVA-1.5 13B on NVIDIA Jetson AGX Orin](https://www.youtube.com/watch?v=9ObzbbBTbcc) (container: [`NanoLLM`](https://dusty-nv.github.io/NanoLLM/))

  

[![](https://raw.githubusercontent.com/dusty-nv/jetson-containers/docs/docs/images/llamaspeak_70b_yt.jpg)](https://www.youtube.com/watch?v=hswNSZTvEFE)

> [Interactive Voice Chat with Llama-2-70B on NVIDIA Jetson AGX Orin](https://www.youtube.com/watch?v=wzLHAgDxMjQ) (container: [`NanoLLM`](https://dusty-nv.github.io/NanoLLM/))

  

[![](https://raw.githubusercontent.com/dusty-nv/jetson-containers/docs/docs/images/nanodb_tennis.jpg)](https://www.youtube.com/watch?v=OJT-Ax0CkhU)

> [Realtime Multimodal VectorDB on NVIDIA Jetson](https://www.youtube.com/watch?v=wzLHAgDxMjQ) (container: [`nanodb`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/vectordb/nanodb))

  

> [NanoOWL - Open Vocabulary Object Detection ViT](https://www.jetson-ai-lab.com/tutorial_nanoowl.html) (container: [`nanoowl`](https://github.com/dusty-nv/jetson-containers/blob/master/packages/vit/nanoowl))

> [Live Llava on Jetson AGX Orin](https://youtu.be/X-OXxPiUTuU) (container: [`NanoLLM`](https://dusty-nv.github.io/NanoLLM/))

[![](https://raw.githubusercontent.com/dusty-nv/jetson-containers/docs/docs/images/live_llava_bear.jpg)](https://www.youtube.com/watch?v=wZq7ynbgRoE)

> [Live Llava 2.0 - VILA + Multimodal NanoDB on Jetson Orin](https://youtu.be/X-OXxPiUTuU) (container: [`NanoLLM`](https://dusty-nv.github.io/NanoLLM/))

> [Small Language Models (SLM) on Jetson Orin Nano](https://www.jetson-ai-lab.com/tutorial_slm.html) (container: [`NanoLLM`](https://dusty-nv.github.io/NanoLLM/))

> [Realtime Video Vision/Language Model with VILA1.5-3b](https://www.jetson-ai-lab.com/tutorial_nano-vlm.html#video-sequences) (container: [`NanoLLM`](https://dusty-nv.github.io/NanoLLM/))

## Releases

No releases published

## Packages

No packages published