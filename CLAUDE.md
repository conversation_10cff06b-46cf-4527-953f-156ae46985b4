# CLAUDE.md - NotionAI Project Configuration

## Legend
| Symbol | Meaning | | Abbrev | Meaning |
|--------|---------|---|--------|---------|
| → | leads to | | md | markdown |
| > | greater than | | pdf | portable document format |
| & | and/with | | ai | artificial intelligence |

## Project Overview

**Type**: Python utility project with AI documentation repository  
**Purpose**: Markdown to PDF conversion + AI knowledge management  
**Stack**: Python 3.11+ + Pandoc + wkhtmltopdf + UV package manager  

```yaml
Core Function: Markdown→PDF conversion utility
Knowledge Base: 1000+ AI research papers & tutorials in /AI/
Tools: Pandoc conversion engine w/ fallback strategies
Distribution: Single-file utility w/ CLI & module interfaces
```

## Architecture & Structure

```yaml
Root Files:
  - markdown_to_pdf.py: Core conversion utility
  - pyproject.toml: UV package configuration  
  - README.md: Usage documentation
  
AI/: Knowledge repository
  - Research papers (ArXiv format)
  - YouTube tutorial transcripts
  - GitHub project documentation
  - AI/ML implementation guides
  
copilot-conversations/: GitHub Copilot chat history
```

## Development Standards

### Code Quality [H:8]
```yaml
Style: Clean, documented functions | Error handling required
Naming: Descriptive function names | Path variables explicit
Dependencies: Minimal (termcolor only) | Standard library preferred
Testing: Manual CLI testing | Path validation critical
```

### File Operations [C:9]
```yaml
Security: Path validation required | No arbitrary file execution
Error Handling: Graceful fallback HTML→PDF | User feedback via colored output  
Path Management: Cross-platform paths | Default ~/.search_server output
Validation: File existence checks | Extension validation
```

### Conversion Process [H:7]
```yaml
Primary: Pandoc w/ wkhtmltopdf engine
Fallback: HTML conversion if PDF fails  
Output: Configurable filename & directory
Feedback: Colored terminal output for status
```

## Dependencies & Tools

```yaml
Runtime:
  - Python ≥3.11 (pyproject.toml requirement)
  - termcolor ≥2.5.0 (colorized output)
  
External Tools:
  - Pandoc (conversion engine) - REQUIRED
  - wkhtmltopdf (PDF rendering) - REQUIRED
  
Package Manager: UV (preferred) | pip (fallback)
Install: uv sync || pip install termcolor>=2.5.0
Verify: python -c "import termcolor; print('OK')"
```

## Usage Patterns

### CLI Usage
```bash
python markdown_to_pdf.py <input.md>              # Basic conversion
python markdown_to_pdf.py AI/paper.md             # Convert AI docs
```

### Module Usage  
```python
from markdown_to_pdf import markdown_to_pdf

# Basic conversion
pdf_path = markdown_to_pdf("input.md")

# Custom output
pdf_path = markdown_to_pdf(
    "input.md", 
    output_filename="custom.pdf",
    output_dir="/custom/path"
)
```

## AI Knowledge Base

### Content Categories [Research Repository]
```yaml
Papers: ArXiv papers w/ implementation details
Tutorials: YouTube transcripts & step-by-step guides
Frameworks: LangChain, Transformers, PyTorch guides  
Models: GPT, LLaMA, Mistral implementation notes
Applications: RAG, fine-tuning, deployment strategies

Navigation Tips:
  - AI.md: Master index (4.3MB) - use Read tool with offset/limit
  - Search: Use Grep tool with patterns like "GPT-4\|LLaMA\|RAG"
  - Count: 1000+ files, mostly research & tutorial content
  - Convert: python markdown_to_pdf.py "AI/(specific-file).md"
```

### File Naming Convention
```yaml
Format: (Source) Title - Creator.md
YouTube: (123) Title - YouTube - Creator.md  
Papers: YYYY.NNNNN Title.md (ArXiv format)
GitHub: ProjectName-RepoName- Description.md
Blogs: Title - Publisher.md
```

## Development Workflow

### Adding Features [M:6]
```yaml
1. Test w/ sample markdown files from AI/
2. Validate cross-platform path handling  
3. Ensure error messages are user-friendly
4. Update README.md examples if API changes
```

### Documentation Management [L:3]
```yaml
AI/: Read-only knowledge repository
New docs: Add to AI/ w/ proper naming convention
Organize: By topic/framework when collection grows
Index: Consider AI.md as master index
```

## Common Workflows

### Research to PDF [H:7]
```bash
# Find relevant AI documents
grep -r "LangChain" AI/ | head -5

# Convert specific research to PDF
python markdown_to_pdf.py "AI/LangChain Expression Language - 03 Runnable路由.md"

# Batch convert multiple files (manual)
for file in AI/*LangChain*.md; do python markdown_to_pdf.py "$file"; done
```

### Development Workflow [M:6] 
```bash
# Setup environment
uv sync                           # Install dependencies
pandoc --version && wkhtmltopdf --version  # Verify tools

# Test conversion
python markdown_to_pdf.py README.md      # Simple test
ls ~/.search_server/                      # Check output

# Module testing
python -c "from markdown_to_pdf import markdown_to_pdf; print('Import OK')"
```

### Testing Strategy [H:7]
```yaml
Manual Testing Required:
  - Various markdown formats from AI/ collection
  - Cross-platform path handling (Windows WSL context)
  - Pandoc error scenarios (missing tools)
  - Large file handling (AI docs can be substantial)

Test Commands:
  - python markdown_to_pdf.py AI/README.md  # Basic test
  - python markdown_to_pdf.py nonexistent.md  # Error handling
  - python -c "from markdown_to_pdf import markdown_to_pdf; print(markdown_to_pdf('AI/README.md'))"  # Module test
  
Verify Setup:
  - pandoc --version  # Must show version
  - wkhtmltopdf --version  # Must show version
  - ls ~/.search_server/  # Check default output dir
```

## Error Handling & Recovery [H:8]

```yaml
File Not Found: Clear error message w/ file path
Pandoc Missing: Installation instructions to user
PDF Engine Fail: Automatic fallback to HTML conversion
Permission Issues: Directory creation w/ proper error reporting
Large Files: No size limits but memory-aware processing
```

## Integration Notes

### Environment Compatibility
```yaml
Platform: Cross-platform design (Windows WSL detected)
Python: Modern Python 3.11+ features allowed
Paths: Pathlib used for cross-platform compatibility
Output: Colored terminal output w/ termcolor

WSL/Windows Notes:
  - Default output: ~/.search_server (Linux home in WSL)
  - Windows paths: Use forward slashes or escape backslashes
  - Pandoc: Install in WSL environment, not Windows
  - Access: Files accessible from Windows via /mnt/c/...
```

### External Tool Dependencies [C:9]
```yaml
Pandoc: CRITICAL - conversion engine
  - Windows: Manual download required
  - WSL/Linux: apt-get install pandoc
  - Verify: pandoc --version

wkhtmltopdf: PRIMARY PDF engine  
  - Windows: Manual download required
  - WSL/Linux: apt-get install wkhtmltopdf
  - Fallback: HTML if unavailable
```

## Future Considerations

### Scalability [M:5]
```yaml
Batch Processing: Multiple file conversion
AI Integration: LLM-powered markdown preprocessing  
Output Formats: EPUB, DOCX support via Pandoc
Configuration: Config file for default settings
```

### Knowledge Base Enhancement [L:3]
```yaml
Search: Full-text search across AI/ directory
Tagging: Metadata extraction from filenames
Organization: Topic-based subdirectories  
Integration: Link to conversion utility for research→PDF workflows
```

---
*NotionAI v0.1.0 | Python markdown conversion + AI knowledge repository | Focus: Utility + Research*