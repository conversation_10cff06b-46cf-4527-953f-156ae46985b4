---
title: "[Question] Use cases · Issue #98 · NomenAK/SuperClaude"
source: https://github.com/NomenAK/SuperClaude/issues/98
author:
  - "[[sunkeunchoi]]"
published: 2025-07-12
created: 2025-07-12
description: Question Type Command Usage Your Question I am new to SuperClaude so I tried example use cases using llm. Below attached document may not accurate but cannot review my self wether below are correct way to use SuperClaude or not. SuperCla...
tags:
  - AI/Claude
reference:
---
### Question Type

Command Usage

### Your Question

## I am new to SuperClaude so I tried example use cases using llm. Below attached document may not accurate but cannot review my self wether below are correct way to use SuperClaude or not.

## SuperClaude Use Cases - Complete Workflow Guide

## 🔄 Scenario 1: Porting to Another Language

**Goal:** Migrate a project from one language (e.g., Python) to another (e.g., Go)

```
/load project --type project
↓
/analyze src --focus architecture --depth deep
↓
/explain core-patterns --level intermediate --format examples
↓
/design src --type architecture --format diagram
↓
/spawn "port modules to Go" --sequential --validate
↓
/build . --type dev --clean
↓
/test . --type all --coverage
↓
/troubleshoot compatibility-issues --type build --trace
↓
/improve . --type performance --safe
↓
/git commit --smart-commit
↓
/document . --type guide --style detailed
```

---

## 🚀 Scenario 2: Implementing a New Feature

**Goal:** Add a feature, e.g., "real-time chat"

```
/design chat-feature --type component --format diagram
↓
/estimate chat-feature --type time --unit days
↓
/task add-chat chat-feature --strategy agile --persist
↓
/build . --type dev
↓
/test chat-module --type unit
↓
/improve chat-module --type quality --safe
↓
/git commit --smart-commit
↓
/document chat-module --type api --style brief
```

---

## 🐛 Scenario 3: Resolving Git Issues

**Goal:** Fix a broken branch with messy history and conflicts

```
/load . --type project
↓
/git status --branch-strategy
↓
/troubleshoot merge-conflict --type bug --trace
↓
/git rebase dev --smart-commit
↓
/cleanup . --type files --safe
↓
/test . --type unit --coverage
↓
/git commit --smart-commit
↓
/document . --type guide --style brief
```

---

## 🛠️ Scenario 4: Refactoring Legacy Code

**Goal:** Improve maintainability and structure of an old module

```
/load legacy-module --type project
↓
/analyze legacy-module --focus quality --depth deep
↓
/explain legacy-patterns --level advanced --format examples
↓
/design refactored-structure --type architecture --format spec
↓
/improve legacy-module --type maintainability --safe
↓
/cleanup legacy-module --type all --aggressive
↓
/test legacy-module --type unit --watch
↓
/git commit --smart-commit
↓
/document legacy-module --type inline --style detailed
```

---

## ⚙️ Scenario 5: CI/CD Pipeline Setup

**Goal:** Automate builds and tests in a CI/CD environment

```
/load . --type config
↓
/design pipeline --type architecture --format diagram
↓
/build . --type prod --optimize
↓
/test . --type all --coverage
↓
/troubleshoot build-failures --type build --trace
↓
/task setup-ci . --strategy enterprise --persist
↓
/git commit --smart-commit
↓
/document ci-pipeline --type external --style detailed
```

---

## 🔐 Scenario 6: Security Audit and Hardening

**Goal:** Ensure the codebase is secure and compliant

```
/load . --type project
↓
/analyze . --focus security --depth deep
↓
/troubleshoot vulnerabilities --type bug --trace
↓
/improve . --type quality --safe
↓
/test . --type all --coverage
↓
/cleanup . --type code --safe
↓
/git commit --smart-commit
↓
/document . --type guide --style detailed
```

---

## 🌍 Scenario 7: Internationalization (i18n) Implementation

**Goal:** Add multi-language support to an app

```
/load . --type project
↓
/design i18n-system --type component --format diagram
↓
/estimate i18n-system --type effort --unit days
↓
/task implement-i18n i18n-system --strategy agile --persist
↓
/build . --type dev
↓
/test . --type integration
↓
/improve . --type maintainability --safe
↓
/git commit --smart-commit
↓
/document i18n-system --type guide --style detailed
```

---

## 📱 Scenario 8: Porting Web App to Mobile App

**Goal:** Turn a React web app into a React Native mobile app

```
/load web-app --type project
↓
/analyze web-app --focus architecture --depth deep
↓
/explain react-native-differences --level intermediate --format examples
↓
/design mobile-version --type architecture --format diagram
↓
/spawn "create shared components" --parallel --validate
↓
/build mobile-app --type dev
↓
/test mobile-app --type e2e
↓
/troubleshoot platform-issues --type deployment --trace
↓
/git commit --smart-commit
↓
/document mobile-app --type external --style brief
```

---

## 🧪 Scenario 9: Improve Test Coverage

**Goal:** Identify gaps and increase unit + integration test coverage

```
/load . --type project
↓
/analyze . --focus quality --depth deep
↓
/test . --type all --coverage
↓
/task increase-tests . --strategy systematic --persist --hierarchy
↓
/improve . --type maintainability --safe
↓
/cleanup . --type code --safe
↓
/git commit --smart-commit
↓
/document tests --type guide --style brief
```

---

## 📊 Scenario 10: Adding a Dashboard to Admin Panel

**Goal:** Build and integrate a new analytics dashboard

```
/design dashboard --type component --format diagram
↓
/estimate dashboard --type time --unit days
↓
/task implement-dashboard dashboard --strategy agile --persist
↓
/build . --type dev --optimize
↓
/test dashboard --type integration
↓
/improve dashboard --type performance --safe
↓
/git commit --smart-commit
↓
/document dashboard --type api --style detailed
```

---

## 🗃️ Scenario 11: Database Migration

**Goal:** Move from MongoDB to PostgreSQL

```
/load . --type project
↓
/analyze db-layer --focus architecture --depth deep
↓
/explain migration-strategy --level advanced --format examples
↓
/design db-migration --type database --format diagram
↓
/spawn "migrate schemas and data" --sequential --validate
↓
/build . --type dev
↓
/test db-layer --type integration
↓
/troubleshoot data-consistency --type bug --trace
↓
/git commit --smart-commit
↓
/document db-migration --type guide --style detailed
```

---

## 🔁 Scenario 12: Automate Dependency Updates

**Goal:** Set up automated dependency checks and updates

```
/load . --type deps
↓
/analyze deps --focus quality --depth quick
↓
/task setup-dep-monitoring --strategy systematic --persist
↓
/improve deps --type maintainability --safe
↓
/build . --type dev --clean
↓
/test . --type unit
↓
/git commit --smart-commit
↓
/document deps --type readme --style brief
```

---

## ⚙️ Scenario 13: Modularizing a Monolithic App

**Goal:** Break monolith into independent modules/services

```
/load monolith --type project
↓
/analyze monolith --focus architecture --depth deep
↓
/explain service-boundaries --level advanced --format diagram
↓
/design modules --type architecture --format diagram
↓
/spawn "separate services" --sequential --validate
↓
/build modules --type dev --clean
↓
/test modules --type all
↓
/troubleshoot service-communication --type deployment --trace
↓
/git commit --smart-commit
↓
/index modules --type structure --format yaml
```

---

## 🚨 Scenario 14: Emergency Hotfix

**Goal:** Quickly resolve a critical production bug

```
/load . --type project
↓
/troubleshoot critical-bug --type bug --trace
↓
/git checkout -b hotfix/critical-fix --branch-strategy
↓
/analyze affected-code --focus quality --depth quick
↓
/improve . --type quality --safe
↓
/test . --type unit --coverage
↓
/build . --type prod --optimize
↓
/git commit --smart-commit
↓
/document hotfix --type guide --style brief
```

---

## 📝 Scenario 15: Code Review Preparation

**Goal:** Prepare codebase for comprehensive code review

```
/load . --type project
↓
/analyze . --focus quality --depth deep
↓
/explain complex-logic --level intermediate --format examples
↓
/cleanup . --type code --safe
↓
/improve . --type style --safe
↓
/test . --type all --coverage
↓
/document . --type inline --style detailed
↓
/git commit --smart-commit
↓
/index . --type docs --format md
```

---

## 🔍 Scenario 16: Performance Optimization

**Goal:** Identify and resolve performance bottlenecks

```
/load . --type project
↓
/analyze . --focus performance --depth deep
↓
/troubleshoot performance-issues --type performance --trace
↓
/explain optimization-strategies --level advanced --format examples
↓
/improve . --type performance --safe
↓
/build . --type prod --optimize
↓
/test . --type all --coverage
↓
/git commit --smart-commit
↓
/document performance-changes --type guide --style detailed
```

---

## 📦 Scenario 17: Package/Library Development

**Goal:** Create and publish a reusable library or package

```
/design library-api --type api --format spec
↓
/estimate library-development --type time --unit weeks
↓
/task create-library . --strategy systematic --persist
↓
/build . --type prod --optimize
↓
/test . --type all --coverage
↓
/cleanup . --type all --safe
↓
/improve . --type quality --safe
↓
/git commit --smart-commit
↓
/document . --type external --style detailed
↓
/index . --type api --format json
```

---

## 🔄 Scenario 18: API Versioning and Migration

**Goal:** Implement API versioning and migrate clients

```
/load api --type project
↓
/analyze api --focus architecture --depth deep
↓
/design api-v2 --type api --format spec
↓
/estimate migration-effort --type effort --unit days
↓
/task implement-versioning . --strategy enterprise --persist
↓
/build . --type dev
↓
/test api-endpoints --type integration
↓
/troubleshoot backward-compatibility --type bug --trace
↓
/git commit --smart-commit
↓
/document api-migration --type guide --style detailed
```

---

## Command Usage Summary

| Command | Primary Use Cases | Frequency |
| --- | --- | --- |
| `/load` | Project initialization | All scenarios |
| `/analyze` | Code assessment | 16/18 scenarios |
| `/git` | Version control | 15/18 scenarios |
| `/test` | Quality assurance | 17/18 scenarios |
| `/document` | Documentation | 17/18 scenarios |
| `/build` | Compilation/packaging | 15/18 scenarios |
| `/improve` | Code enhancement | 15/18 scenarios |
| `/troubleshoot` | Issue resolution | 10/18 scenarios |
| `/design` | Architecture planning | 10/18 scenarios |
| `/explain` | Code clarification | 8/18 scenarios |
| `/task` | Complex workflows | 8/18 scenarios |
| `/spawn` | Task orchestration | 6/18 scenarios |
| `/cleanup` | Code maintenance | 6/18 scenarios |
| `/estimate` | Planning | 5/18 scenarios |
| `/index` | Documentation organization | 3/18 scenarios |

## Workflow Patterns

### Standard Development Flow

1. **Load** → **Analyze** → **Design** → **Build** → **Test** → **Commit** → **Document**

### Troubleshooting Flow

1. **Load** → **Troubleshoot** → **Analyze** → **Improve** → **Test** → **Commit**

### Refactoring Flow

1. **Load** → **Analyze** → **Explain** → **Design** → **Improve** → **Test** → **Document**

### Complex Feature Flow

1. **Design** → **Estimate** → **Task** → **Spawn** → **Build** → **Test** → **Improve** → **Document**

### Context

*No response*

### Current Configuration

*No response*

### What Have You Tried?

*No response*

### Checklist

- I have read the README.md
	I have searched existing issues and discussions
	This is not a bug report (use Bug Report template instead)