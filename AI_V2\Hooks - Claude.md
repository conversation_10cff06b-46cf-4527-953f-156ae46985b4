---
title: Hooks
source: https://docs.anthropic.com/en/docs/claude-code/hooks
author:
  - "[[Anthropic]]"
published:
created: 2025-07-02
description: Customize and extend Claude Code's behavior by registering shell commands
tags:
  - AI/Claude
reference:
---
# Memo

## notify-send

```
vi ~/.claude/settings.json

{
  "hooks": {
    "Notification": [
      {
        "matcher": "",
        "hooks": [
          {
            "type": "command",
            "command": "python3 ~/notifyme.sh"
          }
        ]
      }
    ],
  "PreToolUse": [
    {
      "matcher": "Bash",
      "hooks": [
        {
          "type": "command",
          "command": "echo \"[$(date '+%Y-%m-%d %H:%M:%S')] $(<&0 jq -r '.tool_input.command') - $(<&0 jq -r '.tool_input.description // \"No description\"')\" >> ~/.claude/bash-command-log.txt"
        }
      ]
    }
   ]
  }
}




(1) install notification package
sudo apt install libnotify-bin notification-daemon

(2) as need to start notification daemon 
/usr/lib/notification-daemon/notification-daemon &

-- add into .bashrc
# Auto-start notification daemon for SSH with X forwarding
if [ -n "$SSH_CONNECTION" ] && [ -n "$DISPLAY" ]; then
    if ! pgrep -x "notification-daemon" > /dev/null; then
        (/usr/lib/notification-daemon/notification-daemon &)
   fi
fi

(3)notification command
#!/usr/bin/env python3
"""
Claude Code Notification Script for Ubuntu
Shows desktop notification and plays sound when Claude Code needs input
"""

import subprocess
import os
import sys

def send_notification():
    """Send desktop notification and play sound"""
    
    # Notification message
    title = "Claude Code"
    message = "Claude Code is waiting for your input"
    icon = "dialog-information"  # You can change to other icons
    urgency = "critical"  # Makes it stay on screen longer
    normal = "normal"
    
    # Common Ubuntu notification sounds (pick one that exists on your system)
    sound_files = [
        "/usr/share/sounds/Yaru/stereo/system-ready.oga"
    ]
    
    # Find first available sound file
    sound_file = None
    for sound in sound_files:
        if os.path.exists(sound):
            sound_file = sound
            break
    
    try:
        # Play sound first (non-blocking)
        if sound_file:
            # Try different sound players in order of preference
            sound_commands = [
                ["paplay", sound_file],  # PulseAudio
                ["canberra-gtk-play", "-f", sound_file],  # Canberra
                ["aplay", sound_file],  # ALSA
                ["play", sound_file]  # Sox
            ]
            
            for cmd in sound_commands:
                try:
                    subprocess.Popen(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                    break
                except FileNotFoundError:
                    continue
        
        # Send desktop notification
        subprocess.run([
            "notify-send",
            "-u", normal,
            "-i", icon,
            "-t", "10000",  # Display for 10 seconds
            title,
            message
        ], check=True)
        
        # Optional: Also play system bell as fallback
        print('\a', end='', flush=True)
        
    except subprocess.CalledProcessError as e:
        print(f"Error sending notification: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    send_notification()



```
## Line notification

vi .bashrc
```
export LINE_CHANNEL_ACCESS_TOKEN=x4wlJpWAgQ8jLqKFqWPflxCXksJL29kTWppgTC2C/U0D59WoCjX0EYIZBQyP4rJnEn/PQQvW7yMNfErQosnqWc4OmhtyYOBhrtKD4m/90TP1VZMgE3I/MoCwWPXayGyQqy+Ve4qxadqR71hmnEUHGQdB04t89/1O/w1cDnyilFU=
export LINE_USER_ID=U703acf75e34221cab1760fef9e92ba74
```

vi ~/.claude/settings.json
```
{
  "hooks": {
    "Notification": [
      {
        "matcher": "",
        "hooks": [
          {
            "type": "command",
            "command": "~/line_notifyme.sh"
          }
        ]
      }
    ],
  "PreToolUse": [
    {
      "matcher": "Bash",
      "hooks": [
        {
          "type": "command",
          "command": "echo \"[$(date '+%Y-%m-%d %H:%M:%S')] $(<&0 jq -r '.tool_input.command') - $(<&0 jq -r '.tool_input.description // \"No description\"')\" >> ~/.claude/bash-command-log.txt"
        }
      ]
    }
   ]
  }
}
```

 vi ~/line_notifyme.sh
```
#!/usr/bin/env python3
"""
Claude Code LINE Notification Script
Sends push notifications to LINE when Claude Code needs input
"""

import os
import sys
import json
import requests
from datetime import datetime

# LINE API Configuration
# You can set these as environment variables or hardcode them
CHANNEL_ACCESS_TOKEN = os.environ.get('LINE_CHANNEL_ACCESS_TOKEN', 'YOUR_CHANNEL_ACCESS_TOKEN_HERE')
DESTINATION_USER_ID = os.environ.get('LINE_USER_ID', 'YOUR_DESTINATION_USER_ID_HERE')

# LINE Messaging API endpoint
LINE_API_ENDPOINT = 'https://api.line.me/v2/bot/message/push'

def send_line_notification():
    """Send notification to LINE"""
    
    # Check if credentials are set
    if CHANNEL_ACCESS_TOKEN == 'YOUR_CHANNEL_ACCESS_TOKEN_HERE' or not CHANNEL_ACCESS_TOKEN:
        print("Error: LINE_CHANNEL_ACCESS_TOKEN not set!")
        print("Set it as environment variable or edit this script")
        return False
    
    if DESTINATION_USER_ID == 'YOUR_DESTINATION_USER_ID_HERE' or not DESTINATION_USER_ID:
        print("Error: LINE_USER_ID not set!")
        print("Set it as environment variable or edit this script")
        return False
    
    # Create message with emoji and formatting
    timestamp = datetime.now().strftime("%H:%M:%S")
    
    # Simple text message
    text_message = {
        "type": "text",
        "text": f"🔔 Claude Code Alert!\n\n⏰ Time: {timestamp}\n📢 Claude Code is waiting for your input!\n\n💡 Please check your SSH session."
    }
    
    # Alternative: Flex message (more visual)
    flex_message = {
        "type": "flex",
        "altText": "Claude Code needs your input!",
        "contents": {
            "type": "bubble",
            "hero": {
                "type": "box",
                "layout": "vertical",
                "contents": [
                    {
                        "type": "text",
                        "text": "🔔 CLAUDE CODE",
                        "size": "xl",
                        "weight": "bold",
                        "color": "#FF6B6B",
                        "align": "center"
                    }
                ],
                "backgroundColor": "#FFE5E5",
                "paddingAll": "20px"
            },
            "body": {
                "type": "box",
                "layout": "vertical",
                "contents": [
                    {
                        "type": "text",
                        "text": "Waiting for your input!",
                        "size": "lg",
                        "weight": "bold",
                        "wrap": True
                    },
                    {
                        "type": "separator",
                        "margin": "md"
                    },
                    {
                        "type": "box",
                        "layout": "horizontal",
                        "contents": [
                            {
                                "type": "text",
                                "text": "⏰ Time:",
                                "flex": 0,
                                "color": "#666666"
                            },
                            {
                                "type": "text",
                                "text": timestamp,
                                "flex": 1,
                                "align": "end"
                            }
                        ],
                        "margin": "md"
                    },
                    {
                        "type": "box",
                        "layout": "horizontal",
                        "contents": [
                            {
                                "type": "text",
                                "text": "💻 Action:",
                                "flex": 0,
                                "color": "#666666"
                            },
                            {
                                "type": "text",
                                "text": "Check SSH session",
                                "flex": 1,
                                "align": "end",
                                "color": "#FF6B6B"
                            }
                        ],
                        "margin": "sm"
                    }
                ],
                "spacing": "sm"
            }
        }
    }
    
    # Prepare request
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {CHANNEL_ACCESS_TOKEN}'
    }
    
    # You can choose between simple text or flex message
    # For SSH notifications, simple text is usually sufficient
    data = {
        'to': DESTINATION_USER_ID,
        'messages': [text_message]  # Change to [flex_message] for fancy version
    }
    
    try:
        # Send the notification
        response = requests.post(
            LINE_API_ENDPOINT,
            headers=headers,
            data=json.dumps(data),
            timeout=10
        )
        
        if response.status_code == 200:
            print(f"✅ LINE notification sent successfully at {timestamp}")
            return True
        else:
            print(f"❌ Failed to send LINE notification: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error sending LINE notification: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Main function"""
    success = send_line_notification()
    
    # Also send terminal bell as fallback
    if not success:
        print('\a', end='', flush=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
```
```


```



---
# Introduction

Claude Code hooks are user-defined shell commands that execute at various points in Claude Code’s lifecycle. Hooks provide deterministic control over Claude Code’s behavior, ensuring certain actions always happen rather than relying on the LLM to choose to run them.

Example use cases include:

- **Notifications**: Customize how you get notified when Claude Code is awaiting your input or permission to run something.
- **Automatic formatting**: Run `prettier` on.ts files, `gofmt` on.go files, etc. after every file edit.
- **Logging**: Track and count all executed commands for compliance or debugging.
- **Feedback**: Provide automated feedback when Claude Code produces code that does not follow your codebase conventions.
- **Custom permissions**: Block modifications to production files or sensitive directories.

By encoding these rules as hooks rather than prompting instructions, you turn suggestions into app-level code that executes every time it is expected to run.

Hooks execute shell commands with your full user permissions without confirmation. You are responsible for ensuring your hooks are safe and secure. Anthropic is not liable for any data loss or system damage resulting from hook usage. Review [Security Considerations](https://docs.anthropic.com/en/docs/claude-code/hooks#security-considerations).

## Quickstart

In this quickstart, you’ll add a hook that logs the shell commands that Claude Code runs.

Quickstart Prerequisite: Install `jq` for JSON processing in the command line.

### Step 1: Open hooks configuration

Run the `/hooks` [slash command](https://docs.anthropic.com/en/docs/claude-code/slash-commands) and select the `PreToolUse` hook event.

`PreToolUse` hooks run before tool calls and can block them while providing Claude feedback on what to do differently.

### Step 2: Add a matcher

Select `+ Add new matcher…` to run your hook only on Bash tool calls.

Type `Bash` for the matcher.

### Step 3: Add the hook

Select `+ Add new hook…` and enter this command:

### Step 4: Save your configuration

For storage location, select `User settings` since you’re logging to your home directory. This hook will then apply to all projects, not just your current project.

Then press Esc until you return to the REPL. Your hook is now registered!

### Step 5: Verify your hook

Run `/hooks` again or check `~/.claude/settings.json` to see your configuration:

## Configuration

Claude Code hooks are configured in your [settings files](https://docs.anthropic.com/en/docs/claude-code/settings):

- `~/.claude/settings.json` - User settings
- `.claude/settings.json` - Project settings
- `.claude/settings.local.json` - Local project settings (not committed)
- Enterprise managed policy settings

### Structure

Hooks are organized by matchers, where each matcher can have multiple hooks:

- **matcher**: Pattern to match tool names (only applicable for `PreToolUse` and `PostToolUse`)
	- Simple strings match exactly: `Write` matches only the Write tool
	- Supports regex: `Edit|Write` or `Notebook.*`
	- If omitted or empty string, hooks run for all matching events
- **hooks**: Array of commands to execute when the pattern matches
	- `type`: Currently only `"command"` is supported
	- `command`: The bash command to execute

## Hook Events

### PreToolUse

Runs after Claude creates tool parameters and before processing the tool call.

**Common matchers:**

- `Task` - Agent tasks
- `Bash` - Shell commands
- `Glob` - File pattern matching
- `Grep` - Content search
- `Read` - File reading
- `Edit`, `MultiEdit` - File editing
- `Write` - File writing
- `WebFetch`, `WebSearch` - Web operations

### PostToolUse

Runs immediately after a tool completes successfully.

Recognizes the same matcher values as PreToolUse.

### Notification

Runs when Claude Code sends notifications.

### Stop

Runs when Claude Code has finished responding.

## Hook Input

Hooks receive JSON data via stdin containing session information and event-specific data:

### PreToolUse Input

The exact schema for `tool_input` depends on the tool.

### PostToolUse Input

The exact schema for `tool_input` and `tool_response` depends on the tool.

### Notification Input

### Stop Input

`stop_hook_active` is true when Claude Code is already continuing as a result of a stop hook. Check this value or process the transcript to prevent Claude Code from running indefinitely.

## Hook Output

There are two ways for hooks to return output back to Claude Code. The output communicates whether to block and any feedback that should be shown to Claude and the user.

### Simple: Exit Code

Hooks communicate status through exit codes, stdout, and stderr:

- **Exit code 0**: Success. `stdout` is shown to the user in transcript mode (CTRL-R).
- **Exit code 2**: Blocking error. `stderr` is fed back to Claude to process automatically. See per-hook-event behavior below.
- **Other exit codes**: Non-blocking error. `stderr` is shown to the user and execution continues.

#### Exit Code 2 Behavior

| Hook Event | Behavior |
| --- | --- |
| `PreToolUse` | Blocks the tool call, shows error to Claude |
| `PostToolUse` | Shows error to Claude (tool already ran) |
| `Notification` | N/A, shows stderr to user only |
| `Stop` | Blocks stoppage, shows error to Claude |

### Advanced: JSON Output

Hooks can return structured JSON in `stdout` for more sophisticated control:

#### Common JSON Fields

All hook types can include these optional fields:

If `continue` is false, Claude stops processing after the hooks run.

- For `PreToolUse`, this is different from `"decision": "block"`, which only blocks a specific tool call and provides automatic feedback to Claude.
- For `PostToolUse`, this is different from `"decision": "block"`, which provides automated feedback to Claude.
- For `Stop`, this takes precedence over any `"decision": "block"` output.
- In all cases, `"continue" = false` takes precedence over any `"decision": "block"` output.

`stopReason` accompanies `continue` with a reason shown to the user, not shown to Claude.

#### PreToolUse Decision Control

`PreToolUse` hooks can control whether a tool call proceeds.

- “approve” bypasses the permission system. `reason` is shown to the user but not to Claude.
- “block” prevents the tool call from executing. `reason` is shown to Claude.
- `undefined` leads to the existing permission flow. `reason` is ignored.

#### PostToolUse Decision Control

`PostToolUse` hooks can control whether a tool call proceeds.

- “block” automatically prompts Claude with `reason`.
- `undefined` does nothing. `reason` is ignored.

#### Stop Decision Control

`Stop` hooks can control whether Claude must continue.

- “block” prevents Claude from stopping. You must populate `reason` for Claude to know how to proceed.
- `undefined` allows Claude to stop. `reason` is ignored.

#### JSON Output Example: Bash Command Editing

```python
#!/usr/bin/env python3
import json
import re
import sys

# Define validation rules as a list of (regex pattern, message) tuples
VALIDATION_RULES = [
    (
        r"\bgrep\b(?!.*\|)",
        "Use 'rg' (ripgrep) instead of 'grep' for better performance and features",
    ),
    (
        r"\bfind\s+\S+\s+-name\b",
        "Use 'rg --files | rg pattern' or 'rg --files -g pattern' instead of 'find -name' for better performance",
    ),
]

def validate_command(command: str) -> list[str]:
    issues = []
    for pattern, message in VALIDATION_RULES:
        if re.search(pattern, command):
            issues.append(message)
    return issues

try:
    input_data = json.load(sys.stdin)
except json.JSONDecodeError as e:
    print(f"Error: Invalid JSON input: {e}", file=sys.stderr)
    sys.exit(1)

tool_name = input_data.get("tool_name", "")
tool_input = input_data.get("tool_input", {})
command = tool_input.get("command", "")

if tool_name != "Bash" or not command:
    sys.exit(1)

# Validate the command
issues = validate_command(command)

if issues:
    for message in issues:
        print(f"• {message}", file=sys.stderr)
    # Exit code 2 blocks tool call and shows stderr to Claude
    sys.exit(2)
```

#### Stop Decision Control

`Stop` hooks can control tool execution:

## Working with MCP Tools

Claude Code hooks work seamlessly with [Model Context Protocol (MCP) tools](https://docs.anthropic.com/en/docs/claude-code/mcp). When MCP servers provide tools, they appear with a special naming pattern that you can match in your hooks.

### MCP Tool Naming

MCP tools follow the pattern `mcp__<server>__<tool>`, for example:

- `mcp__memory__create_entities` - Memory server’s create entities tool
- `mcp__filesystem__read_file` - Filesystem server’s read file tool
- `mcp__github__search_repositories` - GitHub server’s search tool

### Configuring Hooks for MCP Tools

You can target specific MCP tools or entire MCP servers:

## Examples

### Code Formatting

Automatically format code after file modifications:

### Notification

Customize the notification that is sent when Claude Code requests permission or when the prompt input has become idle.

## Security Considerations

**USE AT YOUR OWN RISK**: Claude Code hooks execute arbitrary shell commands on your system automatically. By using hooks, you acknowledge that:

- You are solely responsible for the commands you configure
- Hooks can modify, delete, or access any files your user account can access
- Malicious or poorly written hooks can cause data loss or system damage
- Anthropic provides no warranty and assumes no liability for any damages resulting from hook usage
- You should thoroughly test hooks in a safe environment before production use

Always review and understand any hook commands before adding them to your configuration.

### Security Best Practices

Here are some key practices for writing more secure hooks:

1. **Validate and sanitize inputs** - Never trust input data blindly
2. **Always quote shell variables** - Use `"$VAR"` not `$VAR`
3. **Block path traversal** - Check for `..` in file paths
4. **Use absolute paths** - Specify full paths for scripts
5. **Skip sensitive files** - Avoid `.env`, `.git/`, keys, etc.

### Configuration Safety

Direct edits to hooks in settings files don’t take effect immediately. Claude Code:

1. Captures a snapshot of hooks at startup
2. Uses this snapshot throughout the session
3. Warns if hooks are modified externally
4. Requires review in `/hooks` menu for changes to apply

This prevents malicious hook modifications from affecting your current session.

## Hook Execution Details

- **Timeout**: 60-second execution limit
- **Parallelization**: All matching hooks run in parallel
- **Environment**: Runs in current directory with Claude Code’s environment
- **Input**: JSON via stdin
- **Output**:
	- PreToolUse/PostToolUse/Stop: Progress shown in transcript (Ctrl-R)
	- Notification: Logged to debug only (`--debug`)

## Debugging

To troubleshoot hooks:

1. Check if `/hooks` menu displays your configuration
2. Verify that your [settings files](https://docs.anthropic.com/en/docs/claude-code/settings) are valid JSON
3. Test commands manually
4. Check exit codes
5. Review stdout and stderr format expectations
6. Ensure proper quote escaping

Progress messages appear in transcript mode (Ctrl-R) showing:

- Which hook is running
- Command being executed
- Success/failure status
- Output or error messages