---
created: 2025-07-03
tags:
  - AI/Agent
  - AI/Optimization
source: https://blog.dailydoseofds.com/p/build-an-automated-agent-optimization
author: <PERSON><PERSON>
Reference:
---

# Build an Automated Agent Optimization Workflow

[blog.dailydoseofds.com](https://blog.dailydoseofds.com/p/build-an-automated-agent-optimization) Avi <PERSON>

**In today's newsletter:**

* ​Unified backend framework for APIs, Events, and Agents​.

* Build an Automated Agent Optimization Workflow.

* 3 prompting techniques for reasoning in LLMs.

### [Unified backend framework for APIs, Events, and Agents \[Open-source\]](https://github.com/MotiaDev/motia/tree/main)

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21hp58%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fae8a3286-44d8-4d6f-ae58-2b1b287e4678_1208x888.png&valid=true)](https://github.com/MotiaDev/motia/tree/main)

Most AI backends are complete chaos with patched-together APIs, scattered cron jobs, and agents that can't even communicate properly.

They may have five different frameworks just to complete a single workflow.

**[Motia](https://github.com/MotiaDev/motia/tree/main)** (open-source) provides a unified system where APIs, background jobs, events, and agents are just plug-and-play steps.

Just like React streamlines frontend development, Motia simplifies AI backend, where you only need one solution instead of a dozen tools.

Key features:

* You can have Python, JS \& TypeScript in the same workflow.

* You can deploy from your laptop to prod in one click.

* It has built-in observability \& state management.

* It provides automatic retries \& fault tolerance.

* It supports streaming response.

**[GitHub repo →](https://github.com/MotiaDev/motia/tree/main) (don't forget to star)**

[Motia GitHub repo](https://github.com/MotiaDev/motia/tree/main)

### [Build an Automated Agent Optimization Workflow](https://www.comet.com/docs/opik/agent_optimization/overview)

Developers manually iterate through prompts to find an optimal one. This is not scalable, and performance can degrade across models.

Today, let's learn how to use the **[Opik Agent Optimizer](https://www.comet.com/docs/opik/agent_optimization/overview)** toolkit that lets you automatically optimize prompts for LLM apps.

The idea is to start with an initial prompt and an evaluation dataset, and let an LLM iteratively improve the prompt based on evaluations.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21gwz7%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F16db5cd0-6262-45de-9c1e-cfa33fcb1a0a_1526x500.gif&valid=true)](https://substackcdn.com/image/fetch/$s_!gwz7!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F16db5cd0-6262-45de-9c1e-cfa33fcb1a0a_1526x500.gif)

To begin, install Opik and its optimizer package, and configure Opik:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21qo2I%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fa31ccd07-9054-4604-8719-eb790e86081d_2680x1276.png&valid=true)](https://substackcdn.com/image/fetch/$s_!qo2I!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fa31ccd07-9054-4604-8719-eb790e86081d_2680x1276.png)

Next, import all the required classes and functions from `opik` and `opik_optimizer`:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21BfAK%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F90bb4773-4f34-4e86-90c4-f259b2391250_2352x724.png&valid=true)](https://substackcdn.com/image/fetch/$s_!BfAK!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F90bb4773-4f34-4e86-90c4-f259b2391250_2352x724.png)

* `LevenshteinRatio` → Our metric to evaluate the prompt's effectiveness in generating a precise output for the given input.

* `MetaPromptOptimizer` → An algorithm that uses a reasoning model to critique and iteratively refine your initial instruction prompt.

* `tiny_test` → A basic test dataset with input-output pairs.

Next, define an evaluation dataset:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21Fhxm%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F5642c637-d636-4417-9bfc-e7c77d1665ab_2056x1380.png&valid=true)](https://substackcdn.com/image/fetch/$s_!Fhxm!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F5642c637-d636-4417-9bfc-e7c77d1665ab_2056x1380.png)

Moving on, configure the evaluation metric, which tells the optimizer how to score the LLM's outputs against the given label:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21hCIV%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F3bb58dea-16c1-47e8-ab0a-a058cd86339f_2200x940.png&valid=true)](https://substackcdn.com/image/fetch/$s_!hCIV!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F3bb58dea-16c1-47e8-ab0a-a058cd86339f_2200x940.png)

Next, define your base prompt, which is the initial instruction that the `MetaPromptOptimizer` will try to enhance:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21wdo_%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fc02eb899-9cc1-40a2-8466-047d190c29eb_2656x1324.png&valid=true)](https://substackcdn.com/image/fetch/$s_!wdo_!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fc02eb899-9cc1-40a2-8466-047d190c29eb_2656x1324.png)

Next, instantiate a `MetaPromptOptimizer`, specifying the model to use in the optimization process:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%218aNL%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F21cbb0dc-2e11-4db3-a30d-0dfc5e728934_2072x692.png&valid=true)](https://substackcdn.com/image/fetch/$s_!8aNL!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F21cbb0dc-2e11-4db3-a30d-0dfc5e728934_2072x692.png)

Finally, the `optimizer.optimize_prompt(...)` method is invoked with the dataset, metric configuration, and prompt to start the optimization process:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%211UOS%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fb180c8eb-262c-4352-b9b5-13ff700f9d73_2000x852.png&valid=true)](https://substackcdn.com/image/fetch/$s_!1UOS!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fb180c8eb-262c-4352-b9b5-13ff700f9d73_2000x852.png)

It starts by evaluating the initial prompt, which sets the baseline:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21ciqs%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Ff59a1f5d-7656-41ee-95f7-5a03fc138e0c_2216x1276.png&valid=true)](https://substackcdn.com/image/fetch/$s_!ciqs!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Ff59a1f5d-7656-41ee-95f7-5a03fc138e0c_2216x1276.png)

Then it iterates through several different prompts (written by AI), evaluates them, and prints the most optimal prompt. You can invoke `result.display()` to see a summary of the optimization, the best prompt found and its score:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21cb1n%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F245e68a5-f3eb-4e2f-b372-68eb98f455fe_2208x1260.png&valid=true)](https://substackcdn.com/image/fetch/$s_!cb1n!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F245e68a5-f3eb-4e2f-b372-68eb98f455fe_2208x1260.png)

The optimization results are also available in the Opik dashboard for further analysis and visualization:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21w1Ta%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F956eaf6f-d18d-4797-9fcf-c585c16b1f6a_2208x1412.png&valid=true)](https://substackcdn.com/image/fetch/$s_!w1Ta!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F956eaf6f-d18d-4797-9fcf-c585c16b1f6a_2208x1412.png)

And that's how you can use **[Opik Agent Optimizer](https://www.comet.com/docs/opik/agent_optimization/overview)** to enhance the performance and efficiency of your LLM apps.

While we used GPT-4o, everything here can be executed 100% locally since you can use any other LLM + Opik is fully open-source.

**[Here is the link to the Opik docs to learn more →](https://www.comet.com/docs/opik/agent_optimization/overview)**

Talking of prompting, let's see 3 techniques that induce reasoning in LLMs below.

### 3 prompting techniques for reasoning in LLMs

Here are three popular prompting techniques that help LLMs think more clearly before they answer.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21BL9h%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_lossy%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Faaad766c-b168-4208-8e93-c3d54017c187_994x1040.gif&valid=true)](https://substackcdn.com/image/fetch/$s_!BL9h!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Faaad766c-b168-4208-8e93-c3d54017c187_994x1040.gif)

#### **#1) Chain of Thought (CoT)**

The simplest and most widely used technique.

Instead of asking the LLM to jump straight to the answer, we nudge it to reason step by step.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21rqZ4%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_lossy%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fc1345634-8093-41b8-9a17-64830ef3da8d_994x201.gif&valid=true)](https://substackcdn.com/image/fetch/$s_!rqZ4!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fc1345634-8093-41b8-9a17-64830ef3da8d_994x201.gif)

This often improves accuracy because the model can walk through its logic before committing to a final output.

For instance:

Q: If John has 3 apples and gives away 1, how many are left? Let's think step by step:

It's a simple example, but this tiny nudge can unlock reasoning capabilities that standard zero-shot prompting could miss.

#### **#2) Self-Consistency (a.k.a. Majority Voting over CoT)**

CoT is useful but not always consistent.

If you prompt the same question multiple times, you might get different answers depending on the temperature setting (we covered temperature in LLMs here).

Self-Consistency embraces this variation.

You ask the LLM to generate multiple reasoning paths and then select the most common final answer.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21EYZ8%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_lossy%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fd7c15c41-7faf-4722-a3a2-ef7348e1bfc5_994x336.gif&valid=true)](https://substackcdn.com/image/fetch/$s_!EYZ8!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fd7c15c41-7faf-4722-a3a2-ef7348e1bfc5_994x336.gif)

It's a simple idea: when in doubt, ask the model several times and trust the majority.

This technique often leads to more robust results, especially on ambiguous or complex tasks.

However, it doesn't evaluate how the reasoning was done---just whether the final answer is consistent across paths.

#### **#3) Tree of Thoughts (ToT)**

While Self-Consistency varies the final answer, Tree of Thoughts varies the steps of reasoning at each point and then picks the best path overall.

At every reasoning step, the model explores multiple possible directions. These branches form a tree, and a separate process evaluates which path seems the most promising at a particular timestamp.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21IAp7%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_lossy%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F909ee654-9cdc-4440-a0b4-d0b5c710ff02_994x435.gif&valid=true)](https://substackcdn.com/image/fetch/$s_!IAp7!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F909ee654-9cdc-4440-a0b4-d0b5c710ff02_994x435.gif)

Think of it like a search algorithm over reasoning paths, where we try to find the most logical and coherent trail to the solution.

It's more compute-intensive, but in most cases, it significantly outperforms basic CoT.

We'll put together a demo on this soon, covering several use cases and best practices for inducing reasoning in LLMs through prompting.

Let us know what you would like to learn.

That said, the ReAct pattern for AI Agents also involves Reasoning.

[​](https://www.dailydoseofds.com/ai-agents-crash-course-part-10-with-implementation/)**[We implemented it from scratch here →](https://www.dailydoseofds.com/ai-agents-crash-course-part-10-with-implementation/)**

Thanks for reading!

### **P.S. For those wanting to develop "Industry ML" expertise:**

[![](https://images.cubox.cc/cardImg/n2pch3bqdykaytf8bwv5csa9mnyfttprxwnx09awev93p89l9.png)](https://substackcdn.com/image/fetch/$s_!cn8y!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F939bede7-b0de-4770-a3e9-34d39488e776_2733x1020.png)

At the end of the day, all businesses care about *impact* . That's it!

* Can you reduce costs?

* Drive revenue?

* Can you scale ML models?

* Predict trends before they happen?

We have discussed several other topics (with implementations) that align with such topics.

[Develop "Industry ML" Skills](https://www.dailydoseofds.com/membership)

Here are some of them:

* Learn how to build Agentic systems in **[a crash course with 14 parts](https://www.dailydoseofds.com/ai-agents-crash-course-part-1-with-implementation/)** .

* Learn how to build real-world RAG apps and evaluate and scale them in **[this crash course](https://www.dailydoseofds.com/a-crash-course-on-building-rag-systems-part-1-with-implementations/)** .

[![](https://images.cubox.cc/cardImg/4spzixbq1vbvruz237umfz8x54m7osmy95qg7lwse4bg8723br.png)](https://substackcdn.com/image/fetch/$s_!HcHn!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fca6f5be8-3aa0-4754-8087-1929397898e6_697x920.png)

* Learn sophisticated graph architectures and how to train them on graph data.

* So many real-world NLP systems rely on pairwise context scoring. Learn scalable approaches **[here](https://www.dailydoseofds.com/bi-encoders-and-cross-encoders-for-sentence-pair-similarity-scoring-part-1/)** .

* Learn how to run large models on small devices using [​](https://www.dailydoseofds.com/quantization-optimize-ml-models-to-run-them-on-tiny-hardware/)**[Quantization techniques](https://www.dailydoseofds.com/quantization-optimize-ml-models-to-run-them-on-tiny-hardware/)** .

* Learn how to generate prediction intervals or sets with strong statistical guarantees for increasing trust using [​](https://www.dailydoseofds.com/conformal-predictions-build-confidence-in-your-ml-models-predictions/)**[Conformal Predictions](https://www.dailydoseofds.com/conformal-predictions-build-confidence-in-your-ml-models-predictions/)** .

* Learn how to identify causal relationships and answer business questions using causal inference in **[this crash course](https://www.dailydoseofds.com/a-crash-course-on-causality-part-1/)** .

* Learn how to scale and implement ML model training in this **[practical guide](https://www.dailydoseofds.com/how-to-scale-model-training/)** .

* Learn techniques to reliably **[test new models in production](https://www.dailydoseofds.com/5-must-know-ways-to-test-ml-models-in-production-implementation-included/)** .

* Learn how to build privacy-first ML systems using [​](https://www.dailydoseofds.com/federated-learning-a-critical-step-towards-privacy-preserving-machine-learning/)**[Federated Learning](https://www.dailydoseofds.com/federated-learning-a-critical-step-towards-privacy-preserving-machine-learning/)** .

* Learn 6 techniques with implementation to **[compress ML models](https://www.dailydoseofds.com/model-compression-a-critical-step-towards-efficient-machine-learning/)** .

All these resources will help you cultivate key skills that businesses and companies care about the most.

[Read in Cubox](https://cubox.cc/my/card?id=7340307735159768145)
