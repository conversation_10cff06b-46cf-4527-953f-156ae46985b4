---
title: "google-gemini/gemini-cli: An open-source AI agent that brings the power of Gemini directly into your terminal."
source: https://github.com/google-gemini/gemini-cli
author:
  - "[[jerop]]"
published:
created: 2025-06-26
description: An open-source AI agent that brings the power of Gemini directly into your terminal. - google-gemini/gemini-cli
tags:
  - AI/Gemini
  - AI/Google
  - AI/NoCode
reference:
---
# Memo

```
https://console.cloud.google.com/apis/dashboard?hl=zh-cn&inv=1&invt=Ab1GoA&project=gen-lang-client-0761578978


- modify environment
export GOOGLE_CLOUD_PROJECT="gen-lang-client-0761578978"

- install
npm install -g @google/gemini-cli
```
# Customized (claude support)

cd /opt/app/gemini-cli
export CLAUDE_API_KEY="sk-ant-api03-your-key-here"
export CLAUDE_MODEL="claude-3-5-sonnet-20241022"  # 可選
### 1. Install dependencies
npm install

### 2. Build the project
npm run build

### 3. Start the CLI
npm start

---
# 原文
**[gemini-cli](https://github.com/google-gemini/gemini-cli)** Public

An open-source AI agent that brings the power of Gemini directly into your terminal.

[Apache-2.0 license](https://github.com/google-gemini/gemini-cli/blob/main/LICENSE)

[Security policy](https://github.com/google-gemini/.github/blob/main/SECURITY.md)

[10.6k stars](https://github.com/google-gemini/gemini-cli/stargazers) [651 forks](https://github.com/google-gemini/gemini-cli/forks) [75 watching](https://github.com/google-gemini/gemini-cli/watchers) [Branches](https://github.com/google-gemini/gemini-cli/branches) [Tags](https://github.com/google-gemini/gemini-cli/tags) [Activity](https://github.com/google-gemini/gemini-cli/activity) [Custom properties](https://github.com/google-gemini/gemini-cli/custom-properties)

Public repository

[Open in github.dev](https://github.dev/) [Open in a new github.dev tab](https://github.dev/) [Open in codespace](https://github.com/codespaces/new/google-gemini/gemini-cli?resume=1)

<table><thead><tr><th colspan="2"><span>Name</span></th><th colspan="1"><span>Name</span></th><th><p><span>Last commit message</span></p></th><th colspan="1"><p><span>Last commit date</span></p></th></tr></thead><tbody><tr><td colspan="3"><p><span><a href="https://github.com/google-gemini/gemini-cli/commit/b6b9923dc3b80a73fdee3a3ccd6070c8cfb551cd">b6b9923</a> ·</span></p><p><a href="https://github.com/google-gemini/gemini-cli/commits/main/"><span><span><span>1,024 Commits</span></span></span></a></p></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/gemini-cli/tree/main/.gcp">.gcp</a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/gemini-cli/tree/main/.gcp">.gcp</a></p></td><td><p><a href="https://github.com/google-gemini/gemini-cli/commit/f6c36f75e37a9fb6e53480981c2ca1b9267763a0">fix: prepublish changes to package names (</a><a href="https://github.com/google-gemini/gemini-cli/pull/1420">#1420</a><a href="https://github.com/google-gemini/gemini-cli/commit/f6c36f75e37a9fb6e53480981c2ca1b9267763a0">)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/gemini-cli/tree/main/.gemini">.gemini</a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/gemini-cli/tree/main/.gemini">.gemini</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/gemini-cli/tree/main/.github">.github</a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/gemini-cli/tree/main/.github">.github</a></p></td><td><p><a href="https://github.com/google-gemini/gemini-cli/commit/b6b9923dc3b80a73fdee3a3ccd6070c8cfb551cd">Streamline issue submission with YAML forms (</a><a href="https://github.com/google-gemini/gemini-cli/pull/1608">#1608</a><a href="https://github.com/google-gemini/gemini-cli/commit/b6b9923dc3b80a73fdee3a3ccd6070c8cfb551cd">)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/gemini-cli/tree/main/.vscode">.vscode</a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/gemini-cli/tree/main/.vscode">.vscode</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/gemini-cli/tree/main/docs">docs</a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/gemini-cli/tree/main/docs">docs</a></p></td><td><p><a href="https://github.com/google-gemini/gemini-cli/commit/b6b9923dc3b80a73fdee3a3ccd6070c8cfb551cd">Streamline issue submission with YAML forms (</a><a href="https://github.com/google-gemini/gemini-cli/pull/1608">#1608</a><a href="https://github.com/google-gemini/gemini-cli/commit/b6b9923dc3b80a73fdee3a3ccd6070c8cfb551cd">)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/gemini-cli/tree/main/eslint-rules">eslint-rules</a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/gemini-cli/tree/main/eslint-rules">eslint-rules</a></p></td><td><p><a href="https://github.com/google-gemini/gemini-cli/commit/e351baf10f06d2a1d1872bf2a6d7e9e709effed9">feat: add custom eslint rule for cross-package imports (</a><a href="https://github.com/google-gemini/gemini-cli/pull/77">#77</a><a href="https://github.com/google-gemini/gemini-cli/commit/e351baf10f06d2a1d1872bf2a6d7e9e709effed9">)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/gemini-cli/tree/main/integration-tests">integration-tests</a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/gemini-cli/tree/main/integration-tests">integration-tests</a></p></td><td><p><a href="https://github.com/google-gemini/gemini-cli/commit/b96fbd913e8449c99ed7b95920652acdce5dd779">test: add integration test for simple mcp server (</a><a href="https://github.com/google-gemini/gemini-cli/pull/1199">#1199</a><a href="https://github.com/google-gemini/gemini-cli/commit/b96fbd913e8449c99ed7b95920652acdce5dd779">)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/gemini-cli/tree/main/packages">packages</a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/gemini-cli/tree/main/packages">packages</a></p></td><td><p><a href="https://github.com/google-gemini/gemini-cli/commit/b6b9923dc3b80a73fdee3a3ccd6070c8cfb551cd">Streamline issue submission with YAML forms (</a><a href="https://github.com/google-gemini/gemini-cli/pull/1608">#1608</a><a href="https://github.com/google-gemini/gemini-cli/commit/b6b9923dc3b80a73fdee3a3ccd6070c8cfb551cd">)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/gemini-cli/tree/main/scripts">scripts</a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/gemini-cli/tree/main/scripts">scripts</a></p></td><td><p><a href="https://github.com/google-gemini/gemini-cli/commit/f6c36f75e37a9fb6e53480981c2ca1b9267763a0">fix: prepublish changes to package names (</a><a href="https://github.com/google-gemini/gemini-cli/pull/1420">#1420</a><a href="https://github.com/google-gemini/gemini-cli/commit/f6c36f75e37a9fb6e53480981c2ca1b9267763a0">)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/google-gemini/gemini-cli/blob/main/.gitattributes">.gitattributes</a></p></td><td colspan="1"><p><a href="https://github.com/google-gemini/gemini-cli/blob/main/.gitattributes">.gitattributes</a></p></td><td></td><td></td></tr><tr><td colspan="3"></td></tr></tbody></table>

## Gemini CLI

[![Gemini CLI Screenshot](https://github.com/google-gemini/gemini-cli/raw/main/docs/assets/gemini-screenshot.png)](https://github.com/google-gemini/gemini-cli/blob/main/docs/assets/gemini-screenshot.png)

This repository contains the Gemini CLI, a command-line AI workflow tool that connects to your tools, understands your code and accelerates your workflows.

With the Gemini CLI you can:

- Query and edit large codebases in and beyond Gemini's 1M token context window.
- Generate new apps from PDFs or sketches, using Gemini's multimodal capabilities.
- Automate operational tasks, like querying pull requests or handling complex rebases.
- Use tools and MCP servers to connect new capabilities, including [media generation with Imagen, Veo or Lyria](https://github.com/GoogleCloudPlatform/vertex-ai-creative-studio/tree/main/experiments/mcp-genmedia)
- Ground your queries with the [Google Search](https://ai.google.dev/gemini-api/docs/grounding) tool, built in to Gemini.

## Quickstart

1. **Prerequisites:** Ensure you have [Node.js version 18](https://nodejs.org/en/download) or higher installed.
2. **Run the CLI:** Execute the following command in your terminal:
	```
	npx https://github.com/google-gemini/gemini-cli
	```
	Or install it with:
	```
	npm install -g @google/gemini-cli
	gemini
	```
3. **Pick a color theme**
4. **Authenticate:** When prompted, sign in with your personal Google account. This will grant you up to 60 model requests per minute and 1,000 model requests per day using Gemini.

You are now ready to use the Gemini CLI!

If you need to use a specific model or require a higher request capacity, you can use an API key:

1. Generate a key from [Google AI Studio](https://aistudio.google.com/apikey).
2. Set it as an environment variable in your terminal. Replace `YOUR_API_KEY` with your generated key.
	```
	export GEMINI_API_KEY="YOUR_API_KEY"
	```

For other authentication methods, including Google Workspace accounts, see the [authentication](https://github.com/google-gemini/gemini-cli/blob/main/docs/cli/authentication.md) guide.

## Examples

Once the CLI is running, you can start interacting with Gemini from your shell.

You can start a project from a new directory:

```
cd new-project/
gemini
> Write me a Gemini Discord bot that answers questions using a FAQ.md file I will provide
```

Or work with an existing project:

```
git clone https://github.com/google-gemini/gemini-cli
cd gemini-cli
gemini
> Give me a summary of all of the changes that went in yesterday
```

### Next steps

- Learn how to [contribute to or build from the source](https://github.com/google-gemini/gemini-cli/blob/main/CONTRIBUTING.md).
- Explore the available **[CLI Commands](https://github.com/google-gemini/gemini-cli/blob/main/docs/cli/commands.md)**.
- If you encounter any issues, review the **[Troubleshooting guide](https://github.com/google-gemini/gemini-cli/blob/main/docs/troubleshooting.md)**.
- For more comprehensive documentation, see the [full documentation](https://github.com/google-gemini/gemini-cli/blob/main/docs/index.md).
- Take a look at some [popular tasks](https://github.com/google-gemini/#popular-tasks) for more inspiration.

### Troubleshooting

Head over to the [troubleshooting](https://github.com/google-gemini/gemini-cli/blob/main/docs/troubleshooting.md) guide if you're having issues.

## Popular tasks

Start by `cd` ing into an existing or newly-cloned repository and running `gemini`.

```
> Describe the main pieces of this system's architecture.
```

```
> What security mechanisms are in place?
```

```
> Implement a first draft for GitHub issue #123.
```

```
> Help me migrate this codebase to the latest version of Java. Start with a plan.
```

Use MCP servers to integrate your local system tools with your enterprise collaboration suite.

```
> Make me a slide deck showing the git history from the last 7 days, grouped by feature and team member.
```

```
> Make a full-screen web app for a wall display to show our most interacted-with GitHub issues.
```

```
> Convert all the images in this directory to png, and rename them to use dates from the exif data.
```

```
> Organise my PDF invoices by month of expenditure.
```

For details on the terms of service and privacy notice applicable to your use of Gemini CLI, see the [Terms of Service and Privacy Notice](https://github.com/google-gemini/gemini-cli/blob/main/docs/tos-privacy.md).

## Releases

[1 tags](https://github.com/google-gemini/gemini-cli/tags)

## Languages

- [TypeScript 95.1%](https://github.com/google-gemini/gemini-cli/search?l=typescript)
- [JavaScript 4.6%](https://github.com/google-gemini/gemini-cli/search?l=javascript)
- Other 0.3%