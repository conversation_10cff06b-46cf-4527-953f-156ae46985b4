---
created: 2025-06-26
tags:
  - AI/DailyDose
  - AI/MCP
  - AI/Voice
source:
author:
Reference:
---

# \[Hands-on\] Build an MCP-powered Audio Analysis Toolkit

[blog.dailydoseofds.com](https://blog.dailydoseofds.com/p/hands-on-build-an-mcp-powered-audio) A<PERSON>

In today's newsletter:

* Fire Enrich: Open-source data enrichment tool.

* Build an MCP-powered audio analysis toolkit.

* ​Discriminative vs. Generative Models.

* MCP and A2A, explained visually.

### **[Fire Enrich: Open-source data enrichment tool](https://github.com/mendableai/fire-enrich)**

Firecrawl released an open-source Clay alternative.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21sFOo%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F6f9ab541-3c20-42a5-97a2-82bb6f434f82_1236x812.png&valid=true)](https://github.com/mendableai/fire-enrich)

Just upload a CSV with emails, and AI agents automatically fill in missing data like decision makers, company size, and more.

**[GitHub repo →](https://github.com/mendableai/fire-enrich) (don't forget to star)**

*Thanks to Firecrawl for partnering today!*

### Build an MCP-powered audio analysis toolkit

Today, we are releasing another MCP demo, which is an MCP-driven Audio Analysis toolkit that accepts an audio file and lets you:

1. Transcribe it  
2. Perform sentiment analysis  
3. Summarize it  
4. Identify named entities mentioned  
5. Extract broad ideas  
6. Interact with it

...all via MCPs.

Here's our tech stack:

* **[AssemblyAI](https://www.assemblyai.com/dailydose)** for transcription and audio analysis.

* Claude Desktop as the MCP host.

Here's our workflow:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21H6Gg%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F80dde2fd-acac-487b-9d1f-08199715d4fd_1200x928.gif&valid=true)](https://substackcdn.com/image/fetch/$s_!H6Gg!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F80dde2fd-acac-487b-9d1f-08199715d4fd_1200x928.gif)

* User's audio input is sent to AssemblyAI via a local MCP server.

* **[AssemblyAI](https://www.assemblyai.com/dailydose)** transcribes it while providing the summary, speaker labels, sentiment, and topics.

* Post-transcription, the user can also chat with audio.

Let's implement this!

#### Transcription MCP tool

This tool accepts an audio input from the user and transcribes it using AssemblyAI.

We also store the full transcript to use in the next tool.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%218_g7%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fc32a625d-a869-4889-a6d4-8188525a4343_680x596.png&valid=true)](https://substackcdn.com/image/fetch/$s_!8_g7!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fc32a625d-a869-4889-a6d4-8188525a4343_680x596.png)

#### Audio analysis tool

Next, we have a tool that returns specific insights from the transcript, like speaker labels, sentiment, topics, and summary.

Based on the user's input query, the corresponding flags will be automatically set to True when the Agent will prepare the tool call via MCP:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21G9pO%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F48afc3c5-217a-4e66-a4d5-71b662fc403c_679x554.png&valid=true)](https://substackcdn.com/image/fetch/$s_!G9pO!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F48afc3c5-217a-4e66-a4d5-71b662fc403c_679x554.png)

#### Create MCP Server

Now, we'll set up an MCP server to use the tools we created above.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21prvk%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F8c05b5a4-389c-4a1a-b08e-c41c368a0dce_680x481.png&valid=true)](https://substackcdn.com/image/fetch/$s_!prvk!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F8c05b5a4-389c-4a1a-b08e-c41c368a0dce_680x481.png)

#### Integrate MCP server with Claude Desktop

Go to File → Settings → Developer → Edit Config and add the following code.

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21rre_%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Ffc74d42d-aaf3-4208-b968-a69688b1e587_680x518.png&valid=true)](https://substackcdn.com/image/fetch/$s_!rre_!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Ffc74d42d-aaf3-4208-b968-a69688b1e587_680x518.png)

Once the server is configured, Claude Desktop will show the two tools we built above in the Tools menu:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21CtIn%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F22384d99-61fa-4470-b098-c7b6eeb89f08_679x422.png&valid=true)](https://substackcdn.com/image/fetch/$s_!CtIn!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F22384d99-61fa-4470-b098-c7b6eeb89f08_679x422.png)

* transcribe_audio

* get_audio_data

And now you can interact with it:

We have also created a Streamlit UI for the audio analysis app.

You can upload the audio, extract insights, and chat with it using AssemblyAI's LeMUR.

And that was our MCP-powered audio analysis toolkit.

Here's the workflow again for your reference:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21H6Gg%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F80dde2fd-acac-487b-9d1f-08199715d4fd_1200x928.gif&valid=true)](https://substackcdn.com/image/fetch/$s_!H6Gg!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F80dde2fd-acac-487b-9d1f-08199715d4fd_1200x928.gif)

* User-provided audio is sent to AssemblyAI through the MCP server.

* AssemblyAI processes it, MCP host returns requested insights.

**[You can find the code in this repo →](https://github.com/patchy631/ai-engineering-hub/tree/main/audio-analysis-toolkit)**

### [​](https://www.dailydoseofds.com/p/discriminative-vs-generative-models/)**[Discriminative vs. Generative Models](https://www.dailydoseofds.com/p/discriminative-vs-generative-models/)** [​](https://www.dailydoseofds.com/p/discriminative-vs-generative-models/)

Here's a visual that depicts how generative and discriminative models differ:

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%215Q07%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_progressive%3Asteep%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252F4b63c65e-a33f-4558-9d2a-f88a4b683d98_1456x1718.png&valid=true)](https://substackcdn.com/image/fetch/$s_!5Q07!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F4b63c65e-a33f-4558-9d2a-f88a4b683d98_1456x1718.png)

We have seen this topic come up in several interviews, so let's learn more.

Discriminative models:

* learn decision boundaries that separate different classes.

* maximize the conditional probability: P(Y\|X) --- Given X, maximize the probability of label Y.

* are specifically meant for classification tasks.

Generative models:

* maximize the joint probability: P(X, Y)

* learn the class-conditional distribution P(X\|Y)

* are typically not preferred to solve downstream classification tasks.

Since generative models learn the underlying distribution, they can generate new samples. But this is not possible with discriminative models.

Furthermore, generative models possess discriminative properties, i.e., they can be used for classification tasks (if needed). But discriminative models do not possess generative properties.

[​](https://www.dailydoseofds.com/p/discriminative-vs-generative-models/)**[We covered this in more detail in this newsletter issue →](https://www.dailydoseofds.com/p/discriminative-vs-generative-models/)** [​](https://www.dailydoseofds.com/p/discriminative-vs-generative-models/)

### MCP and A2A, explained visually[​](https://www.dailydoseofds.com/p/build-a-multi-agent-network-with-agent2agent-protocol/)

[![](https://cubox.cc/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fsubstackcdn.com%2Fimage%2Ffetch%2F%24s_%21dw1q%21%2Cw_1456%2Cc_limit%2Cf_auto%2Cq_auto%3Agood%2Cfl_lossy%2Fhttps%253A%252F%252Fsubstack-post-media.s3.amazonaws.com%252Fpublic%252Fimages%252Fed49ca8f-b3d0-476e-904f-d5b40bb599bb_1060x984.gif&valid=true)](https://substackcdn.com/image/fetch/$s_!dw1q!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fed49ca8f-b3d0-476e-904f-d5b40bb599bb_1060x984.gif)

In a gist:

* Agent2Agent (A2A) protocol lets AI agents connect to other Agents.

* Model context protocol lets AI Agents connect to Tools/APIs.

So using A2A, while two Agents might be talking to each other...they themselves might be communicating to MCP servers.

In that sense, they do not compete with each other.

The thing about A2A protocol is that Agents can communicate and collaborate with other Agents, even if they are built on different platforms or frameworks.

* In MCP, tools (functions) are represented with docstrings.

* In A2A, Agents are represented using an Agent Card, which is a JSON file that lists the Agent's capabilities, input, authentication schemes, etc.

[​](https://www.dailydoseofds.com/p/build-a-multi-agent-network-with-agent2agent-protocol/)**[For practical details, we built an Agent network with A2A Protocol here →](https://www.dailydoseofds.com/p/build-a-multi-agent-network-with-agent2agent-protocol/)** [​](https://www.dailydoseofds.com/p/build-a-multi-agent-network-with-agent2agent-protocol/)

Thanks for reading!

[Read in Cubox](https://cubox.cc/my/card?id=7337184906985868929)
