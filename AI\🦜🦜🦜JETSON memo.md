---
DocFlag:
  - Reference
  - Tested
Updated: ""
tags:
  - AI->-Jetson
  - AI/Jetson
Created: 2023-08-23T00:51
Reference:
  - https://www.nvidia.com/content/dam/en-zz/Solutions/gtcf21/jetson-orin/nvidia-jetson-agx-orin-technical-brief.pdf
  - https://developer.nvidia.com/embedded/learn/jetson-agx-orin-devkit-user-guide/two_ways_to_set_up_software.html
  - https://developer.download.nvidia.com/assets/embedded/secure/jetson/agx_orin/Jetson_AGX_Orin_Series_TDG-10943-001_v1.2.pdf?tvY3VO3SzXVDpb6-lpSI38syZf7OKDE_Mn_eDkMGIqPq3y1FVVPKOiAfbcxBlzBNdx5Ud_htHRbcGJtLN1mW3FBpGSxgSw9VeBpN1XcNlQpSWgUHHTW7hytUBu9XNuj499CZkXWxUOm1w7t1XUOyAM20J-bMXgc4q0oSCZ2Ejq82MwbXoBUJcLvVaOphjUTI-jqc&t=eyJscyI6IndlYnNpdGUiLCJsc2QiOiJmb3J1bXMuZGV2ZWxvcGVyLm52aWRpYS5jb20vIn0=
  - https://www.jianguoyun.com/p/DVyCpFMQ5cbrChizq4AFIAA#file=%2FRTSO-2005%E4%BA%A7%E5%93%81%E6%89%8B%E5%86%8C.pdf::size=969268
  - https://www.jianguoyun.com/p/Da39rasQ5cbrChig4eQEIAA
  - https://www.realtimes.cn/cn/software.html
  - https://forums.developer.nvidia.com/t/pytorch-for-jetson/72048
---
Initial Setup
switch power mode and fan control
run celery whisperx server in Jetson
Swap file setup
SSH Key setup
Code for testing GPU pytorch
libffi library setup
OpenCV
OpenCL
Setup paperwithcode with PG15 and vectorDB
Vector 距离公式
如何在systemctl启动python 程序
ZeroTier Setup
LaTEX Support
Modular and Moko
lightbgm
To make ChatGLM and LLAMA working in Jetson
Qwen
GO Environment Setup
EmotiVoice Play
libgomp issue
Stirling PDF
Install docker compose
Install 个人金融软件 和 Ruby
After upgrade Jetson from 5 to 6
Create one Jump Server tunnel
FlashAttention Install
Fix GLIBCXX_3.4.30 not find issue
Install pipx
Ollama

# Initial Setup
![[Pasted image 20241031165811.png]]
```JavaScript
https://docs.nvidia.com/jetson/jetpack/install-jetpack/index.html
https://developer.nvidia.com/embedded/learn/get-started-jetson-agx-orin-devkit

https://forums.developer.nvidia.com/t/unable-to-locate-package-nvidia-jetpack-on-orin-devkit/210894/33
Please edit your nvidia-l4t-apt-source.list to r34.1:
cat /etc/apt/sources.list.d/nvidia-l4t-apt-source.list
deb https://repo.download.nvidia.com/jetson/common r35.3 main
deb https://repo.download.nvidia.com/jetson/t234 r35.3 main
###\#Run below command to upgrade and install sdk components:
https://docs.nvidia.com/jetson/jetpack/install-setup/index.html\#upgrade-jetpack
edit /etc/apt/sources.list.d/nvidia-l4t-apt-source.list
deb https://repo.download.nvidia.com/jetson/common r35.4 main
deb https://repo.download.nvidia.com/jetson/t234 r35.4 main
to
deb https://repo.download.nvidia.com/jetson/common r36.3 main
deb https://repo.download.nvidia.com/jetson/t234 r36.3 main

sudo apt install lvm2
sudo apt install xfsprogs
vi /etc/modules
add 'xfs'
sudo modprobe xfs
\#have to use ext4 in jetson
sudo apt update
sudo apt dist-upgrade.
sudo reboot
sudo apt install nvidia-jetpack
apt list nvidia-l4t-core
sudo aptitude install nvidia-jetpack
---------------------------------------------------------------
Download SDK Manager
https://developer.nvidia.com/sdk-manager
https://docs.nvidia.com/sdk-manager/install-with-sdkm-jetson/index.html
sudo apt install ./sdkmanager_[version]-[build#]_amd64.deb
You can start SDK Manager using one of the following two methods:
Launch SDK Manager from the Ubuntu launcher.
Open a terminal and launch SDK Manager with the following command:
sdkmanager
https://linuxize.com/post/how-to-configure-static-ip-address-on-ubuntu-20-04/
edit /etc/netplan/01-network-manager-all.yaml
sudo netplan apply
working!
\#Flash AGX Orin 
1. 数据线很重要，不然无法识别。在左手白色第二层抽屉里数据线
2. 在没有power下插入usb-a到主机。usb-c到jetson box
3. 按住recover button同时，插入电源线
4. unbuntu host会弹出对话框让你选择硬件，说明认识了
开刷！
1. 安装前先把鼠标monitor键盘都连接在jetson上
2. flash结束会开始连os，继续安装cuda等，这时候使用************是连不上的，要用step1里的键盘鼠标去修改ip为************
3. 然后继续安装cuda
------------------------------------------
./Miniconda3-latest-Linux-aarch64.sh
conda create -n mlearn python=3.8.17
conda activate mlearn
pip install nvitop
pip install torch torchvision torchaudio --force-reinstall --index-url https://download.pytorch.org/whl/cu118
pip install torch torchvision torchaudio --force-reinstall --index-url https://download.pytorch.org/whl/cu117
\#install jtop
sudo apt install pip
sudo pip install jetson-stats

https://forums.developer.nvidia.com/t/torch-not-detecting-gpu/226147
!!!! explain in chinese
https://blog.csdn.net/lanyan90/article/details/131411549?spm=1001.2101.3001.6650.1&utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7ECTRLIST%7ERate-1-131411549-blog-125408173.235%5Ev38%5Epc_relevant_sort_base1&depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7ECTRLIST%7ERate-1-131411549-blog-125408173.235%5Ev38%5Epc_relevant_sort_base1&utm_relevant_index=2
!!!!!! below link is good
https://developer.nvidia.com/embedded/learn/jetson-agx-orin-devkit-user-guide/two_ways_to_set_up_software.html
https://forums.developer.nvidia.com/t/pytorch-for-jetson/72048
https://forums.developer.nvidia.com/t/pytorch-for-jetson-version-1-11-now-available/72048
https://github.com/ultralytics/ultralytics/issues/1982
https://blog.csdn.net/a111222zw/article/details/120632906
https://forums.developer.nvidia.com/t/cuda-is-not-installed-on-jetson-orin/220661/7
cd /usr/local/cuda-11.4/samples/1_Utilities/deviceQuery
make
./deviceQuery
jetson_release -v
nvcc -V
dpkg -l libcudnn8
=======================================
Some OS side change
\#add into .bashrc
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/local/cuda/lib64
export PATH=$PATH:/usr/local/cuda/bin
export CUDA_HOME=/usr/local/cuda
\#change link
cd /usr/local
sudo rm -f cuda
sudo ln -s cuda-11.4 cuda
https://developer.ridgerun.com/wiki/index.php/NVIDIA_Jetson_Orin/JetPack_5.0.2/Getting_Started/Wizard_Flashing\#Step_2:_Flash
https://developer.ridgerun.com/wiki/index.php/NVIDIA_Jetson_Orin/JetPack_5.0.2/Flashing_Board
\#Japanese guy and good article
https://www.souichi.club/deep-learning/jetson-agx-orin/
\#Install chromium otherwise after sdkmanager run, browser wont be triggered
wget https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb
sudo apt install ./google-chrome-stable_current_amd64.deb
\#install sdkmanager and run
Ubuntu/Qapl1209ws#
\#switch orin to recover mode and then start flash

\#install torch and torchversion
sudo apt-get -y install autoconf bc build-essential g++-8 gcc-8 clang-8 lld-8 gettext-base gfortran-8 iputils-ping libbz2-dev libc++-dev libcgal-dev libffi-dev libfreetype6-dev libhdf5-dev libjpeg-dev liblzma-dev libncurses5-dev libncursesw5-dev libpng-dev libreadline-dev libssl-dev libsqlite3-dev libxml2-dev libxslt-dev locales moreutils openssl python-openssl rsync scons python3-pip libopenblas-dev;
sudo apt-get install python3-pip libopenblas-base libopenmpi-dev libomp-dev
pip3 install Cython
pip install numpy torch-2.0.0+nv23.05-cp38-cp38-linux_aarch64.whl
https://forums.developer.nvidia.com/t/installing-torchvison-in-jetson-agx-orin/238126
cd /opt/install/torchvision
sudo apt-get install libjpeg-dev zlib1g-dev libpython3-dev libavcodec-dev libavformat-dev libswscale-dev
git branch -a
git checkout v0.15.2
git branch
export BUILD_VERSION=0.15.2
pip install ninja
python3 setup.py install --user
sudo ln -s /usr/lib/python3.8/dist-packages/tensorrt* /opt/workspace/miniconda3/envs/mlearn/lib/python3.8/site-packages/
sudo ln -s /usr/lib/python3.8/dist-packages/tensorrt* /opt/workspace/miniconda3/envs/fastchat/lib/python3.8/site-packages/
sudo ln -s /usr/lib/python3.8/dist-packages/tensorrt* /opt/workspace/miniconda3/envs/mom/lib/python3.8/site-packages/
# Build torchaudio
https://pytorch.org/audio/master/build.jetson.html
python -c '
import torch
print(torch.__version__)
print(torch.cuda.is_available())
print(torch.empty((1, 2), device=torch.device("cuda")))
'
pip install cmake ninja
sudo apt install ffmpeg libavformat-dev libavcodec-dev libavutil-dev libavdevice-dev libavfilter-dev
git clone https://github.com/pytorch/audio
cd audio
USE_CUDA=1 pip install -v -e . --no-use-pep517
pip install ffmpegio
\#build CTranslate2
https://opennmt.net/CTranslate2/installation.html
git clone --recursive https://github.com/OpenNMT/CTranslate2.git
cd CTranslate2
mkdir build && cd build
cmake .. -DWITH_CUDA=ON -DWITH_CUDNN=ON -DWITH_MKL=OFF -DCMAKE_CXX_COMPILER=/tmp/gcc-11/usr/bin/g++-gcc-11.2.0 -DCMAKE_C_COMPILER=/tmp/gcc-11/usr/bin/gcc-11.2.0
make -j4
sudo make install
sudo ldconfig
cd python
pip install pybind11==2.11.1
python setup.py bdist_wheel
pip install dist/*.whl
(mom) ray@jethome:/opt/workspace/app/mom$ cat startwork.sh
\#export LD_PRELOAD=/opt/workspace/miniconda3/envs/mom/lib/python3.8/site-packages/ctranslate2.libs/libgomp-d22c30c5.so.1.0.0
export LD_PRELOAD=/opt/workspace/miniconda3/envs/mom/lib/python3.8/site-packages/scikit_learn.libs/libgomp-d22c30c5.so.1.0.0
celery -A celery_worker worker --loglevel=info --concurrency=1
/opt/workspace/miniconda3/envs/mom/lib/python3.8/site-packages/transformers/sagemaker/training_args_sm.py
\#80
if True and self.local_rank == -1:
# if torch.distributed.is_available() and torch.distributed.is_initialized() and self.local_rank == -1:
/databank/workspace/miniconda3/envs/mom/lib/python3.8/site-packages/speechbrain/core.py
\#603
# if not torch.distributed.is_initialized():
            if not False():
/databank/workspace/miniconda3/envs/mom/lib/python3.8/site-packages/speechbrain/utils/distributed.py
\#103
# if torch.distributed.is_initialized():
    if False:
        torch.distributed.barrier()
/databank/app/mom/whisperX/whisperx/diarize.py
\#18
# self.model = Pipeline.from_pretrained(model_name, use_auth_token=use_auth_token).to(device)
self.model = Pipeline.from_pretrained(model_name, use_auth_token=use_auth_token)
('{"error": "Cannot re-initialize CUDA in forked subprocess. To use CUDA with multiprocessing, you must use the \'spawn\' start method"}', 500)
https://blog.csdn.net/qazwsxrx/article/details/*********#:~:text=%E5%9C%A8%E7%A8%8B%E5%BA%8F%E6%9C%80%E5%BC%80%E5%A7%8B%E7%9A%84%E5%9C%B0%E6%96%B9%E5%8A%A0%E4%B8%8A%EF%BC%9Atorch.multiprocessing.set_start_method%20%28%E2%80%98spawn%E2%80%99%29%E5%8D%B3%E5%8F%AF%E8%A7%A3%E5%86%B3%EF%BC%8C%E5%A6%82%E4%B8%8B%E7%AC%AC%E4%BA%8C%E8%A1%8C%E6%89%80%E7%A4%BA%EF%BC%9Aif%20__name__%3D%3D%27__main__%27%3A%20torch.multiprocessing.set_start_method%20%28%27spawn%27%29%20args%20%3D%20config_parser,multiprocessing%2C%20you%20must%20use%20the%20%27spawn%27%20start%20method

\#install rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
\#install transformer!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
pip install transformers
pip install git+https://github.com/huggingface/transformers
pip install transformers==4.30.2
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/local/cuda-11.2
/usr/local/cuda-11.4/targets/aarch64-linux/lib/libcudart.so
/usr/local/cuda-11.4/targets/aarch64-linux/lib/libcudart.so.11.0
/usr/local/cuda-11.4/targets/aarch64-linux/lib/libcudart.so.11.4.298
-- to run flash attention2 you need to install below
pip install flash-attn --no-build-isolation
pip install rotary-embedding-torch
pip install git+https://github.com/HazyResearch/flash-attention.git\#subdirectory=csrc/rotary
\#download cuda
https://developer.nvidia.com/cuda-11-7-0-download-archive?target_os=Linux&target_arch=x86_64&Distribution=RHEL&target_version=8&target_type=rpm_local
wget https://developer.download.nvidia.com/compute/cuda/11.7.0/local_installers/cuda-repo-rhel8-11-7-local-11.7.0_515.43.04-1.x86_64.rpm
sudo rpm -i cuda-repo-rhel8-11-7-local-11.7.0_515.43.04-1.x86_64.rpm
(llama) [raysheng@MONSTER:/etc/yum.repos.d]$ cat cuda-rhel8-11-7-local.repo
[cuda-rhel8-11-7-local]
name=cuda-rhel8-11-7-local
baseurl=file:///var/cuda-repo-rhel8-11-7-local
enabled=1
gpgcheck=1
gpgkey=file:///var/cuda-repo-rhel8-11-7-local/D1FBD088.pub
obsoletes=0
sudo dnf install cuda-11-7
\#sudo dnf clean all
\#sudo dnf -y module install nvidia-driver:latest-dkms
\#sudo dnf -y install cuda
sudo update-alternatives --config cuda
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/local/cuda-11.2
git clone https://github.com/TimDettmers/bitsandbytes.git
cd bitsandbytes
CUDA_VERSION=114 make cuda11x
python setup.py install
```

# switch power mode and fan control

```JavaScript
https://docs.nvidia.com/jetson/archives/r34.1/DeveloperGuide/text/SD/PlatformPowerAndPerformance/JetsonOrinNxSeriesAndJetsonAgxOrinSeries.html\#supported-modes-and-power-efficiency
sudo /usr/sbin/nvpmodel -m <x>
sudo /usr/sbin/nvpmodel -q
\#enable max perf mode and all cpu will be online
sudo /usr/sbin/nvpmodel -m 0
```

# run celery whisperx server in Jetson

1. as not support torch multiprocess. initialize model have to put into global
2. torchaudio have to build from source so that able to support cuda
3. Have to change multiple file to disable torch.distributed.is_initialized()
4. And jetson not support multprocess cuda . so not working
5. so I created one jetson version mom and didn’t use celery work

```JavaScript
ImportError: /opt/workspace/miniconda3/envs/mom/lib/python3.8/site-packages/ctranslate2/../ctranslate static TLS block
export LD_PRELOAD=/opt/workspace/miniconda3/envs/mom/lib/python3.8/site-packages/ctranslate2.libs/libgomp-d22c30c5.so.1.0.0
export LD_PRELOAD=$LD_PRELOAD:/databank/workspace/miniconda3/envs/mom/lib/python3.8/site-packages/torch/lib/libc10_cuda.so
```

# Resize Disk
```
sudo lvextend --resizefs -L +200G datavg/datalv
```
# Swap file setup

```JavaScript
https://bobcares.com/blog/add-swap-partition-ubuntu/
lvcreate -L32G -n lvswap datavg
root@jethome:~# mkswap /dev/datavg/lvswap
Setting up swapspace version 1, size = 32 GiB (*********** bytes)
no label, UUID=3d85b1b0-6b20-4253-b4cd-a80ad1d32755
blkid
/dev/mapper/datavg-lvswap: UUID="3d85b1b0-6b20-4253-b4cd-a80ad1d32755" TYPE="swap"
swapon /dev/mapper/datavg-lvswap
swapon -s
free -g
/etc/sysctl.conf
vm.swappiness=10
vm.vfs_cache_pressure=50
how to extend swap file in jetson
sudo lvextend -L80G datavg/lvswap
sudo swapoff /dev/mapper/datavg-lvswap
sudo mkswap /dev/datavg/lvswap
sudo swapon /dev/mapper/datavg-lvswap
sudo swapon -s
sudo blkid

Seems the new swap not being used
change to same priority as zram
vi /etc/fstab
/dev/datavg/lvswap none swap sw,pri=5 0 0
sudo swapoff -a
sudo swapon -a  
# above command will only run additional swap work, restart zram service
# to make zram working
sudo systemctl restart nvzramconfig
jtop to confirm

-- totally disable zram and enable normal swap
sudo systemctl stop nvzramconfig
sudo systemctl disable nvzramconfig
lvcreate -L32G -n lvswap datavg
mkswap /dev/datavg/lvswap
sudo swapon /dev/mapper/datavg-lvswap

change to same priority as zram
vi /etc/fstab
/dev/datavg/lvswap none swap sw,pri=5 0 0
sudo swapoff -a
sudo swapon -a  

```
# Disable desktop portal services
```
dpkg -l | grep xdg-desktop-portal

# Create or edit ~/.config/autostart/
mkdir -p ~/.config/autostart

# Create override files to prevent autostart
cat > ~/.config/autostart/xdg-desktop-portal.desktop <<EOF
[Desktop Entry]
Hidden=true
EOF

cat > ~/.config/autostart/xdg-desktop-portal-gtk.desktop <<EOF
[Desktop Entry]
Hidden=true
EOF

cat > ~/.config/autostart/xdg-desktop-portal-gnome.desktop <<EOF
[Desktop Entry]
Hidden=true
EOF

systemctl --user mask xdg-desktop-portal.service
systemctl --user mask xdg-desktop-portal-gnome.service
```
## not going to remove but just in case
```
sudo apt remove xdg-desktop-portal xdg-desktop-portal-gtk xdg-desktop-portal-gnome
sudo apt autoremove
sudo apt clean
```
# SSH Key setup

```JavaScript
https://www.cnblogs.com/safe-rabbit/p/16254860.html#:~:text=VsCode%E9%85%8D%E7%BD%AEssh%E5%85%8D%E5%AF%86%E8%BF%9C%E7%A8%8B%E7%99%BB%E5%BD%95%20%EF%BC%881%EF%BC%89%E6%89%93%E5%BC%80vscode%EF%BC%8C%E7%82%B9%E5%87%BB%E7%BA%A2%E8%89%B2%E6%96%B9%E6%A1%86%E5%A4%84%E5%AE%89%E8%A3%85%E6%8F%92%E4%BB%B6%20%EF%BC%882%EF%BC%89%E5%A6%82%E6%9E%9C%E9%9C%80%E8%A6%81%E6%B1%89%E5%8C%96%E5%8F%AF%E4%BB%A5%E5%AE%89%E8%A3%85%E4%B8%80%E4%B8%8B%E8%BF%99%E4%B8%AA%E6%8F%92%E4%BB%B6%EF%BC%8C%E5%AE%89%E8%A3%85%E5%AE%8C%E6%88%90%E5%90%8E%E9%87%8D%E5%90%AF%E4%B8%80%E4%B8%8Bvscode%E5%B0%B1%E5%8F%AF%E4%BB%A5%20%EF%BC%883%EF%BC%89%E5%AE%89%E8%A3%85%E8%BF%9C%E7%A8%8B%E8%BF%9E%E6%8E%A5%E7%9A%84%E6%8F%92%E4%BB%B6,%EF%BC%884%EF%BC%89%E5%AE%89%E8%A3%85%E6%88%90%E5%8A%9F%E5%90%8E%E4%BC%9A%E5%87%BA%E7%8E%B0%E7%BA%A2%E8%89%B2%E6%96%B9%E6%A1%86%E5%9B%BE%E6%A0%87%EF%BC%8C%E7%82%B9%E5%87%BB%E8%BF%9B%E5%8E%BB%E9%85%8D%E7%BD%AE%EF%BC%8C%E7%84%B6%E5%90%8E%E5%9C%A8%E7%BB%BF%E8%89%B2%E6%96%B9%E6%A1%86%E9%80%89%E6%8B%A9SSH%20Targets%20%EF%BC%885%EF%BC%89%E5%86%8D%E7%82%B9%E5%87%BB%E4%B8%80%E4%B8%8B%E7%BA%A2%E8%89%B2%E6%96%B9%E6%A1%86%E5%87%BA%E9%BD%BF%E8%BD%AE%E8%BF%99%E4%B8%AA%E6%8C%89%E9%92%AE%20%EF%BC%886%EF%BC%89%E7%82%B9%E5%87%BB%E4%B8%8B%E9%9D%A2%E7%BA%A2%E6%A1%86%EF%BC%8C%E8%BF%99%E6%98%AFSSH%E8%BF%9C%E7%A8%8B%E7%9A%84%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6
from pc/laptop
ssh-keygen -t rsa -b 4096
get pub key and copy to
authorized_keys
```
## github ssh key setup
```
# generate key
ssh-keygen -t ed25519 -C "<EMAIL>"
# add this pub key to github
https://github.com/settings/keys

# delete a file from repo history
git filter-branch --index-filter "git rm -rf --cached --ignore-unmatch backend/.env" HEAD
git push --force
```

# Code for testing GPU pytorch

```JavaScript
\#Install
https://docs.nvidia.com/deeplearning/frameworks/install-pytorch-jetson-platform/index.html
sudo apt-get -y update; 
sudo apt-get -y install autoconf bc build-essential g++-8 gcc-8 clang-8 lld-8 gettext-base gfortran-8 iputils-ping libbz2-dev libc++-dev libcgal-dev libffi-dev libfreetype6-dev libhdf5-dev libjpeg-dev liblzma-dev libncurses5-dev libncursesw5-dev libpng-dev libreadline-dev libssl-dev libsqlite3-dev libxml2-dev libxslt-dev locales moreutils openssl python-openssl rsync scons python3-pip libopenblas-dev;

import torch
print(torch.__version__)
print('CUDA available: ' + str(torch.cuda.is_available()))
print('cuDNN version: ' + str(torch.backends.cudnn.version()))
a = torch.cuda.FloatTensor(2).zero_()
print('Tensor a = ' + str(a))
b = torch.randn(2).cuda()
print('Tensor b = ' + str(b))
c = a + b
print('Tensor c = ' + str(c))
```

# libffi library setup

```Python
ldd /lib/aarch64-linux-gnu/libp11-kit.so.0
(base) ray@jethome:/lib/aarch64-linux-gnu$ ls -lrt libffi*
-rw-r--r-- 1 <USER> <GROUP> 35144 Mar 24  2020 libffi.so.7.1.0
lrwxrwxrwx 1 root root    15 Mar 24  2020 libffi.so.7 -> libffi.so.7.1.0
lrwxrwxrwx 1 root root    15 Mar 24  2020 libffi.so -> libffi.so.7.1.0
-rw-r--r-- 1 <USER> <GROUP> 46092 Mar 24  2020 libffi_pic.a
-rw-r--r-- 1 <USER> <GROUP> 46380 Mar 24  2020 libffi.a
ln -s /lib/aarch64-linux-gnu/libffi.so.7.1.0
ln -sf libffi.so.7.1.0 libffi.so.7
ln -sf libffi.so.7.1.0 libffi.so
wget http://ports.ubuntu.com/pool/main/libf/libffi/libffi7_3.3-4_arm64.deb
wget http://ports.ubuntu.com/pool/main/libf/libffi/libffi-dev_3.3-4_arm64.deb
sudo dpkg -i libffi7_3.3-4_arm64.deb
sudo dpkg -i libffi-dev_3.3-4_arm64.deb
```

# OpenCV

## pre installation
```
https://developer.ridgerun.com/wiki/index.php/Compiling_OpenCV_from_Source
\#maybe purge is not required for 4.8.0
sudo apt purge libopencv-dev libopencv-python libopencv-samples libopencv*
\#GUI supoort
sudo apt-get install libgtk2.0-dev pkg-config
sudo apt-get install ccache
sudo apt-get install -y libgflags-dev libgoogle-glog-dev libeigen3-dev
sudo apt-get install -y tesseract-ocr libtesseract-dev
```

## make CmakeList configuration file
### 4.8.0 
```Python
VERSION=4.8.0
git clone https://github.com/opencv/opencv.git -b $VERSION --depth 1
wget -O opencv_contrib.zip https://github.com/opencv/opencv_contrib/archive/4.8.0.zip
cd /opt/install/opencv-4.8.0/
mkdir build
cd build
rm -rf **

cmake -D CMAKE_BUILD_TYPE=RELEASE \
-D CMAKE_INSTALL_PREFIX=/usr/local \
-D OPENCV_GENERATE_PKGCONFIG=ON \
-D BUILD_EXAMPLES=OFF \
-D INSTALL_PYTHON_EXAMPLES=OFF \
-D INSTALL_C_EXAMPLES=OFF \
-D PYTHON_EXECUTABLE=$(which python2) \
-D BUILD_opencv_python2=OFF \
-D PYTHON3_EXECUTABLE=$(which python3) \
-D OPENCV_EXTRA_MODULES_PATH=/opt/install/opencv-4.8.0/opencv_contrib-4.8.0/modules \
-D WITH_CUDA=ON \
-D PYTHON3_INCLUDE_DIR=$(python3 -c "from distutils.sysconfig import get_python_inc; print(get_python_inc())") \
-D PYTHON3_PACKAGES_PATH=$(python3 -c "from distutils.sysconfig import get_python_lib; print(get_python_lib())") \
/opt/install/opencv-4.8.0/opencv
cmake -D CMAKE_BUILD_TYPE=RELEASE \
-D CMAKE_INSTALL_PREFIX=/usr/local \
-D OPENCV_GENERATE_PKGCONFIG=ON \
-D BUILD_EXAMPLES=OFF \
-D INSTALL_PYTHON_EXAMPLES=OFF \
-D INSTALL_C_EXAMPLES=OFF \
-D PYTHON_EXECUTABLE=$(which python2) \
-D BUILD_opencv_python2=OFF \
-D PYTHON3_EXECUTABLE=$(which python3) \
-D OPENCV_EXTRA_MODULES_PATH=/opt/install/opencv_contrib-4.6.0/modules \
-D WITH_CUDA=ON \
-D PYTHON3_INCLUDE_DIR=$(python3 -c "from distutils.sysconfig import get_python_inc; print(get_python_inc())") \
-D PYTHON3_PACKAGES_PATH=$(python3 -c "from distutils.sysconfig import get_python_lib; print(get_python_lib())") \
 ..
cmake -D CMAKE_BUILD_TYPE=RELEASE \
-D CMAKE_INSTALL_PREFIX=/usr/local \
-D CUDA_TOOLKIT_ROOT_DIR=/usr/local/cuda \
-D OPENCV_GENERATE_PKGCONFIG=ON \
-D BUILD_EXAMPLES=OFF \
-D INSTALL_PYTHON_EXAMPLES=OFF \
-D INSTALL_C_EXAMPLES=OFF \
-D PYTHON_EXECUTABLE=$(which python2) \
-D BUILD_opencv_python2=OFF \
-D PYTHON3_EXECUTABLE=$(which python3) \
-D OPENCV_EXTRA_MODULES_PATH=/opt/install/opencv-4.8.0/opencv_contrib-4.8.0/modules \
-D WITH_CUDA=ON \
-D PYTHON3_INCLUDE_DIR=$(python3 -c "from distutils.sysconfig import get_python_inc; print(get_python_inc())") \
-D PYTHON3_PACKAGES_PATH=$(python3 -c "from distutils.sysconfig import get_python_lib; print(get_python_lib())") \
/opt/install/opencv-4.8.0/opencv
```
### 4.9.0
```python
---

Build under jetson pack 36.3

cd /opt/install/jetpack6/v61/opencv/opencv_contrib-4.9.0/modules/cudev/test/
sudo cp CMakeLists.txt CMakeLists.txt.backup
sudo nano CMakeLists.txt
Find the line containing ocv_check_windows_crt_linkage() (around line 20) and either:
Comment it out by adding # at the start of the line

https://github.com/opencv/opencv_contrib/issues/3690
https://github.com/NVIDIA/cccl/pull/1522
https://github.com/NVIDIA/cccl

git clone https://github.com/NVIDIA/cccl.git
cd cccl
cmake --preset install -DCMAKE_INSTALL_PREFIX=/usr/local/
cd build/install
sudo /home/<USER>/miniconda3/envs/mlearn/bin/ninja install


[16:00:57]ray@jethome64:/opt/install/jetpack6/v61/opencv/opencv_contrib-4.9.0/modules/cudev/include/opencv2/cudev$ cd
[16:01:17]ray@jethome64:~$ cp -rp zip.hpp zip.hpp.backup
vi <- pls check url

conda config --add channels conda-forge
conda install cccl


cd build
rm -rf *

cmake -D CMAKE_BUILD_TYPE=Release \
      -D CMAKE_INSTALL_PREFIX=/usr/local \
      -D OPENCV_EXTRA_MODULES_PATH=/opt/install/jetpack6/v61/opencv/opencv_contrib-4.9.0/modules \
      -D WITH_CUDA=ON \
      -D CUDA_ARCH_BIN="87" \
      -D CUDA_ARCH_PTX="" \
      -D WITH_CUDNN=ON \
      -D WITH_CUBLAS=ON \
      -D ENABLE_FAST_MATH=ON \
      -D CUDA_FAST_MATH=ON \
      -D OPENCV_DNN_CUDA=ON \
      -D ENABLE_NEON=ON \
      -D WITH_QT=OFF \
      -D WITH_OPENMP=ON \
      -D BUILD_TIFF=ON \
      -D WITH_FFMPEG=ON \
      -D WITH_GSTREAMER=ON \
      -D WITH_TBB=ON \
      -D BUILD_TBB=ON \
      -D WITH_EIGEN=ON \
      -D WITH_V4L=ON \
      -D WITH_LIBV4L=ON \
      -D OPENCV_ENABLE_NONFREE=ON \
      -D INSTALL_C_EXAMPLES=OFF \
      -D INSTALL_PYTHON_EXAMPLES=OFF \
      -D BUILD_NEW_PYTHON_SUPPORT=ON \
      -D BUILD_opencv_python3=ON \
      -D OPENCV_GENERATE_PKGCONFIG=ON \
      -D BUILD_EXAMPLES=OFF \
      /databank/install/jetpack6/v61/opencv
	  

----
```
## make and installation


```
make -j8 or make -j$(nproc)
sudo make install
sudo ldconfig
\#Cuda 12.2 issue
https://github.com/opencv/opencv/issues/23893
You want to change line 114 in opencv/modules/dnn/src/cuda4dnn/primitives/normalize_bbox.hpp:
from:
if (weight != 1.0)
to:
if (weight != static_cast<T>(1.0))
As well as line 124 in opencv/modules/dnn/src/cuda4dnn/primitives/region.hpp (due to a similar error):
from:
if (nms_iou_threshold > 0) {
to:
if (nms_iou_threshold > static_cast<T>(0)) {


```

# OpenCL

```Python
\#install opencl
https://yunusmuhammad007.medium.com/build-and-install-opencl-on-jetson-nano-10bf4a7f0e65

LLVM_VERSION=10
sudo apt install -y build-essential ocl-icd-libopencl1 cmake git pkg-config libclang-${LLVM_VERSION}-dev clang-${LLVM_VERSION} llvm-${LLVM_VERSION} make ninja-build ocl-icd-libopencl1 ocl-icd-dev ocl-icd-opencl-dev libhwloc-dev zlib1g zlib1g-dev clinfo dialog apt-utils libxml2-dev libclang-cpp${LLVM_VERSION}-dev libclang-cpp${LLVM_VERSION} llvm-${LLVM_VERSION}-dev libncurses5
cmake -DCMAKE_INSTALL_PREFIX=/usr/local/pocl/ -DENABLE_CUDA=ON -DLLC_HOST_CPU=aarch64 -DCLANG_MARCH_FLAG=aarch64 -DLLVM_ALL_TARGETS=NVPTX ..
make -j4
----
CMake Error at cmake/LLVM.cmake:715 (message):
  LLVM could not recognize your CPU model automatically.  Please run CMake
  with -DLLC_HOST_CPU=<cpu> (you can find valid names with: llc -mcpu=help)
Could not determine whether to use -march or -mcpu with clang
----
\#get cpu list
llc -mcpu=help --version
```

Setup paperwithcode with PG15 and vectorDB

```JavaScript
\#Streamlit paperwithcode
\#data gathering
https://github.com/paperswithcode/paperswithcode-data
https://paperswithcode.com/about
https://www.server-world.info/query?os=Ubuntu_20.04&p=postgresql12&f=1
sudo apt install postgresql postgresql-contrib
apt remove postgresql postgresql-contrib
wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo apt-key add -
echo "deb http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" | sudo tee /etc/apt/sources.list.d/postgresql-pgdg.list > /dev/null
apt install postgresql-15
apt install postgresql-server-dev-15
config: /etc/postgresql/15
Binary: /usr/lib/postgresql/15
data  : /var/lib/postgresql/15
dev   : /usr/share/postgresql/15 /usr/share/locale/ /usr/include/postgresql/15/
apt install postgresql
apt install postgresql-server-dev-15
dpkg -L postgresql-15
dpkg -l |grep postgres
dpkg -r packagename
\#explain query will fail at <-> op, so we use log file to record execution plan
shared_preload_libraries = 'auto_explain'
auto_explain.log_min_duration='0'
# 日志目标
logging_collector = on
log_directory = 'pg_log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
# 审计设置
log_statement = 'all'  # 记录所有 SQL 查询
The following packages will be REMOVED:
  apt-clone archdetect-deb bogl-bterm busybox-static cryptsetup-bin dctrl-tools dpkg-repack gdal-data gir1.2-goa-1.
  gir1.2-timezonemap-1.0 gir1.2-xkl-1.0 grub-common libarmadillo9 libarpack2 libavresample-dev libavresample4 libcf
  libcharls2 libdap25 libdapclient6v5 libdc1394-22-dev libdebian-installer4 libepsilon1 libexif-dev libfreexl1 libf
  libfyba0 libgdal26 libgdcm-dev libgdcm3.0 libgeos-3.8.0 libgeos-c1v5 libgeotiff5 libgl2ps1.4 libgphoto2-dev libhd
  libhdf5-openmpi-103 libilmbase-dev libjbig-dev libkmlbase1 libkmldom1 libkmlengine1 liblept5 libminizip1 libnetcd
  libnetcdf15 libodbc1 libogdi4.1 libopencv-calib3d4.2 libopencv-contrib4.2 libopencv-dnn4.2 libopencv-features2d4.
  libopencv-flann4.2 libopencv-highgui4.2 libopencv-imgcodecs4.2 libopencv-imgproc4.2 libopencv-ml4.2 libopencv-obj
  libopencv-photo4.2 libopencv-shape4.2 libopencv-stitching4.2 libopencv-superres4.2 libopencv-video4.2 libopencv-v
  libopencv-videostab4.2 libopencv-viz4.2 libopencv4.2-java libopencv4.2-jni libopenexr-dev libproj15 libqhull7 lib
  libsocket++1 libspatialite7 libsuperlu5 libtesseract4 libtiff-dev libtiffxx5 libtimezonemap-data libtimezonemap1
  libvtk6.3 libxerces-c3.2 libxmlb1 odbcinst odbcinst1debian2 os-prober postgresql-client-12 proj-data python3-icu
  rdate tasksel tasksel-data


psql -U ray -h ************ -p 5432 -W -d raydb

CREATE TABLE paperwithcode (
    paper_url VARCHAR,
    arxiv_id VARCHAR,
    title VARCHAR,
    abstract TEXT,
    url_abs VARCHAR,
    url_pdf VARCHAR,
    proceeding VARCHAR,
    authors TEXT[],
    tasks TEXT[],
    date DATE,
    methods JSONB,
    embedding REAL[]
);
ALTER TABLE paperwithcode ADD PRIMARY KEY (paper_url);

CREATE TABLE paperwithcode_bge (
    paper_url VARCHAR Primary key,
    arxiv_id VARCHAR,
    title VARCHAR,
    abstract TEXT,
    url_abs VARCHAR,
    url_pdf VARCHAR,
    proceeding VARCHAR,
    authors TEXT[],
    tasks TEXT[],
    date DATE,
    methods JSONB,
    embedding REAL[]
);

CREATE TABLE paperwithcode_t (
    id SERIAL PRIMARY KEY,
    paper_url VARCHAR,
    paper_title VARCHAR,
    paper_arxiv_id VARCHAR,
    paper_url_abs VARCHAR,
    paper_url_pdf VARCHAR,
    repo_url VARCHAR,
    is_official BOOLEAN,
    mentioned_in_paper BOOLEAN,
    mentioned_in_github BOOLEAN,
    framework VARCHAR,
    embedding REAL[]
);
CREATE TABLE paperwithcode_t_bge (
    id SERIAL PRIMARY KEY,
    paper_url VARCHAR,
    paper_title VARCHAR,
    paper_arxiv_id VARCHAR,
    paper_url_abs VARCHAR,
    paper_url_pdf VARCHAR,
    repo_url VARCHAR,
    is_official BOOLEAN,
    mentioned_in_paper BOOLEAN,
    mentioned_in_github BOOLEAN,
    framework VARCHAR,
    embedding REAL[]
);

grant all privileges on table paperwithcode_t to rayrole;
grant all privileges on table paperwithcode to rayrole;
grant all privileges on table paperwithcode_bge to rayrole;

GRANT ALL ON ALL TABLES in schema public to rayrole;
GRANT ALL ON ALL sequences in schema public to rayrole;
GRANT ALL ON ALL functions in schema public to rayrole;
GRANTGRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA schema_name TO your_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA schema_name TO your_user;
CREATE INDEX paperwithcode_idx on paperwithcode using hnsw (embedding) with (dims=768, m=8);
CREATE INDEX paperwithcode_t_idx on paperwithcode_t using hnsw (embedding) with (dims=768, m=8);
CREATE INDEX paperwithcode_t_bge_eidx ON paperwithcode_t_bge USING hnsw(embedding) WITH (maxelements=200000, dims=1024, m=32, efconstruction=128, efsearch=64);
CREATE INDEX paperwithcode_t_bge_cidx ON paperwithcode_t_bge USING hnsw(embedding ann_cos_ops) WITH (maxelements=200000, dims=1024, m=32, efconstruction=128, efsearch=64);
CREATE INDEX paperwithcode_t_bge_midx ON paperwithcode_t_bge USING hnsw(embedding ann_manhattan_ops) WITH (maxelements=200000, dims=1024, m=32, efconstruction=128, efsearch=64);
CREATE INDEX paperwithcode_bge_eidx ON paperwithcode_bge USING hnsw(embedding) WITH (maxelements=400000, dims=1024, m=32, efconstruction=128, efsearch=64);
CREATE INDEX paperwithcode_bge_cidx ON paperwithcode_bge USING hnsw(embedding ann_cos_ops) WITH (maxelements=200000, dims=1024, m=32, efconstruction=128, efsearch=64);
CREATE INDEX paperwithcode_bge_midx ON paperwithcode_bge USING hnsw(embedding ann_manhattan_ops) WITH (maxelements=200000, dims=1024, m=32, efconstruction=128, efsearch=64);
------------------------------------------
vector db version
CREATE TABLE paperwithcode_t_v (
    id SERIAL PRIMARY KEY,
    paper_url VARCHAR,
    paper_title VARCHAR,
    paper_arxiv_id VARCHAR,
    paper_url_abs VARCHAR,
    paper_url_pdf VARCHAR,
    repo_url VARCHAR,
    is_official BOOLEAN,
    mentioned_in_paper BOOLEAN,
    mentioned_in_github BOOLEAN,
    framework VARCHAR,
    embedding vector(1024)
);

CREATE TABLE paperwithcode_v (
    paper_url VARCHAR Primary key,
    arxiv_id VARCHAR,
    title VARCHAR,
    abstract TEXT,
    url_abs VARCHAR,
    url_pdf VARCHAR,
    proceeding VARCHAR,
    authors TEXT[],
    tasks TEXT[],
    date DATE,
    methods JSONB,
    embedding vector(1024)
);
\#get dim of array
select cardinality(embedding::real[]) from paperwithcode_t_v limit 1;
raydb=# select cardinality(embedding::real[]) from paperwithcode_t_bge limit 1;
 cardinality
-------------
        1024
\#upgrade extension
vectordb=# select * from pg_available_extensions where name = 'vector' ;
  name  | default_version | installed_version |                       comment
--------+-----------------+-------------------+------------------------------------------------------
 vector | 0.5.0           | 0.4.4             | vector data type and ivfflat and hnsw access methods
(1 row)
alter extension vector update to '0.5.0';
vectordb=# alter extension vector update;
ERROR:  function "l1_distance" already exists with same argument types
NOTICE:  hnsw graph no longer fits into maintenance_work_mem after 202478 tuples
DETAIL:  Building will take significantly more time.
HINT:  Increase maintenance_work_mem to speed up builds.
#### During index rebuild, it needs large memory , pls maintenance_work_mem = 1024MB
L2 distance
CREATE INDEX paperwithcode_t_v_eidx ON paperwithcode_t_v USING ivfflat (embedding vector_l2_ops) WITH (lists = 448);
Inner product
CREATE INDEX paperwithcode_t_v_iidx ON paperwithcode_t_v USING ivfflat (embedding vector_ip_ops) WITH (lists = 448);
Cosine distance
CREATE INDEX paperwithcode_t_v_cidx ON paperwithcode_t_v USING ivfflat (embedding vector_cosine_ops) WITH (lists = 448);
sqrt(388513)=624
CREATE INDEX paperwithcode_v_eidx ON paperwithcode_v USING ivfflat (embedding vector_l2_ops) WITH (lists = 624);
CREATE INDEX paperwithcode_v_iidx ON paperwithcode_v USING ivfflat (embedding vector_ip_ops) WITH (lists = 624);
CREATE INDEX paperwithcode_v_cidx ON paperwithcode_v USING ivfflat (embedding vector_cosine_ops) WITH (lists = 624);
Create the index after the table has some data
Choose an appropriate number of lists - a good place to start is rows / 1000 for up to 1M rows and sqrt(rows) for over 1M rows
When querying, specify an appropriate number of probes (higher is better for recall, lower is better for speed) - a good place to start is sqrt(lists)
SET LOCAL ivfflat.probes = 25;
SET  ivfflat.probes = 25;
SELECT phase, tuples_done, tuples_total FROM pg_stat_progress_create_index;
CREATE INDEX paperwithcode_v_ehdx ON paperwithcode_v USING hnsw (embedding vector_l2_ops) WITH (m = 32, ef_construction = 128);
CREATE INDEX paperwithcode_v_ihdx ON paperwithcode_v USING hnsw (embedding vector_ip_ops) WITH (m = 32, ef_construction = 128);
CREATE INDEX paperwithcode_v_chdx ON paperwithcode_v USING hnsw (embedding vector_cosine_ops) WITH (m = 32, ef_construction = 128);

m - the max number of connections per layer (16 by default)
ef_construction - the size of the dynamic candidate list for constructing the graph (64 by default)
Almost same as pg_embedding
CREATE INDEX ON items USING hnsw (embedding vector_l2_ops) WITH (m = 16, ef_construction = 64);
SET hnsw.ef_search = 100;
SELECT id, content FROM items, plainto_tsquery('hello search') query
    WHERE textsearch @@ query ORDER BY ts_rank_cd(textsearch, query) DESC LIMIT 5;
set_enable_seqscan_stmt = sqlalchemy.text("SET enable_seqscan = off")
session.execute(set_enable_seqscan_stmt)
set_ivfflat_probes_stmt = sqlalchemy.text("SET ivfflat.probes = 25")
session.execute(set_ivfflat_probes_stmt)
set_hnsw_ef_search_stmt = sqlalchemy.text("SET hnsw.ef_search = 100")
session.execute(set_hnsw_ef_search_stmt)
----------------------------------
发生异常: ImportError
/lib/aarch64-linux-gnu/libp11-kit.so.0: undefined symbol: ffi_type_pointer, version LIBFFI_BASE_7.0
  File "/opt/workspace/app/visual-embeddings/loadDB.py", line 45, in __init__
    self.engine = create_engine(self.DBCONNECTION_STRING)
  File "/opt/workspace/app/visual-embeddings/loadDB.py", line 130, in <module>
    manager = PaperWithCodeManager('/opt/workspace/aibase/test.json', embeddings_func)
ImportError: /lib/aarch64-linux-gnu/libp11-kit.so.0: undefined symbol: ffi_type_pointer, version LIBFFI_BASE_7.0
https://blog.csdn.net/qq_38606680/article/details/129118491
cd /opt/workspace/miniconda3/envs/mlearn/lib
(mlearn) ray@jethome:/opt/workspace/miniconda3/envs/mlearn/lib$ ls -lrt libff*
-rw-rw-r-- 4 <USER> <GROUP> 114624 May 10 20:02 libffi.a
-rwxrwxr-x 4 <USER> <GROUP> 104400 May 10 20:02 libffi.so.8.1.2
lrwxrwxrwx 1 ray ray     15 Aug 24 03:41 libffi.so -> libffi.so.8.1.2
lrwxrwxrwx 1 ray ray     15 Aug 24 03:41 libffi.8.so -> libffi.so.8.1.2
lrwxrwxrwx 1 ray ray     15 Aug 24 03:41 libffi.7.so -> libffi.so.8.1.2
lrwxrwxrwx 1 ray ray     15 Aug 24 03:41 libffi.so.8 -> libffi.so.8.1.2
lrwxrwxrwx 1 ray ray     15 Aug 24 03:41 libffi.so.7 -> libffi.so.8.1.2
Changed to
(mlearn) ray@jethome:/opt/workspace/miniconda3/envs/mlearn/lib$ ls -lrt libff*
-rw-rw-r-- 4 <USER> <GROUP> 114624 May 10 20:02 libffi.a
-rwxrwxr-x 4 <USER> <GROUP> 104400 May 10 20:02 libffi.so.8.1.2
lrwxrwxrwx 1 ray ray     15 Aug 24 03:41 libffi.8.so -> libffi.so.8.1.2
lrwxrwxrwx 1 ray ray     15 Aug 24 03:41 libffi.so.8 -> libffi.so.8.1.2
lrwxrwxrwx 1 ray ray     38 Aug 26 14:22 libffi.so.7.1.0 -> /lib/aarch64-linux-gnu/libffi.so.7.1.0
lrwxrwxrwx 1 ray ray     15 Aug 26 14:23 libffi.7.so -> libffi.so.7.1.0
lrwxrwxrwx 1 ray ray     15 Aug 26 14:24 libffi.so.7 -> libffi.so.7.1.0
lrwxrwxrwx 1 ray ray     15 Aug 26 14:24 libffi.so -> libffi.so.7.1.0

dstat -t -c -d -D dm-0 --disk-tps --disk-util --aio -m -g -s -l -n -N wlan0 --tcp 
```

Some of Postgresql DB command

```SQL
CREATE OR REPLACE FUNCTION grant_connect_to_all_databases(role_name text) RETURNS voi               d AS $$
DECLARE
    db_name text;
BEGIN
    FOR db_name IN
        SELECT datname
        FROM pg_database
        WHERE datistemplate = false
          AND datname != 'momdb'  -- exclude momdb
          AND datname != current_database()  -- exclude the current database
    LOOP
        EXECUTE format('GRANT CONNECT ON DATABASE %I TO %I', db_name, role_name);
    END LOOP;
END;
$$ LANGUAGE plpgsql;
SELECT grant_connect_to_all_databases('momrole');
 CREATE OR REPLACE FUNCTION grant_permissions_on_all_databases(role_name text) RETURNS                void AS $$
DECLARE
    db_name text;
BEGIN
    FOR db_name IN
        SELECT datname
        FROM pg_database
        WHERE datistemplate = false
          AND datname != current_database()  -- exclude the current database
    LOOP
        -- Connect to each database
        PERFORM dblink_exec('dbname=' || quote_ident(db_name),
            format('
                -- Grant usage on schema
                GRANT USAGE ON SCHEMA public TO %I;
                -- Grant select on all tables
                GRANT SELECT ON ALL TABLES IN SCHEMA public TO %I;
                -- Grant select on future tables
                ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO %I;
            ', role_name, role_name, role_name)
        );
    END LOOP;
END;
$$ LANGUAGE plpgsql;
SELECT grant_permissions_on_all_databases('momrole');
CREATE EXTENSION IF NOT EXISTS dblink;
SELECT grant_permissions_on_all_databases('momrole');
CREATE OR REPLACE FUNCTION check_role_permissions(role_name text) RETURNS TABLE (
    database_name text,
    schema_name text,
    object_type text,
    object_name text,
    privileges text
) AS $$
DECLARE
    db_name text;
    result_row record;
BEGIN
    FOR db_name IN SELECT datname FROM pg_database WHERE datistemplate = false LOOP
        FOR result_row IN
            SELECT * FROM dblink(format('dbname=%I', db_name),
                format('
                    -- Database-level privileges
                    SELECT current_database() AS database_name,
                           NULL::text AS schema_name,
                           ''DATABASE''::text AS object_type,
                           current_database() AS object_name,
                           pg_catalog.has_database_privilege(%L, current_database(), ''CREATE'')::text || ''/'' ||
                           pg_catalog.has_database_privilege(%L, current_database(), ''CONNECT'')::text || ''/'' ||
                           pg_catalog.has_database_privilege(%L, current_database(), ''TEMPORARY'')::text || 
                           '' (CREATE/CONNECT/TEMPORARY)'' AS privileges
                    UNION ALL
                    -- Schema-level privileges
                    SELECT current_database(),
                           nspname,
                           ''SCHEMA'',
                           nspname,
                           pg_catalog.has_schema_privilege(%L, nspname, ''CREATE'')::text || ''/'' ||
                           pg_catalog.has_schema_privilege(%L, nspname, ''USAGE'')::text || 
                           '' (CREATE/USAGE)''
                    FROM pg_namespace
                    UNION ALL
                    -- Table-level privileges
                    SELECT current_database(),
                           table_schema,
                           ''TABLE'',
                           table_name,
                           pg_catalog.has_table_privilege(%L, table_schema || ''.'' || table_name, ''SELECT'')::text || ''/'' ||
                           pg_catalog.has_table_privilege(%L, table_schema || ''.'' || table_name, ''INSERT'')::text || ''/'' ||
                           pg_catalog.has_table_privilege(%L, table_schema || ''.'' || table_name, ''UPDATE'')::text || ''/'' ||
                           pg_catalog.has_table_privilege(%L, table_schema || ''.'' || table_name, ''DELETE'')::text || 
                           '' (SELECT/INSERT/UPDATE/DELETE)''
                    FROM information_schema.tables
                    WHERE table_type = ''BASE TABLE'' AND table_schema NOT IN (''pg_catalog'', ''information_schema'')
                ', role_name, role_name, role_name, role_name, role_name, role_name, role_name, role_name, role_name)
            ) AS t(database_name text, schema_name text, object_type text, object_name text, privileges text)
        LOOP
            database_name := result_row.database_name;
            schema_name := result_row.schema_name;
            object_type := result_row.object_type;
            object_name := result_row.object_name;
            privileges := result_row.privileges;
            RETURN NEXT;
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
-- Use the function
SELECT * FROM check_role_permissions('momrole');
-- list functions 
\df
-- see full definition of a function
\sf func
\c flowise
-- Check database-level permissions
SELECT has_database_privilege('momrole', 'flowise', 'CONNECT');
-- Check schema-level permissions
SELECT has_schema_privilege('momrole', 'flowsc', 'USAGE');
-- Check table-level permissions (replace table_name with an actual table name)
SELECT has_table_privilege('momrole', 'flowsc.table_name', 'SELECT');
-- Grant connect permission on the database
GRANT CONNECT ON DATABASE flowise TO momrole;
-- Grant usage permission on the schema
GRANT USAGE ON SCHEMA flowsc TO momrole;
-- Grant select permission on all tables in the schema
GRANT SELECT ON ALL TABLES IN SCHEMA flowsc TO momrole;
-- Grant select permission on future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA flowsc GRANT SELECT ON TABLES TO momrole;
```

Vector 距离公式

![[Notion/AI/🦜🦜🦜JETSON memo/attachments/Untitled.png|Untitled.png]]
![[Untitled 1.png]]

```JavaScript
\#Jaccard
https://blog.csdn.net/weixin_43272781/article/details/113757298
import torch
# 创建两个2维张量（矩阵）
matrix1 = torch.tensor([[1, 2, 3], [4, 5, 6]], dtype=torch.float32)
matrix2 = torch.tensor([[4, 5, 6], [7, 8, 9]], dtype=torch.float32)
# 计算交集
intersection = torch.sum(torch.min(matrix1, matrix2))
# 计算并集
union = torch.sum(torch.max(matrix1, matrix2))
# 计算Jaccard相似度
jaccard_similarity = intersection / union
print("Jaccard Similarity:", jaccard_similarity.item())

https://docs.streamlit.io/library/api-reference/data/st.dataframe
/opt/workspace/app/visual-embeddings/simplesearchTitle.py
https://github.com/streamlit/streamlit/issues/732
# streamlit memory issue
https://docs.streamlit.io/library/advanced-features/caching

\#Pearson
static dist_t cosine_dist_impl(coord_t const* ax, coord_t const* bx, size_t dim)
{
	dist_t 		suma = 0.0;
	dist_t 		sumb = 0.0;
	dist_t 		meana, meanb;
	dist_t 		numerator = 0.0;
	dist_t 		denoma = 0.0;
	dist_t 		denomb = 0.0;
	// 计算平均值
	for (size_t i = 0; i < dim; i++)
	{
		suma += ax[i];
		sumb += bx[i];
	}
	meana = suma / dim;
	meanb = sumb / dim;
	// 计算皮尔逊相关系数
	for (size_t i = 0; i < dim; i++)
	{
		dist_t a = ax[i] - meana;
		dist_t b = bx[i] - meanb;
		numerator += a * b;
		denoma += a * a;
		denomb += b * b;
	}
	return numerator / sqrt(denoma * denomb);
}
```

# 如何在systemctl启动python 程序

conda env list
/opt/workspace/miniconda3/envs/mlearn

```JavaScript
https://unix.stackexchange.com/questions/436791/limit-total-memory-usage-for-multiple-instances-of-systemd-service
root@jethome:/lib/systemd/system# cat paperwithcode_limit.slice
[Unit]
Description=Slice that limits memory for all my services
[Slice]
# MemoryHigh works only in "unified" cgroups mode, NOT in "hybrid" mode
MemoryHigh=500M
# MemoryMax works in "hybrid" cgroups mode, too
MemoryMax=600M
root@jethome:/lib/systemd/system# cat paperwithcode.service
[Unit]
Description=Web interface for Paper With Code
After=postgresql.service
[Service]
User=ray
Environment="PATH=/opt/workspace/miniconda3/envs/mlearn/bin:$PATH"
ExecStart=/opt/workspace/miniconda3/envs/mlearn/bin/streamlit run /opt/workspace/app/visual-embeddings/simplesearch.py
WorkingDirectory=/opt/workspace/app/visual-embeddings/
MemoryMax=1024M
Restart=on-failure
RestartSec=5s
Slice=paperwithcode_limit.slice
[Install]
WantedBy=multi-user.target
systemctl daemon-reload
systemctl start your_service_name
systemctl enable your_service_name
```

---

ZeroTier Setup

```JavaScript
VPN zerotier-one
https://www.zerotier.com/download/
https://github.com/zerotier/ZeroTierOne
curl -s https://install.zerotier.com | sudo bash
*** Enabling and starting ZeroTier service...
Synchronizing state of zerotier-one.service with SysV service script with /lib/systemd/systemd-sysv-install.
Executing: /lib/systemd/systemd-sysv-install enable zerotier-one
*** Waiting for identity generation...
*** Success! You are ZeroTier address [ 93f40932cc ].
\#future you can use this to upgrade
sudo apt-get install --upgrade zerotier-one
Network ID
272f5eae16151c55

https://docs.zerotier.com/getting-started/getting-started
sudo zerotier-cli info
sudo zerotier-cli join 272f5eae16151c55
(base) ray@jethome:/opt/install$ sudo zerotier-cli listnetworks
200 listnetworks <nwid> <name> <mac> <status> <type> <dev> <ZT assigned ips>
200 listnetworks 272f5eae16151c55 netai-home 56:8f:e1:1f:9c:92 OK PRIVATE ztre4uxmsd *************/16
```

---

# LaTEX Support

```Python
https://qiita.com/momomo_rimoto/items/ea83f6e703bceff69914
https://qiita.com/buran5884/items/f88d120833d6e1d624f2
\#download and install
wget http://ftp.jaist.ac.jp/pub/CTAN/systems/texlive/tlnet/install-tl-unx.tar.gz
sudo ./install-tl
Welcome to TeX Live!

See /usr/local/texlive/2023/index.html for links to documentation.
The TeX Live web site (https://tug.org/texlive/) contains any updates and corrections. TeX Live is a joint project of the TeX user groups around the world; please consider supporting it by joining the group best for you. The list of groups is available on the web at https://tug.org/usergroups.html.

Add /usr/local/texlive/2023/texmf-dist/doc/man to MANPATH.
Add /usr/local/texlive/2023/texmf-dist/doc/info to INFOPATH.
Most importantly, add /usr/local/texlive/2023/bin/aarch64-linux
to your PATH for current and future sessions.
Logfile: /usr/local/texlive/2023/install-tl.log
\#add environment parameters
vi .bashrc
export PATH="$PATH:/usr/local/texlive/2023/bin/aarch64-linux"
export MANPATH="$MANPATH:/usr/local/texlive/2023/texmf-dist/doc/man"
export INFOPATH="$INFOPATH:/usr/local/texlive/2023/texmf-dist/doc/info"
\#in vscode
install LaTex Workshop
```

# Modular and Moko

```Python
Modular installation
https://developer.modular.com/download
apt-get install -y apt-transport-https &&
  keyring_location=/usr/share/keyrings/modular-installer-archive-keyring.gpg &&
  curl -1sLf 'https://dl.modular.com/bBNWiLZX5igwHXeu/installer/gpg.0E4925737A3895AD.key' |  gpg --dearmor >> ${keyring_location} &&
  curl -1sLf 'https://dl.modular.com/bBNWiLZX5igwHXeu/installer/config.deb.txt?distro=debian&codename=wheezy' > /etc/apt/sources.list.d/modular-installer.list &&
  apt-get update &&
  apt-get install -y modular

(mlearn) ray@jethome:~$ modular auth mut_6e637632606a498c8a374238b4f4bff5
(mlearn) ray@jethome:~$ modular install mojo
modular: error: no packages found for the current target hardware - please run `modular host-info` and file a ticket at https://github.com/modularml/mojo with your hardware information
(mlearn) ray@jethome:~$ modular host-info
  Host Information
  ================
  Target Triple: arm64-unknown-linux
  CPU: generic
  CPU Features: crc, crypto, fp-armv8, lse, neon
```

# lightbgm

```Python
install lightbgm in jetson
sudo apt install libboost-all-dev
sudo apt install opencl-headers
sudo apt install clinfo
sudo apt install ocl-icd-opencl-dev
git clone --recursive https://github.com/microsoft/LightGBM
cd LightGBM
mkdir build
cd build
cmake -DUSE_GPU=1 -DOpenCL_LIBRARY=/usr/lib/aarch64-linux-gnu/libOpenCL.so.1 -DOpenCL_INCLUDE_DIR=/usr/local/cuda/include/ ..
make -j4
```

# To make ChatGLM and LLAMA working in Jetson

```JavaScript
ValueError: Tokenizer class LlamaTokenizer does not exist or is not currently imported.
For now to resolve this error, need to manually update the tokenizer_class to "LlamaTokenizer" in tokenizer_config.json.
\#the reason is transformer version is old 
You must install 
pip install transformers==4.33.2
pip install sentencepiece
You will find ChatGLM not working
return torch.distributed.is_initialized() and strtobool(os.environ.get("ACCELERATE_USE_FSDP", "False")) == 1
AttributeError: module 'torch.distributed' has no attribute 'is_initialized'
\#The reason is jetson not support torch distrubtion mode, disable it
# fix below 
vi /opt/workspace/miniconda3/envs/mom/lib/python3.8/site-packages/transformers/modeling_utils.py
117 def is_fsdp_enabled():
 118     return False
 119 #    return torch.distributed.is_initialized() and strtobool(os.environ.get("ACCELERATE_USE_FSDP",      "False")) == 1
```

---

# Qwen

```JavaScript
# reference LLM test code
/opt/workspace/mom/LLMCheckTest.ipynb
git clone https://huggingface.co/Qwen/Qwen-7B-Chat
git clone -b v1.0.8 https://github.com/Dao-AILab/flash-attention
cd flash-attention && pip install .
pip install csrc/layer_norm  -> it is big. i changed ./csrc/layer_norm/setup.py :line 56         return nvcc_extra_args + ["--threads", "2"] 
\#Very slow while compile
https://github.com/Dao-AILab/flash-attention/issues/131
export MAX_JOBS=6 
pip install csrc/layer_norm 
pip install csrc/rotary
pip install tiktoken
pip install transformers_stream_generator
pip install sse_starlette
# from transformers.generation import GenerationConfig
from transformers import AutoModelForCausalLM, AutoTokenizer
model_path = "/opt/workspace/aibase/Qwen-7B-Chat"
tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
model = AutoModelForCausalLM.from_pretrained(model_path,trust_remote_code=True).half().cuda().eval()
model.eval()
# Specify hyperparameters for generation. But if you use transformers>=4.32.0, there is no need to do this.
# model.generation_config = GenerationConfig.from_pretrained("Qwen/Qwen-7B-Chat", trust_remote_code=True)
response, history = model.chat(tokenizer, "你好", history=None)
print(response)
# 2nd dialogue turn
response, history = model.chat(tokenizer, "给我讲一个年轻人奋斗创业最终取得成功的故事。", history=history)
print(response)
python cli_demo.py
-----------------------------------------------------------------
\#play like openai api
# if you have all then no need to install
pip install accelerate
pip install fastapi uvicorn openai "pydantic>=2.3.0" sse_starlette
python openai_api.py -c {path}
python openai_api.py -c /opt/aibase/Qwen-7B-Chat --server-port 8002 --server-name 0.0.0.0
import openai
openai.api_base = "http://localhost:8000/v1"
openai.api_key = "none"
# create a request activating streaming response
for chunk in openai.ChatCompletion.create(
    model="Qwen",
    messages=[
        {"role": "user", "content": "你好"}
    ],
    stream=True 
    # Specifying stop words in streaming output format is not yet supported and is under development.
):
    if hasattr(chunk.choices[0].delta, "content"):
        print(chunk.choices[0].delta.content, end="", flush=True)
---------------------------------------------------------------------------------
# create a request not activating streaming response
response = openai.ChatCompletion.create(
    model="Qwen",
    messages=[
        {"role": "user", "content": "你好"}
    ],
    stream=False,
    stop=[] # You can add custom stop words here, e.g., stop=["Observation:"] for ReAct prompting.
)
print(response.choices[0].message.content)
---------------------------------------------------------------------------------
"""
You are an expert who will judge people sentiment while people talking and 
    also help to tranlate content into english if it is not in english.:
   マサオは高校の頃女流ルーダー大会1位でプロデビューしました。
    Above is the content; please try to extract all data points from the content above 
    and export in a JSON array format:
{
    "Sentiment": "the content sentiment if it is positive or negative",
    "Translate": "The content translated into English",
}
    Now please extract details from the content  and export in a JSON array format, 
    return ONLY the JSON array:
"""
---------------------------------------------------------------------------------
# create a request not activating streaming response
response = openai.ChatCompletion.create(
    model="Qwen",
    messages=[
        {"role": "user", "content": """
         You are an expert who will judge people sentiment while people talking and 
    also help to tranlate content into english if it is not in english.:
   I am feeling tired

    Above is the content; please try to extract all data points from the content above 
    and export in a JSON array format:
{
    "Sentiment": "the content sentiment if it is positive or negative",
    "Translate": "The content translated into English",
}
    Now please extract details from the content  and export in a JSON array format, 
    return ONLY the JSON array:
         """          
        }
    ],
    stream=False,
    stop=[] # You can add custom stop words here, e.g., stop=["Observation:"] for ReAct prompting.
)
print(response.choices[0].message.content)
---------------------------------------------------------------------------------
# Use 14B with Int4
https://huggingface.co/Qwen/Qwen-14B-Chat-Int4
Need to install 
pip install auto-gptq optimum
#因为是arm + cuda 11.4 , 所以只有0.3.1.
# Tried below , not working so we give up
git clone https://github.com/PanQiWei/AutoGPTQ.git && cd AutoGPTQ
pip install -v .
model = AutoModelForCausalLM.from_pretrained(
    "Qwen/Qwen-7B-Chat",
     device_map="auto",
     trust_remote_code=True,
     use_cache_quantization=True,
     use_cache_kernel=True,
     use_flash_attn=False
)

python openai_api.py -c /opt/aibase/Qwen-14B-Chat-Int4 --server-port 8002 --server-name 0.0.0.0

CUDA error (/databank/workspace/aibase/flash-attention/csrc/flash_attn/src/fmha_fwd_launch_template.h:89): no kernel image is available for execution on the device
https://qiita.com/k_ikasumipowder/items/1142dadba01b42ac6012
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
最终发现FlashAttention2 根本不支持 Jetson AGX Orion 框架
```

# GO Environment Setup

```JavaScript
sudo wget https://go.dev/dl/go1.21.3.linux-arm64.tar.gz
sudo wget https://go.dev/dl/go1.22.5.linux-arm64.tar.gz
sudo wget https://go.dev/dl/go1.22.5.linux-amd64.tar.gz
cd /opt/workspace
mkdir -p go/src
cd /opt/install
tar -C /usr/local -xzf go1.21.3.linux-arm64.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' | sudo tee /etc/profile.d/golang.sh
echo "export GOPATH=/opt/workspace/go/src" >> /etc/profile.d/golang.sh
echo "export GOROOT=/usr/local/go" >> /etc/profile.d/golang.sh
exec $SHELL -l
update-alternatives --install /usr/bin/go go /usr/local/go/bin/go 100 --slave /usr/bin/gofmt gofmt /usr/local/go/bin/gofmt
update-alternatives --display go

go install github.com/go-delve/delve/cmd/dlv@latest
go install -v honnef.co/go/tools/cmd/staticcheck@latest
go install -v golang.org/x/tools/gopls@latest
compiled stuffs will be under /opt/workspace/go/src
you can move to /usr/local/go
```

# EmotiVoice Play

```JavaScript
github
https://github.com/netease-youdao/EmotiVoice/tree/main
pretrained file
1. git lfs clone https://huggingface.co/WangZeJun/simbert-base-chinese WangZeJun/simbert-base-chinese
2. https://drive.google.com/drive/folders/1y6Xwj_GG9ulsAonca_unSGbJ4lxbNymM
conda create -n EmotiVoice python=3.8 -y
conda activate EmotiVoice
pip install torch torchaudio
pip install numpy numba scipy transformers soundfile yacs g2p_en jieba pypinyin

(mom) ray@jethome:/opt/workspace/app/EmotiVoice$ python frontend_en.py data/my_text.txt > data/my_text_for_tts.txt
[nltk_data] Downloading package averaged_perceptron_tagger to
[nltk_data]     /home/<USER>/nltk_data...
[nltk_data]   Unzipping taggers/averaged_perceptron_tagger.zip.
[nltk_data] Downloading package cmudict to /home/<USER>/nltk_data...
[nltk_data]   Unzipping corpora/cmudict.zip.
(mom) ray@jethome:/opt/workspace/app/EmotiVoice$ python inference_am_vocoder_joint.py --logdir prompt_tts_open_source_joint --config_folder config/joint --checkpoint g_00140000 --test_file $TEXT
run!
Traceback (most recent call last):
  File "inference_am_vocoder_joint.py", line 156, in <module>
    main(args, config)
  File "inference_am_vocoder_joint.py", line 66, in main
    style_encoder.load_state_dict(model_ckpt)
  File "/opt/workspace/miniconda3/envs/mom/lib/python3.8/site-packages/torch/nn/modules/module.py", line 2041, in load_state_dict
    raise RuntimeError('Error(s) in loading state_dict for {}:\n\t{}'.format(
RuntimeError: Error(s) in loading state_dict for StyleEncoder:
        Unexpected key(s) in state_dict: "bert.embeddings.position_ids".

TEXT=data/inference/text
python inference_am_vocoder_joint.py \
--logdir prompt_tts_open_source_joint \
--config_folder config/joint \
--checkpoint g_00140000 \
--test_file $TEXT
```

# libgomp issue

```Python
https://github.com/opencv/opencv/issues/14884
libgomp-cc9055c7.so.1.0.0: cannot allocate memory in static TLS block
#要下载jetson的专用的pytorch tensorflow
https://developer.download.nvidia.com/compute/redist/jp/v512/
pip install tensorflow-2.12.0+nv23.06-cp38-cp38-linux_aarch64.whl
setup profile as well as .bashrc
(mlearn) ray@jethome:/etc/profile.d$ cat cuda_env.sh
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/local/cuda/lib64
export PATH=$PATH:/usr/local/cuda/bin
export CUDA_HOME=/usr/local/cuda
export LD_PRELOAD=/opt/workspace/miniconda3/envs/mlearn/lib/python3.8/site-packages/scikit_learn.libs/libgomp-d22c30c5.so.1.0.0
remove all LD_PRELOAD and restart Server
# test and verify, issue gone, Before you load tensorflow , run below import sklearn firstly
from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
# 测试 TensorFlow 是否正常工作
# tf.test.gpu_device_name()
# 使用 scikit-learn 的一个简单示例
iris = load_iris()
X_train, X_test, y_train, y_test = train_test_split(iris.data, iris.target, test_size=0.3, random_state=42)
clf = RandomForestClassifier(n_estimators=10)
clf.fit(X_train, y_train)
print("Accuracy:", clf.score(X_test, y_test))
```

# Stirling PDF

## Install docker compose

```JavaScript
https://gist.github.com/imneonizer/********************************
sudo apt install docker-compose
(openresearcher) ray@jethome:/databank/app/OpenResearcher$ sudo usermod -aG docker ray
(openresearcher) ray@jethome:/databank/app/OpenResearcher$ id ray
uid=1000(ray) gid=1000(ray) groups=1000(ray),4(adm),24(cdrom),27(sudo),29(audio),30(dip),44(video),46(plugdev),104(render),116(i2c),120(lpadmin),135(gdm),999(gpio),996(weston-launch),123(sambashare),994(docker),1001(jtop),992(ollama)
```

```JavaScript
cd /opt/workspace/app/Stirling-PDF/env
(base) ray@jethome:/opt/workspace/app/Stirling-PDF/env$ cat stirlingpdf.yaml
version: '3.3'
services:
  stirling-pdf:
    image: frooodle/s-pdf:latest
    ports:
      - '8080:8080'
    volumes:
      - /opt/workspace/app/Stirling-PDF/env/trainingData:/usr/share/tesseract-ocr/5/tessdata \#Required for extra OCR languages
      - /opt/workspace/app/Stirling-PDF/env/extraConfigs:/configs
      - /opt/workspace/app/Stirling-PDF/env/customFiles:/customFiles/
      - /opt/workspace/app/Stirling-PDF/env/logs:/logs/
    environment:
      - DOCKER_ENABLE_SECURITY=false
(base) ray@jethome:/usr/lib/systemd/system$ cat /usr/lib/systemd/system/stirlingpdf.service
[Unit]
Description=Stirling PDF Docker Compose
Requires=docker.service
After=docker.service
[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/workspace/app/Stirling-PDF/env
ExecStart=/usr/bin/docker-compose -f stirlingpdf.yaml up -d
ExecStop=/usr/bin/docker-compose -f stirlingpdf.yaml down
ExecStartPost=/usr/bin/docker-compose -f stirlingpdf.yaml logs
ExecStartPost=/usr/bin/docker-compose -f stirlingpdf.yaml ps
[Install]
WantedBy=multi-user.target
```

# Install 个人金融软件 和 Ruby

```LaTeX
https://github.com/maybe-finance/maybe?

sudo apt update
sudo apt install ruby-full
sudo apt install -y build-essential libssl-dev libreadline-dev zlib1g-dev libyaml-dev libffi-dev libgdbm-dev libncurses5-dev automake libtool bison libffi-dev
sudo apt install -y libyaml-dev
gem install bundler
gem install rails
\#doc related to install rbenv and ruby
https://github.com/rbenv/rbenv
sudo apt install rbenv
# install ruby build
cd ~
rm -rf $(rbenv root)/plugins/ruby-build
git clone https://github.com/rbenv/ruby-build.git $(rbenv root)/plugins/ruby-build
cd $(rbenv root)/plugins/ruby-build && git pull
rbenv install --list
rbenv install 3.2.3
add below line to .bashrc
eval "$(/usr/bin/rbenv init - bash)"
reboot
rbenv global 3.2.3
rbenv versions
rbenv version
# or:
# rbenv local 3.1.2    # set the Ruby version for this directory
gem install bundler
gem install rails

git clone https://github.com/maybe-finance/maybe.git
# got below error
# (base) ray@jethome:/opt/workspace/app/maybe$ bundle install
\#rbenv: version `3.3.0' is not installed (set by /opt/workspace/app/maybe/.ruby-version)
rbenv install 3.3.0
rbenv global 3.3.0
\#confirm
rbenv versions
rbenv version
gem install bundler
gem install rails
cd maybe
cp .env.example .env
bundle install
=============================
# Install default configuration:
cp $(bundle exec i18n-tasks gem-path)/templates/config/i18n-tasks.yml config/
# Add an RSpec for missing and unused keys:
cp $(bundle exec i18n-tasks gem-path)/templates/rspec/i18n_spec.rb spec/
# Or for minitest:
cp $(bundle exec i18n-tasks gem-path)/templates/minitest/i18n_test.rb test/
Post-install message from rubyzip:
RubyZip 3.0 is coming!
=============================
\#Postgresql DB setup
postgres=# create database maybedb;
CREATE DATABASE
postgres=# create role mayberole;
CREATE ROLE
postgres=# grant connect on database maybedb to mayberole;
GRANT
postgres=# create user maybe password 'maybe\#123';
CREATE ROLE
postgres=# grant mayberole to maybe;
GRANT ROLE
postgres=# show search_path;
   search_path
-----------------
 "$user", public
(1 row)
postgres=# create tablespace maybetbsp location '/opt/postgresql/data/pg_tblspc';
CREATE TABLESPACE
postgres=# alter role mayberole set default_tablespace='maybetbsp';
ALTER ROLE
postgres=# alter role mayberole set search_path=maybesc,public;
ALTER ROLE
postgres=# \c maybedb postgres
psql (16.2 (Ubuntu 16.2-1.pgdg20.04+1), server 15.6 (Ubuntu 15.6-1.pgdg20.04+1))
You are now connected to database "maybedb" as user "postgres".
maybedb=# create schema maybesc;
CREATE SCHEMA
maybedb=# alter schema maybesc owner to maybe;
ALTER SCHEMA
maybedb=# alter database maybedb set search_path=maybesc,public;
ALTER DATABASE
\#open pg_hba.conf
local maybedb maybe md5
pg_ctl reload
maybedb=# \c maybedb maybe
Password for user maybe:
\#as we can't specify db, so did some change
postgres=# alter role mayberole nosuperuser;
ALTER ROLE
postgres=# alter role mayberole with CreateDB;
ALTER ROLE
#只能显示的grant
postgres=# alter role maybe createdb;
ALTER ROLE
\#change pg_hba.conf
local all maybe md5
pg_ctl reload
cat .env
# Database Configuration
DB_HOST=localhost
POSTGRES_PASSWORD='maybe\#123'
POSTGRES_USER=maybe
rails db:setup
Created database 'maybe_development'
Created database 'maybe_test'
Family created: The Maybe Family
User created: <EMAIL> for family: The Maybe Family
\#relogin 
bin/dev
Email: <EMAIL> Password: password
```

# After upgrade Jetson from 5 to 6

```Python
conda install python=3.10
pip install numpy torch-2.3.0-cp310-cp310-linux_aarch64.whl
pip install torchvision-0.18.0a0+6043bc2-cp310-cp310-linux_aarch64.whl
pip install torchaudio-2.3.0+952ea74-cp310-cp310-linux_aarch64.whl
need to upgrade other package as well
```

# Create one Jump Server tunnel

```JavaScript
\#1 enable sshd forward
/etc/ssh/sshd_config
GatewayPorts yes
AllowTcpForwarding yes
sudo systemctl restart sshd
\#2 start tunnel
tmux
ssh -R 0.0.0.0:8081:************:80 127.0.0.1
# check if tunnel is working
ss -tuln | grep 8081
ps aux | grep ssh
lsof -i -n -P | grep ssh
curl -v http://**************:8081
```

# FlashAttention Install

```JavaScript
https://github.com/Dao-AILab/flash-attention
https://github.com/Dao-AILab/flash-attention/issues/1146
https://arnon.dk/matching-sm-architectures-arch-and-gencode-for-various-nvidia-cards/
 it was due to the fact that gpu on jetson agx orin is of sm87 instead of sm80, 
 which is the structure of A100. I looked into setup.py and found the '-gencode' 
 option, maybe with this and 'code=sm_80' it points directly at sm_80, so sm_87 is not available. 


cd flash-attention/
vi setup.py
\#change code=m
cc_flag.append("arch=compute_80,code=sm_87")  #here , maybe we can try arch=compute_87 as well




pip install packaging
pip install ninja
python setup.py install
pip list|grep flash
pip list|grep tran

# Install build dependencies
pip install build wheel setuptools

# Build the wheel
python setup.py bdist_wheel
```

# Fix GLIBCXX_3.4.30 not find issue

```JavaScript
 ImportError: /databank/workspace/miniconda3/envs/openresearcher/bin/../lib/libstdc++.so.6: version GLIBCXX_3.4.30' not found (required by /opt/workspace/miniconda3/envs/openresearcher/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
 strings /databank/workspace/miniconda3/envs/openresearcher/lib/libstdc++.so.6 | grep GLIBCXX
@ we found os the library include it.
dpkg -L libstdc++6
(openresearcher) ray@jethome:/usr/lib$ strings /usr/lib/aarch64-linux-gnu/libstdc++.so.6.0.30|grep GLIBCXX_3.4.30
GLIBCXX_3.4.30
 so we enter
 (openresearcher) ray@jethome:/databank/workspace/miniconda3/envs/openresearcher/lib$ ls -lrt|grep libstdc
-rwxrwxr-x 10 <USER> <GROUP>  3934728 Jun  1  2022 libstdc++.so.6.0.29
lrwxrwxrwx  1 ray ray       19 Aug 20 09:36 libstdc++.so.6 -> libstdc++.so.6.0.29
lrwxrwxrwx  1 ray ray       19 Aug 20 09:36 libstdc++.so -> libstdc++.so.6.0.29
and
(openresearcher) ray@jethome:/databank/workspace/miniconda3/envs/openresearcher/lib$ rm -f libstdc++.so.6
(openresearcher) ray@jethome:/databank/workspace/miniconda3/envs/openresearcher/lib$ ln -sf /usr/lib/aarch64-linux-gnu/libstdc++.so.6.0.30 libstdc++.so.6
(openresearcher) ray@jethome:/databank/workspace/miniconda3/envs/openresearcher/lib$ ls -lrt|grep libstdc 
-rwxrwxr-x 10 <USER> <GROUP>  3934728 Jun  1  2022 libstdc++.so.6.0.29
lrwxrwxrwx  1 ray ray       19 Aug 20 09:36 libstdc++.so -> libstdc++.so.6.0.29
lrwxrwxrwx  1 ray ray       46 Aug 20 12:19 libstdc++.so.6 -> /usr/lib/aarch64-linux-gnu/libstdc++.so.6.0.3
```

# Install pipx

```SQL
pip install --user pipx
python -m pipx ensurepath
```

# Ollama

## Since 0.4.2
Support Nvidia jetson
```
curl -fsSL https://ollama.com/install.sh | sh
```
## Other reference
[[🦜🦜🦜MeetingMom Tool]]
https://github.com/ollama/ollama/blob/main/docs/api.md


```Python
# reference
https://www.reddit.com/r/ollama/comments/1c4zg15/does_anyone_know_how_to_change_where_your_models/
https://github.com/ollama/ollama/tree/main
https://www.gpu-mart.com/blog/custom-llm-models-with-ollama-modelfile
# install
curl -fsSL https://ollama.com/install.sh | sh
(base) [root@MONSTER:/etc/systemd/system]# cat /etc/systemd/system/ollama.service
[Unit]
Description=Ollama Service
After=network-online.target
[Service]
ExecStart=/usr/local/bin/ollama serve
User=ollama
Group=ollama
Restart=always
RestartSec=3
Environment="PATH=/opt/postgresql/bin:/home/<USER>/.juliaup/bin:/home/<USER>/.modular/pkg/packages.modular.com_mojo/bin:/home/<USER>/miniconda3/bin:/home/<USER>/miniconda3/condabin:/home/<USER>/.local/bin:/home/<USER>/bin:/usr/share/Modules/bin:/usr/lib64/ccache:/usr/local/bin:/usr/bin:/usr/local/sbin:/usr/sbin:/usr/local/cuda/bin:/usr/local/go/bin"
Environment="OLLAMA_HOST=0.0.0.0:8234"
Environment="OLLAMA_ORIGINS=*"
Environment="OLLAMA_KEEP_ALIVE=24h"
Environment="OLLAMA_NUM_PARALLEL=4"

[Install]
WantedBy=default.target
\#Donwload
ollama pull llama3.1
Default location
/usr/share/ollama
\#run
ollama run llama3.1
ollama run llama3.1:70b
ollama ps
\#to top model
crl+x or exit after 5min or restart ollama service
\#customize it
export OLLAMA_HOST=0.0.0.0:8234
ollama show llama2:latest --modelfile > myllama2.modelfile
ollama create myllama2 --file myllama2.modelfile
ollama run myllama2

```


#  Some Backend Service running 
```
(base) ray@jethome:/opt/workspace/app/slack-kb-agent$ tmux ls
1: 1 windows (created Sun Aug 11 10:33:19 2024)
2: 1 windows (created Mon Aug 12 11:59:00 2024)
4: 1 windows (created Tue Aug 13 15:30:48 2024)
1 → SSL tunnel to ************:80 pet camera
2 → Flowise pnmp start
3 → one is slace app the other is ngrok session, dont forget to register ngrok url in slack event 
```
## Establish SSH Tunnel
```
#1 enable sshd forward
/etc/ssh/sshd_config
GatewayPorts yes
AllowTcpForwarding yes
sudo systemctl restart sshd

#2 start tunnel
tmux
ssh -R 0.0.0.0:8081:************:80 127.0.0.1
ssh -R 0.0.0.0:2222:**************:22 127.0.0.1
ssh -R 0.0.0.0:2222:*************:22 ray@127.0.0.1

# check if tunnel is working
ss -tuln | grep 8081
ps aux | grep ssh
lsof -i -n -P | grep ssh
curl -v http://**************:8081
```
## Create socks proxy for Kaixin PC
```
ssh -D 0.0.0.0:1082 -N ray@**************
```

## Start slack agent 
```
Tmux

conda activate mlearn

python /opt/workspace/app/slack-kb-agent/app.py

```

## Start ngrok 
```
as free version , only can start one session . We need to setup config to make multiple tunnel

(base) ray@jethome:~/.config/ngrok$ cat ngrok.yml
version: "2"
authtoken: *************************************************
tunnels:
   slackagent:
     addr: 5003
     proto: http
     #hostname: jethome
     host_header: slackagent.jethome
   flowise:
     addr: 8300
     proto: http
     #hostname: jethome
     host_header: flowise.jethome

ngrok start --all
then open another tmux window
ngrok http 5003
ngrok start --all

Update events url

https://api.slack.com/apps/A07FXMYA8VA/event-subscriptions?
https://d696-240d-1a-4f3-9a00-86aa-e737-e160-2d1f7.ngrok-free.app
https://1415-240d-1a-4f3-9a00-87b5-88dc-cd10-a9ff.ngrok-free.app/slack/events

```

## Start Flowise
```
cd Flowise
pnpm start
```

## Start knowledge base tool
cd /opt/workspace/app/cursor/jina
cd backend
conda activate mlearn
python main_v2.py

cd fronend
./start-prod.sh
or
npm run dev
or
npx next dev -p 8505

-- use pm2 startit automatically
pm2 start "npx next start -p 8505" --name "jina-ai" -e NODE_ENV=development
pm2 start main_v2.py --name "jina-python" --interpreter /opt/workspace/miniconda3/envs/mlearn/bin/python
pm2 logs jina-ai
pm2 logs jina-python

---

--- BELOW IS ON JETHOME64
## PDF Translate
```
# conda activate fish-speech
cd /opt/app/PDFMathTranslate
source .venv/bin/activate
uv run pdf2zh -i
```
## LlamaCoder

```
cd /opt/app/llamacoder
npx next start -p 8505
```
## DeepSeek thinker
```
conda activate tools
python deepseek_handler_groq_aware_iterations.py
```

## search_server MCP server
```
npx dotenv-vault@latest keys

export DOTENV_KEY="dotenv://:<EMAIL>/vault/.env.vault?environment=development"

# uv run python -m src.search_server.server --port 8512 --transport sse
uv run search-server --transport sse --port 8511
```
<span style="background:#40a9ff">uv run search-server --transport sse --port 8511</span>
<span style="background:#40a9ff">uv run search-server --transport sse --port 8521</span>
## supergateway for mcp
```
cd /databank/app/cline/supergateway
# npx -y supergateway --port 8511 --stdio "uv --directory /opt/app/cline/search_server run search-server --transport stdio"
## npx -y supergateway --port 8513 --stdio "node /opt/app/cline/markdownify-mcp/dist/index.js"
npx -y supergateway@latest --port 8514 --stdio "env FIRECRAWL_API_KEY=fc-929b7041ace54704a654b408fdd1e43e npx -y firecrawl-mcp"
npx -y supergateway@latest --port 8515 --stdio "npx -y @kevinwatt/yt-dlp-mcp"

npx -y supergateway --port 8515 --stdio "env OBSIDIAN_API_KEY=447fc86b1ac3cbc5bd9f2f7429e6e9ce72d12bc46625df8377cadcb0666a6e71 'D:\My Drive\AIResearch\Obsidian\AI\.obsidian\plugins\mcp-tools\bin\mcp-server.exe'"


npx -y supergateway@latest --port 8517 --stdio "uv --directory /opt/app/cline/huggingface-mcp-server run huggingface"

npx -y supergateway@latest --port 8518 --stdio "npx -y @supabase/mcp-server-supabase@latest --access-token ********************************************"

npx -y supergateway@latest --port 8519 --stdio "npx -y @upstash/context7-mcp@latest"

npx -y supergateway@latest --port 8520 --stdio "node /opt/app/cline/mcp-server-sequential-thinking/dist/index.js"


```
<span style="background:#40a9ff">npx -y supergateway@latest --port 8515 --stdio "npx -y @kevinwatt/yt-dlp-mcp"</span>
<span style="background:#40a9ff">npx -y supergateway@latest --port 8519 --stdio "npx -y @upstash/context7-mcp@latest"</span>
<span style="background:#40a9ff">npx -y supergateway@latest --port 8520 --stdio "node /opt/app/cline/mcp-server-sequential-thinking/dist/index.js"</span>



## dart mcp

<span style="background:#40a9ff"> npx -y supergateway@latest --port 8516 --stdio "DART_TOKEN=dsa_6078e6e85fe9e37fdd88fece2b3f1a50993714996a0ca859758d07a775bb7da5 npx -y dart-mcp-server@latest"</span>

## supabase mcp

npx -y supergateway@latest --port 8518 --stdio "npx -y @supabase/mcp-server-supabase@latest --access-token ********************************************"



## ~~[Dont use] mcp-proxy for mcp~~
Dont use it not working
```
# mcp-proxy --sse-host=0.0.0.0 --sse-port=8511 -- /home/
# ray/.local/bin/uv --directory /opt/app/cline/search_server run search-server --transport stdio
```

## BrowserTools
https://browsertools.agentdesk.ai/installation
https://github.com/AgentDeskAI/browser-tools-mcp
git clone https://github.com/AgentDeskAI/browser-tools-mcp.git
~~mcp-proxy --sse-host=0.0.0.0 --sse-port=8512 -- /home/<USER>/.nvm/versions/node/v18.20.5/bin/browser-tools-mcp~~
## ~~mount google driver~~
```
rclone mount gdrive: /opt/app/cline/obsidian-vault --allow-non-empty
```
## manim
https://github.com/netcaster1/manim_mcp.git
<span style="background:#40a9ff">uv run mm_mcp</span>
will listen on 8522

## Interactive Feedback MCP

Looks can't run in ARM enviromment


## open project 
cd  /opt/app/cline/mcp-openproject-smithery
<span style="background:#40a9ff">npm start</span>
will listen on 8523

## claude code
<span style="background:#40a9ff">npx -y supergateway@latest --port 8525 --stdio "claude mcp serve"</span>


## Start Line-claude-bot
cd /opt/app/cline/claude-line-bot
node server.js
<span style="background:#40a9ff">It will listener on 8610 port</span>
start ngrok and update webhook
https://developers.line.biz/console/channel/2007601993/messaging-api

## ngrok

```
cat ngrok.yml
version: "3"
agent:
    authtoken: *************************************************
endpoints:
  - name: omni_server
    url: https://omni.ngrok.pro
    upstream:
      url: http://0.0.0.0:8511
#  - name: dart
#    url: https://dart.ngrok.pro
#    upstream:
#      url: http://0.0.0.0:8516
  - name: sequential-thinking
    url: https://thinker.ngrok.pro
    upstream:
      url: http://0.0.0.0:8520
tunnels:
  dart:
    addr: 8516
    proto: http
    host_header: ollama.jethome64
  manim:
    addr: 8522
    proto: http
    host_header: ollama.jethome64
  openproject:
    addr: 8523
    proto: http
    host_header: ollama.jethome64
  line-bot:
    addr: 8610
    proto: http
    host_header: ollama.jethome64
#  claudecode:
#    addr: 8525
#    proto: http
#    host_header: ollama.jethome64
#  omni_noauth:
#    addr: 8521
#    proto: http
#    host_header: ollama.jethome64
```

# Fix cursor codebase indexing issue 

[[Codebase Indexing No Longer Working Inside Dev Containers - Bug Report - Cursor Community Forum]]

create one script to do that automatically
**/opt/workspace/app/cursor/upgradefix.sh**
```
# find all base versions
cd ~/.cursor-server/cli/servers
ls -lrt
# get current issue version and previous version
~/.cursor-server/cli/servers/Stable-51c8aff7cb5a89f4a0e462fbacab938bdbfaf140/server/extensions/cursor-retrieval/node_modules/@anysphere/file-service-linux-arm64-gnu

~/.cursor-server/cli/servers/Stable-c499aee5f16e67815c7dc795ff338dc8ab3e07f0/server/extensions/cursor-retrieval/node_modules/@anysphere

#copy arm version to target directory
(base) ray@jethome:~/.cursor-server/cli/servers/Stable-c499aee5f16e67815c7dc795ff338dc8ab3e07f0/server/extensions/cursor-retrieval/node_modules/@anysphere$ rsync -avl ~/.cursor-server/cli/servers/Stable-51c8aff7cb5a89f4a0e462fbacab938bdbfaf140/server/extensions/cursor-retrieval/node_modules/@anysphere/file-service-linux-arm64-gnu/ ./file-service-linux-arm64-gnu/
sending incremental file list
created directory ./file-service-linux-arm64-gnu
./
file_service.linux-arm64-gnu.node
package.json

sent 6,486,524 bytes  received 110 bytes  12,973,268.00 bytes/sec
total size is 6,484,744  speedup is 1.00
(base) ray@jethome:~/.cursor-server/cli/servers/Stable-c499aee5f16e67815c7dc795ff338dc8ab3e07f0/server/extensions/cursor-retrieval/node_modules/@anysphere$ ls -lrt
total 12
drwxr-xr-x 2 <USER> <GROUP> 4096 Aug 30 12:49 file-service-linux-arm64-gnu
drwxr-xr-x 2 <USER> <GROUP> 4096 Oct 15 13:11 file-service-linux-x64-gnu
drwxr-xr-x 2 <USER> <GROUP> 4096 Oct 15 13:11 file-service

```

# Package upgrade
```shell
You can use the following command to hold the PostgreSQL packages at their current version:

sudo apt-mark hold postgresql-17 postgresql-client-17 postgresql postgresql-16 postgresql-client-16 postgresql-client-common postgresql-common postgresql-server-dev-16

Then you can run the upgrade again:
sudo apt upgrade

If you want to see which packages are currently on hold, you can use:
apt-mark showhold

Later, when you want to allow PostgreSQL updates again, you can remove the hold with:

sudo apt-mark unhold postgresql-17 postgresql-client-17 postgresql postgresql-16 postgresql-client-16 postgresql-client-common postgresql-common postgresql-server-dev-16

 Ubuntu (which uses APT package management) has similar configuration directories to yum.d/. The main configuration directories are:

/etc/apt/apt.conf.d/

Contains APT configuration files
Each file can contain specific settings


/etc/apt/sources.list.d/

Contains repository source files (similar to yum.repos.d/)
Files end with .list extension
Used to add additional repositories


/etc/apt/preferences.d/

Used for package pinning and priority settings
Can be used to prevent specific packages from upgrading



For your PostgreSQL case, you can create a file in /etc/apt/preferences.d/ to pin the PostgreSQL packages. Here's how:

sudo nano /etc/apt/preferences.d/postgresql

Package: postgresql*
Pin: version *
Pin-Priority: 100

Package: *postgresql*
Pin: version *
Pin-Priority: 100
This will prevent PostgreSQL-related packages from being automatically upgraded while allowing other updates. The lower priority (100) means these packages won't be automatically upgraded unless explicitly requested.
The main configuration file is /etc/apt/sources.list, but it's recommended to add custom repository configurations in /etc/apt/sources.list.d/ as separate .list files for better organization, just like in yum.d/.
After making these changes, run:
sudo apt update

```
# Additional
## wakeup on lan to remote sbi laptop
[[Windows10 Windows11 の Wake On LAN （WOL）設定]]
```
powercfg /getactivescheme

# 设置电源计划为高性能
powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c

# AC電源でのスリープまでの時間を「なし」に設定
powercfg /change /monitor-timeout-ac 0
powercfg /change /standby-timeout-ac 0
powercfg /change /hibernate-timeout-ac 0

# バッテリー駆動時のスリープまでの時間を「なし」に設定
powercfg /change /monitor-timeout-dc 0
powercfg /change /standby-timeout-dc 0
powercfg /change /hibernate-timeout-dc 0

# 使用GUID来设置睡眠时间为0（禁用）
powercfg /setacvalueindex 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c 238c9fa8-0aad-41ed-83f4-97be242c8f20 29f6c1db-86da-48c5-9fdb-f2b67b1f44da 0
powercfg /setdcvalueindex 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c 238c9fa8-0aad-41ed-83f4-97be242c8f20 29f6c1db-86da-48c5-9fdb-f2b67b1f44da 0

# 应用更改
powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c
```
```python


```

## Network IP setup

```
 Jetson's network configuration structure is slightly different. Since NetworkManager is likely being used as the primary network management tool, let's set up the static IP through NetworkManager.

First, let's check your current network interface name and connection:

(base) ray@jethome:/etc/network$ sudo nmcli connection show
NAME                UUID                                  TYPE      DEVICE
F660A-Lijd-A        22433b48-bdc1-4012-a831-2fad40f65402  wifi      wlan0
br-c46ca9109297     4867de7d-7984-415e-b989-34d4bff616d9  bridge    br-c46ca9109297
ztre4uxmsd          5070b0c6-6d71-47bd-ad12-1f358a8f9471  tun       ztre4uxmsd
br-81ec61e8bf65     94333b04-2cab-4954-94ed-6ff95ca6ee80  bridge    br-81ec61e8bf65
docker0             ************************************  bridge    docker0
Wired connection 1  ************************************  ethernet  --

Then set static IP using nmcli (replace values according to your network):
# Replace "Wired connection 1" with your connection name from the nmcli connection show command
sudo nmcli connection modify "Wired connection 1" \
ipv4.addresses "*************/24" \
ipv4.gateway "***********" \
ipv4.dns "*******,*******" \
ipv4.method manual

sudo nmcli connection down "Wired connection 1"
sudo nmcli connection up "Wired connection 1"

nmcli connection show "F660A-Lijd-A"

nmcli -p device show wlan0
sudo nmcli connection modify "F660A-Lijd-A" \
ipv4.addresses "192.168.1.xxx/24" \
ipv4.gateway "***********" \
ipv4.dns "*******,*******" \
ipv4.method manual

sudo nmcli connection down "F660A-Lijd-A"
sudo nmcli connection up "F660A-Lijd-A"


```

# /lib/systemd/systemd-resolved eating much CPU

```
vi /etc/systemd/resolved.conf

[Resolve]
DNS=******* *******
#FallbackDNS=
DNSSEC=no
DNSOverTLS=no
Domains=~.

sudo systemctl restart systemd-resolved
resolvectl status
```

# npx create-react-app frontend too slow

```
(base) ray@jethome:/opt/app/cursor/coder$ npx create-react-app frontend

Creating a new React app in /databank/app/cursor/coder/frontend.

Installing packages. This might take a couple of minutes.
Installing react, react-dom, and react-scripts with cra-template...

⠋
```

You can use below comand and it is faster
```

npx create-next-app@latest frontend
```



# Realtimes carrier board Setup

## installation software download
```
https://www.realtimes.cn/cn/software.html
passwd: realtimes2022

cd /databank/install
$ tar –vxf Jetson_Linux_R<version-i>_aarch64.tbz2 
生成文件夹目录 Linux_for_Tegra

1、进入 Linux Driver Package 的根文件系统目录
$ cd <your_L4T_root>/Linux_for_Tegra/rootfs 

2、解压 the Root File System 运行：
$ sudo tar -jxpf ../../Tegra-Linux-Sample-Root-Filesystem_R<version-i>_aarch64.tbz2

1、将 Realtimes-L4T-<version>.tar 包解压到与 Linux_for_Tegra 文件夹同级目录下面，使用命令：
$ tar -xvf Realtimes-L4T-<version>.tar

2、进入到 Realtimes-L4T 文件夹，运行
$ sudo ./install.sh
安装成功，会有 success 提示！

Enter recovery mode (p10 connect to usb + press recovery button + poweron)
cd /databank/install/Linux_for_Tegra
$ sudo ./flash.sh rtso-2005 mmcblk0p1

after compress, it will automatically reboot
then setup ip/keyboard/language/timezone 
then from ubuntu vm host and startup sdkmanager, install
from usb.

```

## post installation
```
1, create databank
1. move /tmp to /databank/tmp
2. install miniconda and install
3. maybe it is not required sudo apt-get install git-lfs
   git lfs --version
4. install go
   



```
### jtop installation hang

https://rnext.it/jetson_stats/troubleshooting.html
https://rnext.it/jetson_stats/contributing.html
https://github.com/rbonghi/jetson_stats/issues/525



journalctl -u jtop.service -n 100 --no-pager
sudo pip3 install --no-cache-dir -v -U jetson-stats
modify jtop.service and remvoe multi-user.target and then systemctl daemon-reload.
systemctl start jtop

```
[Unit]
Description=jtop service
#After=multi-user.target

[Service]
# Type=exec
Environment="JTOP_SERVICE=True"
ExecStart=/usr/local/bin/jtop --force
Restart=on-failure
RestartSec=10s
TimeoutStartSec=30s
TimeoutStopSec=30s

[Install]
WantedBy=multi-user.target
```


### Download
https://developer.download.nvidia.cn/compute/redist/jp/v61/pytorch/
https://forums.developer.nvidia.com/t/pytorch-for-jetson/72048
https://docs.nvidia.com/deeplearning/frameworks/install-pytorch-jetson-platform/index.html


🦋🦋 Below site is useful 🦋🦋
https://pypi.jetson-ai-lab.dev/jp6/cu126

### install Go
```
sudo wget https://go.dev/dl/go1.23.3.linux-arm64.tar.gz

```
### install pytorch 2.5 and jtop
```


sudo apt-get install -y  python3-pip libopenblas-dev

#download 
https://developer.nvidia.com/cusparselt-downloads?target_os=Linux&target_arch=aarch64-jetson&Compilation=Native&Distribution=Ubuntu&target_version=22.04&target_type=deb_local

sudo dpkg -i cusparselt-local-tegra-repo-ubuntu2204-0.6.3_1.0-1_arm64.deb
sudo cp /var/cusparselt-local-tegra-repo-ubuntu2204-0.6.3/cusparselt-*-keyring.gpg /usr/share/keyrings/
sudo apt-get update
sudo apt-get -y install libcusparselt0 libcusparselt-dev

pip install numpy==1.24.3

then install jetson v61 pytorch
conda activate mlearn
pip install -U torch-2.5.0a0+872d972e41.nv24.08.17622132-cp310-cp310-linux_aarch64.whl


# uninstall jtop
https://rnext.it/jetson_stats/jtop/jtop.html#uninstall
jtop --restore
systemctl stop jtop.service
systemctl disable jtop.service
rm /etc/systemd/system/jtop.service
systemctl daemon-reload
rm /run/jtop.sock
rm /etc/profile.d/jtop_env.sh
pip3 uninstall jetson-stats

# install jetson-stats again
sudo pip3 install -U jetson-stats

#if not able to find gpu
restart jtop and enter mlearn an jtop
```

### stop pps_ts_to_spe output

```
sudo mkdir -p /etc/systemd/system/pps_ts2spe.service.d/
sudo vi /etc/systemd/system/pps_ts2spe.service.d/override.conf
[Service]
StandardOutput=null
StandardError=journal

sudo systemctl daemon-reload
sudo systemctl restart pps_ts2spe.service

journalctl -f | grep pps_ts_to_spe
```
### setup MESA library
[13:59:15]ray@jethome64:/etc/ld.so.conf.d$ gputest
libEGL warning: MESA-LOADER: failed to open swrast: /usr/lib/dri/swrast_dri.so: cannot open shared object file: No such file or directory (search paths /usr/lib/aarch64-linux-gnu/dri:\$${ORIGIN}/dri:/usr/lib/dri, suffix _dri)

nvbufsurftransform: Could not get EGL display connection

```

sudo nano /etc/ld.so.conf.d/cuda.conf
Add these lines (adjust paths according to your CUDA installation):
/usr/local/cuda/lib64
/usr/local/cuda/extras/CUPTI/lib64

sudo ldconfig

sudo apt-get install -y \
    libgl1-mesa-dri \
    libgl1-mesa-glx \
    mesa-utils
sudo apt-get install libegl1    
sudo mkdir -p /usr/lib/dri
sudo ln -s /usr/lib/aarch64-linux-gnu/dri/swrast_dri.so /usr/lib/dri/swrast_dri.so
sudo ldconfig

```
libEGL warning: MESA-LOADER: failed to open swrast: /databank/workspace/miniconda3/envs/mlearn/bin/../lib/libstdc++.so.6: version `GLIBCXX_3.4.30' not found 

(GLIBXxxx issue)[# Fix GLIBCXX_3.4.30 not find issue]



### build torchaudio and torchvision for v6.1 cnn9
```
pip install cmake ninja
sudo apt install ffmpeg libavformat-dev libavcodec-dev libavutil-dev libavdevice-dev libavfilter-dev
git clone https://github.com/pytorch/audio
pip uninstall torchaudio
https://pytorch.org/audio/main/build.html

pip install cmake ninja
sudo apt install ffmpeg libavformat-dev libavcodec-dev libavutil-dev libavdevice-dev libavfilter-dev

cd audio
USE_CUDA=1 pip install -v -e . --no-use-pep517

vi pyproject.toml
[build-system]
requires = [
    "setuptools",
    "wheel",
    "torch>=2.5.0",
    "ninja",
    "numpy>=1.24.3",
    "cmake"
]


# Method 1: Using python -m build
export PYTHONPATH=$PYTHONPATH:/opt/install/jetpack6/v61/audio
USE_CUDA=1 TORCH_CUDA_ARCH_LIST="8.7" pip wheel . --no-deps --no-build-isolation

# Or Method 2: Using setup.py directly
USE_CUDA=1 TORCH_CUDA_ARCH_LIST="8.7" python setup.py bdist_wheel

import torchaudio

print(torchaudio.__version__)
torchaudio.utils.ffmpeg_utils.get_build_config()



--------------------------------------------------------------------------
https://github.com/pytorch/vision
https://download.pytorch.org/whl/nightly/torchvision/
[https://github.com/pytorch/vision/blob/main/CONTRIBUTING.md#development-installation]

https://pytorch.org/audio/main/build.jetson.html


wget https://github.com/pytorch/vision/archive/refs/tags/v0.20.0.tar.gz
python setup.py install
pip uninstall torchvision
(mlearn) ray@jethome64:/opt/install/jetpack6/v61/vision-0.20.0/dist$ pip list|grep torch
torch               2.5.0a0+872d972e41.nv24.8
torchaudio          2.3.0+952ea74
torchvision         0.20.0a0
```

#### build a wheel
```
pip install wheel build
cd /opt/install/jetpack6/v61/vision-0.20.0
python -m build --wheel
pip install dist/*.whl

```
To avoid torchvision imcompatible, pls install all dependecy
```
pip install \
  pillow==11.1.0 \
  torch==2.5.0a0+872d972e41.nv24.8 \
  numpy==1.26.3 \
  sympy==1.13.1 \
  fsspec==2024.12.0 \
  jinja2==3.1.5 \
  networkx==3.4.2 \
  typing-extensions==4.12.2 \
  filelock==3.16.1 \
  mpmath==1.3.0 \
  MarkupSafe==3.0.2

```

#### issue
```
# libEGL issue
# Suppress EGL warnings
os.environ['EGL_PLATFORM'] = 'device'
os.environ['TORCH_ALLOW_TF32_CUBLAS_OVERRIDE'] = '1'

# torch._C issue No module named 'torch._C._distributed_c10d'; 'torch._C' is not a package
sudo time env MACOSX_DEPLOYMENT_TARGET=14.1 CC=clang CXX=clang++ BUILD_CAFFE2_OPS=0 USE_CUDA=0 USE_MKLDNN=0 USE_DISTRIBUTED=1 python3 setup.py develop


```
<span style="background:#40a9ff">### uv installation</span>
```
uv add /databank/install/jetpack6/v61/offcial/torch-2.8.0-cp310-cp310-linux_aarch64.whl
uv add /databank/install/jetpack6/v61/offcial/torchvision-0.23.0-cp310-cp310-linux_aarch64.whl
uv add /databank/install/jetpack6/v61/offcial/torchaudio-2.8.0-cp310-cp310-linux_aarch64.whl
uv add /databank/install/jetpack6/v61/offcial/opencv_python-4.11.0-py3-none-any.whl
uv add /databank/install/jetpack6/v61/offcial/opencv_contrib_python-4.11.0.86-cp310-cp310-linux_aarch64.whl
uv add /databank/install/flash-attention/dist/flash_attn-2.6.3-cp310-cp310-linux_aarch64.whl
uv add /databank/install/jetpack6/v61/offcial/triton-3.4.0-cp310-cp310-linux_aarch64.whl 
uv add /databank/install/jetpack6/v61/offcial/vllm-0.8.6+cu126-cp310-cp310-linux_aarch64.whl

Will have issue when run pytorch , numpy issue
/opt/app/tools/readpal/pyproject.toml
 Summary

  The issue was a NumPy version incompatibility. The Jetpack 6 optimized PyTorch wheels were compiled against NumPy 1.x, but
   the environment had NumPy 2.2.6.

  Fixed by:
  1. Adding numpy<2 constraint to pyproject.toml
  2. Running uv sync --reinstall to downgrade NumPy to 1.26.4
  3. PyTorch now works correctly with CUDA support on the Orin device

```

# nvm installation

```

curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.0/install.sh | bash

nvm install 16.20.0    # Install specific version
nvm install --lts      # Install latest LTS version
nvm install node       # Install latest version
nvm use 16.20.0       # Use specific version
nvm use --lts         # Use latest LTS version
nvm ls                # List installed versions

echo "16.20.0" > .nvmrc    # Create .nvmrc file
nvm use                    # Use version from .nvmrc

setup nvm default enviromnment to avoid everytime i have to run 'nvm use'
nvm alias default v18.20.5


```

## npm slow issue

```
nvm list
npm config set prefer-offline true
npm install --prefer-offline
Or use

# 安装所有依赖（类似 npm install）
pnpm install

# 安装特定包
pnpm add package-name          # 安装到 dependencies
pnpm add -D package-name      # 安装到 devDependencies
pnpm add -g package-name      # 全局安装

# 移除包
pnpm remove package-name

# 更新包
pnpm update
pnpm update package-name

# 运行脚本（类似 npm run）
pnpm run dev
pnpm run build
# 或者简写
pnpm dev
pnpm build

# 创建新项目
mkdir my-project
cd my-project

# 初始化项目
pnpm init

# 安装开发依赖
pnpm add -D typescript @types/node

# 安装运行时依赖
pnpm add express react react-dom

# 查看依赖列表
pnpm list
```

## npm latest package installation issue
```
npm cache clean --force
npm install -g @anthropic-ai/claude-code
npm list -g

```
# Setup Nginx
```
sudo apt install nginx
sudo openssl req -x509 -nodes -days 99999 \
  -newkey rsa:2048 \
  -keyout /etc/ssl/private/nginx-selfsigned.key \
  -out /etc/ssl/certs/nginx-selfsigned.crt
Country Name (2 letter code) [AU]:JP
State or Province Name (full name) [Some-State]:Chiba
Locality Name (eg, city) []:Chiba
Organization Name (eg, company) [Internet Widgits Pty Ltd]:TianDaoChai
Organizational Unit Name (eg, section) []:
Common Name (e.g. server FQDN or YOUR name) []:jethome64
Email Address []:<EMAIL>

vi /etc/nginx/sites-available/default
# HTTPS server block
server {
    listen 8506 ssl;
    # If you have no domain, use the server's IP or "localhost" here:
    server_name ************;

    # Point Nginx to the self-signed certificate and key you just generated
    ssl_certificate     /etc/ssl/certs/nginx-selfsigned.crt;
    ssl_certificate_key /etc/ssl/private/nginx-selfsigned.key;

    # (Optional) Some recommended SSL settings:
    ssl_protocols       TLSv1.2 TLSv1.3;
    ssl_ciphers         HIGH:!aNULL:!MD5;

    location / {
        proxy_pass http://127.0.0.1:8505;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        # proxy_set_header Host $host;
        # Pass the exact client-supplied Host header (which includes :8506)
        proxy_set_header Host $http_host;
        proxy_set_header X-Forwarded-Host $http_host;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Optionally redirect HTTP to HTTPS:
#server {
#    listen 80;
#    server_name ************;
#    return 301 https://$host$request_uri;
#}

sudo systemctl reload nginx

https://************:8506/
```

---
# Setup Google Driver and mount to OS

https://rclone.org/drive/#making-your-own-client-id
#install 
curl https://rclone.org/install.sh | sudo bash
rclone version
rclone config (dont setup advanced setting and )

```python
[19:11:58]ray@jethome64:/opt/app/cline$ rclone config
Current remotes:

Name                 Type
====                 ====
gdrive               drive

e) Edit existing remote
n) New remote
d) Delete remote
r) Rename remote
c) Copy remote
s) Set configuration password
q) Quit config
e/n/d/r/c/s/q> e

Select remote.
Choose a number from below, or type in an existing value.
 1 > gdrive
remote> 1

Editing existing "gdrive" remote with options:
- type: drive
- client_id: *********************************************.apps.googleusercontent.com
- client_secret: 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
- scope: drive

Option client_id.
Google Application Client Id
Setting your own is recommended.
See https://rclone.org/drive/#making-your-own-client-id for how to create your own.
If you leave this blank, it will use an internal key which is low performance.
Enter a value of type string. Press Enter for the default (*********************************************.apps.googleusercontent.com).
client_id>

Option client_secret.
OAuth Client Secret.
Leave blank normally.
Enter a value of type string. Press Enter for the default (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).
client_secret>

Option scope.
Comma separated list of scopes that rclone should use when requesting access from drive.
Choose a number from below, or type in your own value of type string.
Press Enter for the default (drive).
 1 / Full access all files, excluding Application Data Folder.
   \ (drive)
 2 / Read-only access to file metadata and file contents.
   \ (drive.readonly)
   / Access to files created by rclone only.
 3 | These are visible in the drive website.
   | File authorization is revoked when the user deauthorizes the app.
   \ (drive.file)
   / Allows read and write access to the Application Data folder.
 4 | This is not visible in the drive website.
   \ (drive.appfolder)
   / Allows read-only access to file metadata but
 5 | does not allow any access to read or download file content.
   \ (drive.metadata.readonly)
scope> 1

Option service_account_file.
Service Account Credentials JSON file path.
Leave blank normally.
Needed only if you want use SA instead of interactive login.
Leading `~` will be expanded in the file name as will environment variables such as `${RCLONE_CONFIG_DIR}`.
Enter a value. Press Enter to leave empty.
service_account_file>

Edit advanced config?
y) Yes
n) No (default)
y/n>

Use web browser to automatically authenticate rclone with remote?
 * Say Y if the machine running rclone has a web browser you can use
 * Say N if running rclone on a (remote) machine without web browser access
If not sure try Y. If Y failed, try N.

y) Yes (default)
n) No
y/n> n

Option config_token.
For this to work, you will need rclone available on a machine that has
a web browser available.
For more help and alternate methods see: https://rclone.org/remote_setup/
Execute the following on the machine with the web browser (same rclone
version recommended):
        rclone authorize "drive" "*******************************************************************************************************************************************************************************************************************"
Then paste the result.
Enter a value.
config_token> **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

Configure this as a Shared Drive (Team Drive)?

y) Yes
n) No (default)
y/n>

Configuration complete.
Options:
- type: drive
- client_id: *********************************************.apps.googleusercontent.com
- client_secret: 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
- scope: drive
- token: {"access_token":"******************************************************************************************************************************************************************************************************************************","token_type":"Bearer","refresh_token":"1//0edw1OC8lhVH2CgYIARAAGA4SNwF-L9IrVbxVe0PhAW2kH9gsHPzrP42Upyubscfqr-N8a2gvSZOOvq1Cw4xdUXFrWcPHcmHHdAI","expiry":"2025-02-21T20:13:46.539257404+09:00"}
- team_drive:
Keep this "gdrive" remote?
y) Yes this is OK (default)
e) Edit this remote
d) Delete this remote
y/e/d>

Current remotes:

Name                 Type
====                 ====
gdrive               drive


```

rclone lsd gdrive:

## Mount manually
rclone mount gdrive: /opt/app/cline/obsidian-vault --allow-non-empty


## Mount as service
1. Enable fuser mount disk with ray user
sudo nano /etc/fuse.conf
user_allow_other


2. create service 
```python
[19:47:23]ray@jethome64:~$ cat /etc/systemd/system/rclone-gdrive.service
[Unit]
Description=Rclone Mount for Google Drive
After=network-online.target

[Service]
Type=simple
ExecStart=/usr/bin/rclone mount gdrive: /opt/app/cline/obsidian-vault \
    --config /home/<USER>/.config/rclone/rclone.conf \
    --allow-other \
    --vfs-cache-mode writes
ExecStop=/bin/fusermount -u /opt/app/cline/obsidian-vault
Restart=on-failure
User=ray
Group=ray

[Install]
WantedBy=multi-user.target

```
---
# Setup One Driver and mount to OS

## setup

rclone config
need to use wsl server to open browser and generate token

## manually mount
```
[16:04:02]ray@jethome64:~$ cat mountonedriver.sh
rclone --vfs-cache-mode writes mount onedrive:search_server ~/.search_server &
```
## manually unmount
```
-- unmount
fusermount -u /home/<USER>/.search_server
```
## mount as service (user level systemd)
### create service file under .config/systemd/user
```
[16:12:17]ray@jethome64:~/.config/systemd/user$ cat rclone-onedrive.service
[Unit]
Description=OneDrive (rclone)
After=network.target

[Service]
Type=simple
ExecStart=/usr/bin/rclone mount onedrive:search_server %h/.search_server --vfs-cache-mode writes
Restart=on-abort

[Install]
WantedBy=default.target

```

systemctl --user daemon-reload
systemctl --user restart rclone-onedrive.service

```
systemctl --user enable rclone-onedrive.service
systemctl --user start rclone-onedrive.service
```




---

# 如何Clone 一个conda 环境到另一个

除了rsync -avl /home/<USER>/miniconda3/envs/xxxx/ /home/<USER>/miniconda3/envs/yyyy/
修改pip file，不然会安装到旧的环境下
```
vi /home/<USER>/miniconda3/envs/sovit/bin/pip
#!/home/<USER>/miniconda3/envs/sovit/bin/python3.10
```
---
# Disable snape service

```
# Stop the service
sudo systemctl stop snapd.service snapd.socket snapd.seeded.service

# Disable it from starting at boot
sudo systemctl disable snapd.service snapd.socket snapd.seeded.service

# Mask the services to prevent them from being started automatically
sudo systemctl mask snapd.service snapd.socket snapd.seeded.service
```
---

# Docker installation
https://claude.ai/share/************************************
```
sudo apt-get update
sudo apt-get install -y \
    ca-certificates \
    curl \
    gnupg \
    lsb-release
# Create the keyrings directory
sudo install -m 0755 -d /etc/apt/keyrings

# Add Docker's official GPG key
sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
sudo chmod a+r /etc/apt/keyrings/docker.asc

# Add the repository to Apt sources
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
  $(. /etc/os-release && echo "${UBUNTU_CODENAME:-$VERSION_CODENAME}") stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Update the package database
sudo apt-get update

sudo apt-get install -y \
    docker-ce \
    docker-ce-cli \
    containerd.io \
    docker-buildx-plugin \
    docker-compose-plugin

# Add current user to docker group
sudo usermod -aG docker ${USER}

# Apply the new group membership (you'll need to log out and back in for full effect)
newgrp docker

# Enable Docker service
sudo systemctl enable docker.service
sudo systemctl enable containerd.service

# Start Docker service (if not already running)
sudo systemctl start docker.service

# Check Docker version
docker --version

# Run hello-world container (without sudo!)
docker run hello-world
# Check Docker Compose version
docker compose version

# Create Docker daemon configuration
sudo tee /etc/docker/daemon.json <<EOF
{
  "data-root": "/path/to/new/docker/root"
}
EOF

# Restart Docker
sudo systemctl restart docker


# Limit log file sizes
sudo tee /etc/docker/daemon.json <<EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
EOF

# Restart Docker
sudo systemctl restart docker




```


---



![[image0 (1).jpg]]
